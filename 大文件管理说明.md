# 🗂️ 大文件管理工具套件

一套完整的大文件管理工具，支持移动、恢复和管理大文件，同时保护Git仓库完整性。

## 📋 功能特性

### ✅ **核心功能**
- 🔍 **智能查找**: 查找大于指定大小的文件
- 📦 **安全移动**: 移动大文件到备份目录
- 🔄 **完整恢复**: 支持交互式和强制恢复
- 📊 **详细日志**: 记录所有移动操作
- 🚫 **智能排除**: 自动排除Git仓库和开发目录

### 🛡️ **安全保护**
- 排除所有层级的 `.git` 目录
- 排除 `node_modules`, `.cache` 等开发目录
- 完整的操作日志记录
- 交互式确认机制

## 📁 文件说明

| 文件 | 功能 | 用途 |
|------|------|------|
| `move_large_files.sh` | 主移动脚本 | 查找并移动大文件 |
| `restore_large_files.sh` | 恢复脚本 | 恢复移动的文件 |
| `large_files_manager.sh` | 统一管理器 | 图形化菜单管理 |
| `test_move_large_files.sh` | 测试脚本 | 测试排除功能 |

## 🚀 快速开始

### 1. 基本使用

```bash
# 移动大文件
./move_large_files.sh

# 查看可恢复的文件
./restore_large_files.sh --list

# 恢复文件
./restore_large_files.sh

# 使用图形化管理器
./large_files_manager.sh
```

### 2. 高级选项

```bash
# 恢复脚本选项
./restore_large_files.sh --help     # 显示帮助
./restore_large_files.sh --list     # 仅列出文件
./restore_large_files.sh --force    # 强制恢复
./restore_large_files.sh --dir DIR  # 指定备份目录
```

## 📊 使用流程

### 移动大文件
```
1. 运行 ./move_large_files.sh
2. 脚本自动查找 >50M 的文件
3. 排除 .git, node_modules 等目录
4. 移动文件到 large_files_backup/
5. 生成操作日志
```

### 恢复文件
```
1. 运行 ./restore_large_files.sh
2. 选择要恢复的文件
3. 确认覆盖已存在的文件
4. 恢复到原始位置
5. 可选择删除空的备份目录
```

## 🔧 配置选项

### move_large_files.sh 配置
```bash
SIZE_LIMIT="50M"              # 文件大小限制
BACKUP_DIR="large_files_backup"  # 备份目录名
```

### 排除目录列表
- `*/.git/*` - 所有Git仓库文件
- `*/node_modules/*` - Node.js依赖
- `*/.cache/*` - 缓存文件
- `*/.npm/*`, `*/.yarn/*` - 包管理器缓存
- `*/build/*`, `*/dist/*` - 构建输出
- `*/large_files_backup/*` - 备份目录本身

## 📈 输出示例

### 移动操作
```
🔍 开始查找并移动大于50M的文件...
📁 备份目录: large_files_backup
🚫 排除目录: 所有子目录下的 .git, node_modules, .cache 等
==================================
📄 处理文件: ./video.mp4 (大小: 120M)
📦 移动: ./video.mp4 -> large_files_backup/./video.mp4
✅ 成功移动: ./video.mp4
---
🎉 大文件移动完成！
📊 统计信息:
   - 成功移动: 1 个文件
   - 移动失败: 0 个文件
```

### 恢复操作
```
🔄 开始恢复大文件...
📁 备份目录: large_files_backup
==================================
📄 恢复文件: ./video.mp4 (大小: 120M)
✅ 成功恢复: ./video.mp4
---
🎉 文件恢复完成！
📊 统计信息:
   - 成功恢复: 1 个文件
   - 恢复失败: 0 个文件
```

## 📋 日志格式

移动日志保存在 `large_files_backup/move_log.txt`:
```
# 大文件移动日志 - 2024-01-15 10:30:00
# 格式: 时间戳|操作|源文件|目标文件|状态
2024-01-15 10:30:15|MOVE|./video.mp4|large_files_backup/./video.mp4|SUCCESS
2024-01-15 10:30:20|MOVE|./data.zip|large_files_backup/./data.zip|SUCCESS
```

## 🧪 测试功能

```bash
# 运行测试脚本
./test_move_large_files.sh

# 测试会创建模拟的大文件和目录结构
# 验证排除功能是否正常工作
```

## ⚠️ 注意事项

1. **备份重要**: 移动前确保重要文件已备份
2. **磁盘空间**: 确保有足够空间存储备份
3. **权限问题**: 某些系统文件可能需要特殊权限
4. **Git安全**: 所有Git仓库文件都会被自动排除
5. **路径长度**: 避免过长的文件路径

## 🛠️ 故障排除

### 常见问题

**Q: 移动失败怎么办？**
A: 检查文件权限和磁盘空间，查看日志文件了解详情

**Q: 如何修改文件大小限制？**
A: 编辑 `move_large_files.sh` 中的 `SIZE_LIMIT` 变量

**Q: 恢复时提示文件已存在？**
A: 使用 `--force` 选项强制覆盖，或手动处理冲突

**Q: 如何完全清理？**
A: 使用管理器的清理功能或手动删除 `large_files_backup` 目录

## 📞 支持

如果遇到问题：
1. 查看日志文件 `large_files_backup/move_log.txt`
2. 运行测试脚本验证功能
3. 检查文件权限和磁盘空间
4. 使用 `--help` 选项查看详细帮助

---

**🎯 现在您可以安全地管理大文件，同时保护Git仓库的完整性！**
