# 本地仓库同步系统安装指南

## 系统要求

### 操作系统
- Ubuntu 18.04+ / Debian 10+
- CentOS 7+ / RHEL 7+
- macOS 10.14+
- Windows 10+ (WSL2)

### 必需软件
- Bash 4.0+
- Git 2.0+
- jq 1.5+
- rsync 3.0+

### 可选软件
- inotify-tools (用于实时文件监控)
- cron (用于定时同步)

## 快速安装

### 1. 安装依赖

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install -y git jq rsync inotify-tools
```

#### CentOS/RHEL
```bash
sudo yum install -y git jq rsync inotify-tools
# 或者 (CentOS 8+)
sudo dnf install -y git jq rsync inotify-tools
```

#### macOS
```bash
# 使用 Homebrew
brew install git jq rsync

# 安装文件监控工具 (可选)
brew install fswatch
```

#### Windows (WSL2)
```bash
# 在WSL2中运行
sudo apt-get update
sudo apt-get install -y git jq rsync inotify-tools
```

### 2. 下载同步系统

```bash
# 进入xiaoli_application_ros2目录
cd /path/to/xiaoli_application_ros2

# 检查scripts/sync目录
ls scripts/sync/
```

应该看到以下文件：
- `sync-manager.sh` - 主管理工具
- `sync-core.sh` - 核心同步引擎
- `sync-monitor.sh` - 监控系统
- `sync-config.json` - 默认配置文件
- `test-sync.sh` - 测试脚本

### 3. 初始化系统

```bash
cd xiaoli_application_ros2/scripts/sync

# 初始化同步系统
./sync-manager.sh init
```

### 4. 验证安装

```bash
# 检查系统状态
./sync-manager.sh check

# 运行测试套件
./test-sync.sh
```

## 详细安装步骤

### 步骤1: 环境检查

```bash
# 检查Bash版本
bash --version

# 检查Git版本
git --version

# 检查jq版本
jq --version

# 检查rsync版本
rsync --version
```

### 步骤2: 权限设置

```bash
# 确保脚本有执行权限
chmod +x xiaoli_application_ros2/scripts/sync/*.sh

# 检查目录权限
ls -la xiaoli_application_ros2/scripts/sync/
```

### 步骤3: 配置验证

```bash
# 验证配置文件格式
jq '.' xiaoli_application_ros2/scripts/sync/sync-config.json

# 检查日志目录
mkdir -p xiaoli_application_ros2/scripts/sync/logs
mkdir -p xiaoli_application_ros2/scripts/sync/backups
mkdir -p xiaoli_application_ros2/scripts/sync/tmp
```

## 可选组件安装

### 文件监控工具

#### Linux (inotify-tools)
```bash
# 测试inotify是否可用
echo "test" > /tmp/test_file
inotifywait -m /tmp/ &
WATCH_PID=$!
echo "modified" >> /tmp/test_file
kill $WATCH_PID
rm /tmp/test_file
```

#### macOS (fswatch)
```bash
# 安装fswatch
brew install fswatch

# 测试fswatch
fswatch -o /tmp/ &
WATCH_PID=$!
touch /tmp/test_file
kill $WATCH_PID
rm /tmp/test_file
```

### 定时任务设置

```bash
# 编辑crontab
crontab -e

# 添加定时同步任务 (每30分钟)
*/30 * * * * /path/to/xiaoli_application_ros2/scripts/sync/sync-manager.sh sync all >/dev/null 2>&1
```

## 配置优化

### 1. 性能优化

编辑 `sync-config.json`:

```json
{
  "performance": {
    "parallel_transfers": true,
    "max_parallel_files": 10,
    "compression_enabled": true,
    "cache_size_mb": 256
  }
}
```

### 2. 日志配置

```json
{
  "global": {
    "log_level": "INFO",
    "log_retention_days": 30,
    "log_max_size_mb": 100
  }
}
```

### 3. 安全设置

```json
{
  "security": {
    "require_confirmation": true,
    "max_file_size_mb": 1000,
    "check_permissions": true,
    "denied_paths": [
      "/etc/**",
      "/sys/**",
      "/proc/**"
    ]
  }
}
```

## 故障排除

### 常见问题

#### 1. 权限错误
```bash
# 问题: Permission denied
# 解决: 检查文件和目录权限
chmod +x sync-manager.sh
chmod -R 755 xiaoli_application_ros2/scripts/sync/
```

#### 2. 依赖缺失
```bash
# 问题: command not found: jq
# 解决: 安装缺失的依赖
sudo apt-get install jq
```

#### 3. 配置文件损坏
```bash
# 问题: parse error in config file
# 解决: 重新初始化配置
cp sync-config.json sync-config.json.backup
./sync-manager.sh init
```

#### 4. 磁盘空间不足
```bash
# 检查磁盘空间
df -h

# 清理旧日志
./sync-manager.sh logs clean 7
```

### 调试模式

```bash
# 启用详细日志
./sync-manager.sh -v sync all

# 启用调试模式
DEBUG=true ./sync-manager.sh sync all

# 查看实时日志
tail -f logs/sync-$(date +%Y%m%d).log
```

### 性能诊断

```bash
# 运行性能测试
./test-sync.sh performance

# 查看资源使用
top -p $(pgrep -f sync-manager)

# 网络延迟测试 (如果涉及远程同步)
ping target_host
```

## 升级指南

### 版本检查
```bash
./sync-manager.sh version
```

### 备份配置
```bash
./sync-manager.sh backup
```

### 更新系统
```bash
# 备份当前配置
./sync-manager.sh backup

# 下载新版本文件
# (替换脚本文件)

# 验证新版本
./sync-manager.sh check

# 恢复配置 (如需要)
./sync-manager.sh restore backup_file
```

## 卸载指南

### 完全卸载
```bash
# 停止所有监控进程
./sync-monitor.sh stop

# 清理cron任务
crontab -e
# (手动删除相关条目)

# 备份重要数据
./sync-manager.sh backup

# 删除系统文件
rm -rf xiaoli_application_ros2/scripts/sync/
```

### 保留配置的卸载
```bash
# 只删除执行文件，保留配置和日志
rm xiaoli_application_ros2/scripts/sync/*.sh
```

## 技术支持

### 日志位置
- 同步日志: `logs/sync-YYYYMMDD.log`
- 监控日志: `logs/monitor-YYYYMMDD.log`
- 测试日志: `logs/test-YYYYMMDD_HHMMSS.log`

### 配置文件
- 主配置: `sync-config.json`
- 状态文件: `tmp/monitor_status.json`
- PID文件: `pids/*.pid`

### 获取帮助
```bash
# 显示帮助信息
./sync-manager.sh help

# 显示监控帮助
./sync-monitor.sh help

# 显示测试帮助
./test-sync.sh help
```

### 问题报告

创建问题报告时请包含：

1. 系统信息
```bash
uname -a
bash --version
./sync-manager.sh version
```

2. 错误日志
```bash
tail -50 logs/sync-$(date +%Y%m%d).log
```

3. 配置信息
```bash
./sync-manager.sh config show
```

4. 系统状态
```bash
./sync-manager.sh check
```