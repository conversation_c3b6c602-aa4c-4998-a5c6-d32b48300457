# 本地仓库同步系统使用示例

## 基础示例

### 示例1: 简单的项目备份

**场景**: 将开发项目自动备份到另一个目录

```bash
# 1. 初始化系统
./sync-manager.sh init

# 2. 添加备份同步对
./sync-manager.sh add \
  /home/<USER>/xiaoli_application_ros2 \
  /backup/xiaoli_application_ros2 \
  unidirectional \
  source-wins

# 3. 启动实时监控
./sync-monitor.sh start

# 4. 测试同步
echo "test file" > /home/<USER>/xiaoli_application_ros2/test.txt
# 几秒后检查备份目录
ls /backup/xiaoli_application_ros2/
```

### 示例2: 双向开发环境同步

**场景**: 在工作电脑和笔记本之间同步代码

```bash
# 1. 在工作电脑上设置
./sync-manager.sh add \
  /work/xiaoli_project \
  /shared/xiaoli_project \
  bidirectional \
  manual

# 2. 在笔记本上设置相同的同步对
./sync-manager.sh add \
  /laptop/xiaoli_project \
  /shared/xiaoli_project \
  bidirectional \
  manual

# 3. 启动监控
./sync-monitor.sh start

# 4. 测试双向同步
# 在工作电脑修改文件
echo "work change" > /work/xiaoli_project/work_file.txt

# 在笔记本修改文件
echo "laptop change" > /laptop/xiaoli_project/laptop_file.txt

# 查看同步结果
./sync-manager.sh status
```

## 高级示例

### 示例3: 多仓库管理

**场景**: 同时管理多个项目的同步

```bash
# 1. 添加主项目同步
./sync-manager.sh add \
  /home/<USER>/xiaoli_application_ros2 \
  /backup/xiaoli_ros2 \
  unidirectional \
  source-wins

# 2. 添加文档项目同步
./sync-manager.sh add \
  /home/<USER>/documents \
  /backup/documents \
  mirror \
  source-wins

# 3. 添加配置文件同步
./sync-manager.sh add \
  /home/<USER>/.config \
  /backup/config \
  bidirectional \
  keep-both

# 4. 查看所有仓库
./sync-manager.sh list

# 5. 批量同步
./sync-manager.sh sync all
```

### 示例4: 带过滤规则的同步

**场景**: 只同步源代码，排除构建文件

```bash
# 1. 添加同步对
./sync-manager.sh add \
  /home/<USER>/xiaoli_application_ros2 \
  /backup/xiaoli_code_only

# 2. 编辑配置文件，添加过滤规则
cat > temp_config.json << 'EOF'
{
  "exclude_patterns": [
    "build/**",
    "install/**",
    "log/**",
    "devel/**",
    "*.pyc",
    "__pycache__/**",
    "node_modules/**",
    ".git/objects/**",
    "*.log",
    "*.tmp"
  ],
  "include_patterns": [
    "src/**",
    "launch/**",
    "config/**",
    "scripts/**",
    "CMakeLists.txt",
    "package.xml",
    "*.md",
    "*.yaml",
    "*.py",
    "*.cpp",
    "*.h"
  ]
}
EOF

# 3. 更新仓库配置
jq --argjson filters "$(cat temp_config.json)" \
  '(.repositories[] | select(.id == "xiaoli_application_ros2")) += $filters' \
  sync-config.json > sync-config.json.tmp && \
  mv sync-config.json.tmp sync-config.json

# 4. 执行同步
./sync-manager.sh sync xiaoli_application_ros2

# 5. 验证结果
ls -la /backup/xiaoli_code_only/
ls -la /backup/xiaoli_code_only/build/  # 应该为空或不存在
```

### 示例5: Git集成自动同步

**场景**: 每次Git提交后自动同步到备份

```bash
# 1. 设置同步对
./sync-manager.sh add \
  /home/<USER>/xiaoli_application_ros2 \
  /backup/xiaoli_git_backup

# 2. 启用Git集成
./sync-manager.sh config set git_integration.enabled=true
./sync-manager.sh config set git_integration.auto_commit=true
./sync-manager.sh config set git_integration.commit_message_template="Auto backup: {timestamp}"

# 3. 创建Git钩子
cd /home/<USER>/xiaoli_application_ros2
cat > .git/hooks/post-commit << 'EOF'
#!/bin/bash
# 提交后自动同步到备份
/path/to/sync-manager.sh sync xiaoli_application_ros2
EOF
chmod +x .git/hooks/post-commit

# 4. 测试Git集成
echo "test git integration" > test_git.txt
git add test_git.txt
git commit -m "Test git integration"

# 5. 检查备份目录
ls -la /backup/xiaoli_git_backup/
```

## 实际应用场景

### 场景A: ROS2开发环境备份

**背景**: 为xiaoli_application_ros2项目设置自动备份和多环境同步

```bash
#!/bin/bash
# setup_ros2_sync.sh - ROS2项目同步设置脚本

# 项目路径
ROS2_PROJECT="./projects/xiaoli_application_ros2"
BACKUP_DIR="./backups/ros2_projects"
REMOTE_DEV="./shared/ros2_dev"

# 1. 初始化同步系统
./sync-manager.sh init

# 2. 设置本地备份
./sync-manager.sh add "$ROS2_PROJECT" "$BACKUP_DIR/xiaoli_app" unidirectional source-wins

# 3. 配置备份排除规则
jq '(.repositories[] | select(.source == "'$ROS2_PROJECT'")) += {
  "exclude_patterns": [
    "build/**",
    "install/**",
    "log/**",
    "devel/**",
    ".vscode/settings.json",
    "*.pyc",
    "__pycache__/**"
  ]
}' sync-config.json > sync-config.json.tmp && mv sync-config.json.tmp sync-config.json

# 4. 设置开发环境同步
./sync-manager.sh add "$ROS2_PROJECT" "$REMOTE_DEV/xiaoli_app" bidirectional manual

# 5. 启用Git集成
./sync-manager.sh config set git_integration.enabled=true
./sync-manager.sh config set git_integration.pull_before_sync=true

# 6. 设置定时备份 (每小时)
(crontab -l 2>/dev/null; echo "0 * * * * $(pwd)/sync-manager.sh sync xiaoli_application_ros2") | crontab -

# 7. 启动监控
./sync-monitor.sh start

echo "ROS2项目同步设置完成!"
echo "本地备份: $BACKUP_DIR/xiaoli_app"
echo "开发同步: $REMOTE_DEV/xiaoli_app"
```

### 场景B: 多机器开发协作

**背景**: 团队成员在不同机器上协作开发

```bash
#!/bin/bash
# setup_team_sync.sh - 团队协作同步设置

TEAM_SHARE="/shared/team_projects"
LOCAL_PROJECT="/home/<USER>/xiaoli_application_ros2"

# 1. 检查共享目录
if [ ! -d "$TEAM_SHARE" ]; then
    echo "请先创建共享目录: $TEAM_SHARE"
    exit 1
fi

# 2. 设置双向同步
./sync-manager.sh add "$LOCAL_PROJECT" "$TEAM_SHARE/xiaoli_app" bidirectional manual

# 3. 配置冲突策略为手动解决
./sync-manager.sh config set repositories[0].conflict_resolution=manual

# 4. 启用文件变更通知
./sync-manager.sh config set notifications.enabled=true
./sync-manager.sh config set notifications.on_conflict=true

# 5. 创建同步脚本
cat > sync_with_team.sh << 'EOF'
#!/bin/bash
echo "开始团队同步..."

# 先拉取团队更改
./sync-manager.sh sync xiaoli_application_ros2

# 检查是否有冲突
if grep -q "CONFLICT" logs/sync-$(date +%Y%m%d).log; then
    echo "检测到冲突，请手动解决后重新运行"
    exit 1
fi

echo "团队同步完成"
EOF

chmod +x sync_with_team.sh

echo "团队协作同步设置完成!"
echo "使用 ./sync_with_team.sh 进行团队同步"
```

### 场景C: 持续集成环境

**背景**: 自动将开发代码同步到CI/CD环境

```bash
#!/bin/bash
# setup_ci_sync.sh - CI环境同步设置

DEV_PROJECT="/home/<USER>/xiaoli_application_ros2"
CI_DEPLOY="/var/ci/xiaoli_app"
STAGING_ENV="/var/staging/xiaoli_app"

# 1. 设置开发到CI的同步
./sync-manager.sh add "$DEV_PROJECT" "$CI_DEPLOY" unidirectional source-wins

# 2. 设置CI到预发布的同步
./sync-manager.sh add "$CI_DEPLOY" "$STAGING_ENV" mirror source-wins

# 3. 配置只同步必要文件
jq '(.repositories[] | select(.target == "'$CI_DEPLOY'")) += {
  "include_patterns": [
    "src/**",
    "launch/**",
    "config/**",
    "scripts/**",
    "CMakeLists.txt",
    "package.xml"
  ],
  "exclude_patterns": [
    ".git/**",
    "build/**",
    "install/**",
    "*.pyc",
    "__pycache__/**",
    ".vscode/**"
  ]
}' sync-config.json > sync-config.json.tmp && mv sync-config.json.tmp sync-config.json

# 4. 启用Git钩子触发
cat > $DEV_PROJECT/.git/hooks/post-receive << 'EOF'
#!/bin/bash
# 接收推送后自动部署到CI
echo "触发CI同步..."
/path/to/sync-manager.sh sync xiaoli_application_ros2
if [ $? -eq 0 ]; then
    echo "CI同步成功，开始构建测试..."
    # 这里可以添加构建和测试命令
    cd /var/ci/xiaoli_app
    colcon build
    colcon test
fi
EOF

chmod +x $DEV_PROJECT/.git/hooks/post-receive

echo "CI环境同步设置完成!"
```

## 性能优化示例

### 示例6: 大型项目优化配置

**场景**: 优化大型ROS2项目的同步性能

```bash
# 1. 启用性能优化配置
./sync-manager.sh config set performance.parallel_transfers=true
./sync-manager.sh config set performance.max_parallel_files=8
./sync-manager.sh config set performance.compression_enabled=true
./sync-manager.sh config set performance.cache_enabled=true
./sync-manager.sh config set performance.cache_size_mb=256

# 2. 优化监控配置
./sync-manager.sh config set monitoring.debounce_seconds=10
./sync-manager.sh config set monitoring.batch_size=50

# 3. 设置智能排除规则
cat > optimization_config.json << 'EOF'
{
  "exclude_patterns": [
    "build/**",
    "install/**",
    "log/**",
    "devel/**",
    ".git/objects/**",
    ".git/logs/**",
    "*.pyc",
    "__pycache__/**",
    "node_modules/**",
    "*.o",
    "*.so",
    "*.a",
    "core.*",
    "*.tmp",
    ".DS_Store",
    "Thumbs.db"
  ],
  "sync_settings": {
    "compress_transfer": true,
    "preserve_permissions": true,
    "preserve_timestamps": false
  }
}
EOF

# 4. 应用优化配置
jq --argjson opt "$(cat optimization_config.json)" \
  '(.repositories[] | select(.id == "xiaoli_application_ros2")) += $opt' \
  sync-config.json > sync-config.json.tmp && \
  mv sync-config.json.tmp sync-config.json

# 5. 运行性能测试
./test-sync.sh performance
```

## 监控和报警示例

### 示例7: 设置同步监控和报警

**场景**: 监控同步状态并在异常时发送报警

```bash
#!/bin/bash
# monitoring_setup.sh - 同步监控设置

# 1. 启用通知
./sync-manager.sh config set notifications.enabled=true
./sync-manager.sh config set notifications.on_error=true
./sync-manager.sh config set notifications.on_conflict=true

# 2. 创建健康检查脚本
cat > health_check.sh << 'EOF'
#!/bin/bash
# 同步系统健康检查

LOG_FILE="health_check.log"
ALERT_FILE="alerts.txt"

# 检查监控状态
if ! ./sync-monitor.sh status | grep -q "running"; then
    echo "$(date): 监控系统未运行" >> $ALERT_FILE
fi

# 检查同步错误
ERROR_COUNT=$(grep -c "ERROR" logs/sync-$(date +%Y%m%d).log 2>/dev/null || echo "0")
if [ "$ERROR_COUNT" -gt 5 ]; then
    echo "$(date): 发现 $ERROR_COUNT 个同步错误" >> $ALERT_FILE
fi

# 检查队列积压
QUEUE_SIZE=$(./sync-monitor.sh status | grep "队列大小" | awk '{print $2}')
if [ "$QUEUE_SIZE" -gt 100 ]; then
    echo "$(date): 同步队列积压 $QUEUE_SIZE 项" >> $ALERT_FILE
fi

# 发送报警邮件 (如果有报警)
if [ -s "$ALERT_FILE" ]; then
    mail -s "同步系统报警" <EMAIL> < $ALERT_FILE
    > $ALERT_FILE  # 清空报警文件
fi
EOF

chmod +x health_check.sh

# 3. 设置定期健康检查 (每15分钟)
(crontab -l 2>/dev/null; echo "*/15 * * * * $(pwd)/health_check.sh") | crontab -

echo "监控报警设置完成!"
```

## 故障恢复示例

### 示例8: 自动故障恢复

**场景**: 自动检测和恢复同步故障

```bash
#!/bin/bash
# auto_recovery.sh - 自动故障恢复脚本

recover_sync() {
    local repo_id="$1"
    echo "开始恢复同步: $repo_id"
    
    # 1. 停止监控
    ./sync-monitor.sh stop
    
    # 2. 清理同步队列
    ./sync-monitor.sh queue clear
    
    # 3. 检查磁盘空间
    if [ $(df / | tail -1 | awk '{print $5}' | sed 's/%//') -gt 90 ]; then
        echo "磁盘空间不足，清理日志..."
        ./sync-manager.sh logs clean 7
    fi
    
    # 4. 重置同步状态
    rm -f tmp/sync_lock_*
    rm -f pids/*.pid
    
    # 5. 重新启动监控
    ./sync-monitor.sh start
    
    # 6. 执行一次完整同步
    ./sync-manager.sh sync "$repo_id"
    
    echo "故障恢复完成"
}

# 检查是否需要恢复
FAILED_SYNCS=$(grep -c "同步失败" logs/sync-$(date +%Y%m%d).log 2>/dev/null || echo "0")
if [ "$FAILED_SYNCS" -gt 3 ]; then
    echo "检测到连续同步失败，开始自动恢复..."
    recover_sync "xiaoli_application_ros2"
fi
```

## 测试和验证示例

### 示例9: 完整的测试流程

**场景**: 验证同步系统的各项功能

```bash
#!/bin/bash
# complete_test.sh - 完整测试流程

TEST_SOURCE="/tmp/test_source"
TEST_TARGET="/tmp/test_target"

echo "开始完整功能测试..."

# 1. 创建测试环境
mkdir -p "$TEST_SOURCE"/{src,config,docs}
echo "test content" > "$TEST_SOURCE/README.md"
echo "print('hello')" > "$TEST_SOURCE/src/main.py"
echo '{"test": true}' > "$TEST_SOURCE/config/settings.json"

# 2. 添加测试同步对
./sync-manager.sh add "$TEST_SOURCE" "$TEST_TARGET" unidirectional source-wins

# 3. 执行同步测试
./sync-manager.sh sync test_source

# 4. 验证同步结果
if [ -f "$TEST_TARGET/README.md" ] && [ -f "$TEST_TARGET/src/main.py" ]; then
    echo "✓ 基础同步测试通过"
else
    echo "✗ 基础同步测试失败"
fi

# 5. 测试增量同步
echo "additional content" >> "$TEST_SOURCE/README.md"
./sync-manager.sh sync test_source

if grep -q "additional content" "$TEST_TARGET/README.md"; then
    echo "✓ 增量同步测试通过"
else
    echo "✗ 增量同步测试失败"
fi

# 6. 测试监控功能
./sync-monitor.sh start test_source
sleep 2
echo "monitor test" > "$TEST_SOURCE/monitor_test.txt"
sleep 5

if [ -f "$TEST_TARGET/monitor_test.txt" ]; then
    echo "✓ 监控功能测试通过"
else
    echo "✗ 监控功能测试失败"
fi

# 7. 清理测试环境
./sync-monitor.sh stop
./sync-manager.sh remove test_source
rm -rf "$TEST_SOURCE" "$TEST_TARGET"

echo "测试完成!"
```

这些示例展示了本地仓库同步系统在各种实际场景中的应用，从简单的备份到复杂的团队协作，从性能优化到故障恢复，涵盖了系统的主要功能和最佳实践。