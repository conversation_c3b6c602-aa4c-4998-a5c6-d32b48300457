# 同步配置 Web 界面

基于 Web 的可视化配置界面，用于管理本地仓库同步系统。

## 功能特性

### 🎛️ 可视化配置
- **仓库管理**: 添加、编辑、删除同步仓库配置
- **策略设置**: 可视化配置同步策略和冲突解决方案  
- **全局设置**: 日志、备份、性能参数统一管理
- **监控配置**: 文件监控方式和参数设置

### 📊 实时监控
- **状态面板**: 实时显示系统运行状态
- **仓库状态**: 查看各仓库同步状态和最后同步时间
- **日志查看**: 实时日志显示和过滤
- **操作控制**: 一键启动/停止监控和同步

### 🎨 现代界面
- **响应式设计**: 支持桌面和移动设备
- **直观操作**: 简洁的现代化 UI 设计
- **实时反馈**: 操作结果即时提示
- **主题适配**: 支持浅色主题

## 技术架构

```
Web界面
├── 前端 (Vanilla JS)
│   ├── HTML5 + CSS3
│   ├── 响应式布局
│   └── 原生 JavaScript
├── 后端 (Node.js)
│   ├── Express.js 服务器
│   ├── REST API
│   └── 文件系统操作
└── 集成
    ├── sync-config.json 读写
    ├── sync-manager.sh 调用
    └── 日志文件读取
```

## 快速开始

### 1. 安装依赖

```bash
# 自动安装
./install.sh

# 或手动安装
npm install
```

### 2. 启动服务

```bash
# 使用启动脚本
./start-ui.sh

# 或直接启动
npm start

# 开发模式 (自动重启)
npm run dev
```

### 3. 访问界面

打开浏览器访问: http://localhost:3000

## 界面说明

### 控制台
- 查看系统整体状态
- 快速执行同步操作
- 启动/停止监控服务
- 仓库状态概览

### 仓库配置
- 添加新的同步仓库
- 编辑现有仓库配置
- 设置同步策略和冲突解决
- 启用/禁用仓库

### 监控设置
- 配置文件监控方式
- 设置防抖时间
- 选择监控事件类型
- 调整监控参数

### 全局设置
- 日志级别和保留策略
- 备份策略配置
- 性能参数调优
- 系统行为设置

### 日志查看
- 实时日志显示
- 按级别过滤
- 日志内容搜索
- 导出日志文件

## API 接口

### 配置管理
- `GET /api/config` - 获取配置文件
- `POST /api/config` - 保存配置文件

### 状态查询
- `GET /api/status` - 获取系统状态
- `GET /api/logs` - 获取日志内容

### 操作控制
- `POST /api/sync/:repoId` - 同步指定仓库
- `POST /api/monitor/start` - 启动监控
- `POST /api/monitor/stop` - 停止监控

## 配置文件

界面直接读写 `sync-config.json` 文件，所有更改都会：

1. **实时保存**: 配置更改立即写入文件
2. **自动备份**: 保存前自动创建备份文件
3. **格式验证**: 确保 JSON 格式正确性
4. **版本控制**: 自动更新配置版本信息

## 安全考虑

- **本地访问**: 默认只监听 localhost
- **文件权限**: 仅访问同步系统目录
- **操作验证**: 危险操作需要确认
- **错误处理**: 完善的错误捕获和提示

## 故障排除

### 端口占用
```bash
# 查看端口使用情况
lsof -i :3000

# 使用其他端口
PORT=8080 npm start
```

### 权限问题
```bash
# 确保脚本可执行
chmod +x install.sh start-ui.sh

# 检查配置文件权限
ls -la ../sync-config.json
```

### 依赖问题
```bash
# 清理依赖重新安装
rm -rf node_modules package-lock.json
npm install
```

## 开发说明

### 项目结构
```
web-ui/
├── package.json          # 项目配置
├── server.js             # Express 服务器
├── install.sh            # 安装脚本
├── start-ui.sh           # 启动脚本
├── public/               # 静态文件
│   ├── index.html        # 主页面
│   ├── styles.css        # 样式文件
│   └── app.js            # 前端逻辑
└── README.md             # 说明文档
```

### 开发命令
```bash
# 安装依赖
npm install

# 开发模式 (自动重启)
npm run dev

# 生产模式
npm start

# 指定端口
PORT=8080 npm start
```

### 自定义配置

可以通过环境变量自定义配置：

```bash
# 端口号
export PORT=8080

# 配置文件路径
export CONFIG_FILE=/path/to/sync-config.json

# 脚本目录
export SCRIPT_DIR=/path/to/sync/scripts
```