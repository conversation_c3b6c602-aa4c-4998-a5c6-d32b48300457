<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地仓库同步系统 - 配置界面</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <span class="icon">🔄</span>
                    本地仓库同步系统
                </h1>
                <div class="header-actions">
                    <button id="refreshBtn" class="btn btn-secondary">
                        <span class="icon">🔄</span>
                        刷新
                    </button>
                    <button id="saveBtn" class="btn btn-primary">
                        <span class="icon">💾</span>
                        保存配置
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <nav class="nav">
                    <button class="nav-item active" data-tab="dashboard">
                        <span class="icon">📊</span>
                        控制台
                    </button>
                    <button class="nav-item" data-tab="repositories">
                        <span class="icon">📁</span>
                        仓库配置
                    </button>
                    <button class="nav-item" data-tab="monitoring">
                        <span class="icon">👁️</span>
                        监控设置
                    </button>
                    <button class="nav-item" data-tab="global">
                        <span class="icon">⚙️</span>
                        全局设置
                    </button>
                    <button class="nav-item" data-tab="logs">
                        <span class="icon">📝</span>
                        日志查看
                    </button>
                </nav>
            </aside>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 控制台 -->
                <section id="dashboard" class="tab-content active">
                    <div class="section-header">
                        <h2>系统控制台</h2>
                        <p>查看同步状态和执行操作</p>
                    </div>
                    
                    <div class="cards">
                        <div class="card">
                            <div class="card-header">
                                <h3>系统状态</h3>
                                <span id="systemStatus" class="status-badge">检查中...</span>
                            </div>
                            <div class="card-content">
                                <div class="status-grid">
                                    <div class="status-item">
                                        <span class="label">活动仓库</span>
                                        <span id="activeRepos" class="value">-</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="label">监控状态</span>
                                        <span id="monitorStatus" class="value">-</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="label">最后同步</span>
                                        <span id="lastSync" class="value">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>快速操作</h3>
                            </div>
                            <div class="card-content">
                                <div class="action-buttons">
                                    <button id="startMonitorBtn" class="btn btn-success">
                                        <span class="icon">▶️</span>
                                        启动监控
                                    </button>
                                    <button id="stopMonitorBtn" class="btn btn-danger">
                                        <span class="icon">⏹️</span>
                                        停止监控
                                    </button>
                                    <button id="syncAllBtn" class="btn btn-primary">
                                        <span class="icon">🔄</span>
                                        立即同步
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>仓库列表</h3>
                        </div>
                        <div class="card-content">
                            <div id="repositoriesList" class="repositories-list">
                                <!-- 动态生成 -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 仓库配置 -->
                <section id="repositories" class="tab-content">
                    <div class="section-header">
                        <h2>仓库配置</h2>
                        <button id="addRepoBtn" class="btn btn-primary">
                            <span class="icon">➕</span>
                            添加仓库
                        </button>
                    </div>
                    
                    <div id="repositoriesConfig" class="repositories-config">
                        <!-- 动态生成仓库配置表单 -->
                    </div>
                </section>

                <!-- 监控设置 -->
                <section id="monitoring" class="tab-content">
                    <div class="section-header">
                        <h2>监控设置</h2>
                        <p>配置文件监控和自动同步参数</p>
                    </div>
                    
                    <div class="card">
                        <div class="card-content">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="monitoringEnabled">启用监控</label>
                                    <input type="checkbox" id="monitoringEnabled" class="toggle">
                                </div>
                                
                                <div class="form-group">
                                    <label for="monitoringMethod">监控方法</label>
                                    <select id="monitoringMethod" class="select">
                                        <option value="inotify">inotify</option>
                                        <option value="polling">轮询</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="debounceSeconds">防抖秒数</label>
                                    <input type="number" id="debounceSeconds" class="input" min="1" max="60">
                                </div>
                                
                                <div class="form-group">
                                    <label for="watchSubdirs">监控子目录</label>
                                    <input type="checkbox" id="watchSubdirs" class="toggle">
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 全局设置 -->
                <section id="global" class="tab-content">
                    <div class="section-header">
                        <h2>全局设置</h2>
                        <p>配置日志、备份和性能参数</p>
                    </div>
                    
                    <div class="settings-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3>日志设置</h3>
                            </div>
                            <div class="card-content">
                                <div class="form-group">
                                    <label for="logLevel">日志级别</label>
                                    <select id="logLevel" class="select">
                                        <option value="DEBUG">DEBUG</option>
                                        <option value="INFO">INFO</option>
                                        <option value="WARNING">WARNING</option>
                                        <option value="ERROR">ERROR</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="logRetentionDays">日志保留天数</label>
                                    <input type="number" id="logRetentionDays" class="input" min="1" max="365">
                                </div>
                                
                                <div class="form-group">
                                    <label for="logMaxSize">最大日志文件大小(MB)</label>
                                    <input type="number" id="logMaxSize" class="input" min="1" max="1000">
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>备份设置</h3>
                            </div>
                            <div class="card-content">
                                <div class="form-group">
                                    <label for="backupEnabled">启用备份</label>
                                    <input type="checkbox" id="backupEnabled" class="toggle">
                                </div>
                                
                                <div class="form-group">
                                    <label for="backupRetention">备份保留数量</label>
                                    <input type="number" id="backupRetention" class="input" min="1" max="100">
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>性能设置</h3>
                            </div>
                            <div class="card-content">
                                <div class="form-group">
                                    <label for="maxConcurrentSyncs">最大并发同步数</label>
                                    <input type="number" id="maxConcurrentSyncs" class="input" min="1" max="10">
                                </div>
                                
                                <div class="form-group">
                                    <label for="timeoutSeconds">超时时间(秒)</label>
                                    <input type="number" id="timeoutSeconds" class="input" min="30" max="3600">
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 日志查看 -->
                <section id="logs" class="tab-content">
                    <div class="section-header">
                        <h2>日志查看</h2>
                        <div class="log-controls">
                            <button id="refreshLogsBtn" class="btn btn-secondary">
                                <span class="icon">🔄</span>
                                刷新
                            </button>
                            <button id="clearLogsBtn" class="btn btn-danger">
                                <span class="icon">🗑️</span>
                                清空显示
                            </button>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-content">
                            <div id="logsContainer" class="logs-container">
                                <!-- 动态生成日志内容 -->
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <!-- 确认对话框 -->
    <div id="confirmDialog" class="modal">
        <div class="modal-content">
            <h3 id="confirmTitle">确认操作</h3>
            <p id="confirmMessage">确定要执行此操作吗？</p>
            <div class="modal-actions">
                <button id="confirmCancel" class="btn btn-secondary">取消</button>
                <button id="confirmOk" class="btn btn-primary">确认</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>