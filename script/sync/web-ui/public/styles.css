/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* 布局 */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
    max-width: 1400px;
    margin: 0 auto;
}

.title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.main {
    flex: 1;
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e2e8f0;
    padding: 2rem 0;
}

.nav {
    padding: 0 1rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    background: none;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
}

.nav-item:hover {
    background: #f1f5f9;
    color: #334155;
}

.nav-item.active {
    background: #3b82f6;
    color: white;
}

.content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 组件样式 */
.section-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1e293b;
}

.section-header p {
    color: #64748b;
    margin-top: 0.25rem;
}

.cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.2s;
}

.card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 1.5rem 1.5rem 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
}

.card-content {
    padding: 1rem 1.5rem 1.5rem;
}

/* 状态显示 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.online {
    background: #dcfce7;
    color: #166534;
}

.status-badge.offline {
    background: #fee2e2;
    color: #991b1b;
}

.status-badge.warning {
    background: #fef3c7;
    color: #92400e;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.status-item {
    text-align: center;
}

.status-item .label {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.status-item .value {
    display: block;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
}

/* 按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: white;
    border-color: #d1d5db;
    color: #374151;
}

.btn-secondary:hover {
    background: #f9fafb;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* 表单 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.input, .select {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.input:focus, .select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.toggle {
    width: 3rem;
    height: 1.5rem;
    appearance: none;
    background: #d1d5db;
    border-radius: 9999px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
}

.toggle:checked {
    background: #3b82f6;
}

.toggle::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 1.25rem;
    height: 1.25rem;
    background: white;
    border-radius: 50%;
    transition: all 0.2s;
}

.toggle:checked::before {
    transform: translateX(1.5rem);
}

/* 仓库列表 */
.repositories-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.repo-item {
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.repo-info h4 {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.repo-info p {
    font-size: 0.875rem;
    color: #64748b;
}

.repo-actions {
    display: flex;
    gap: 0.5rem;
}

/* 仓库配置 */
.repositories-config {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.repo-config {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.repo-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.repo-config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* 日志 */
.log-controls {
    display: flex;
    gap: 0.75rem;
}

.logs-container {
    max-height: 500px;
    overflow-y: auto;
    background: #1e293b;
    border-radius: 0.5rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
}

.log-entry {
    margin-bottom: 0.5rem;
    display: flex;
    gap: 0.75rem;
}

.log-timestamp {
    color: #64748b;
    min-width: 120px;
}

.log-level {
    min-width: 60px;
    font-weight: 500;
}

.log-level.INFO {
    color: #3b82f6;
}

.log-level.WARNING {
    color: #f59e0b;
}

.log-level.ERROR {
    color: #ef4444;
}

.log-level.SUCCESS {
    color: #10b981;
}

.log-message {
    color: #e2e8f0;
    flex: 1;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(100%);
    transition: all 0.3s;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.toast.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.toast.warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fed7aa;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 0.75rem;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
}

.modal-content h3 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.modal-content p {
    margin-bottom: 1.5rem;
    color: #64748b;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 图标 */
.icon {
    font-size: 1em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .content {
        order: 1;
        padding: 1rem;
    }
    
    .nav {
        display: flex;
        overflow-x: auto;
        padding: 0 1rem;
    }
    
    .nav-item {
        min-width: 120px;
        margin-right: 0.5rem;
        margin-bottom: 0;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        height: auto;
        padding: 1rem 0;
        gap: 1rem;
    }
    
    .cards {
        grid-template-columns: 1fr;
    }
    
    .form-grid,
    .settings-grid,
    .repo-config-grid {
        grid-template-columns: 1fr;
    }
}