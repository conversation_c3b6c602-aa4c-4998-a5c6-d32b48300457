// 全局状态
let currentConfig = null;
let currentTab = 'dashboard';

// DOM元素
const elements = {
    // 导航
    navItems: document.querySelectorAll('.nav-item'),
    tabContents: document.querySelectorAll('.tab-content'),
    
    // 按钮
    refreshBtn: document.getElementById('refreshBtn'),
    saveBtn: document.getElementById('saveBtn'),
    addRepoBtn: document.getElementById('addRepoBtn'),
    startMonitorBtn: document.getElementById('startMonitorBtn'),
    stopMonitorBtn: document.getElementById('stopMonitorBtn'),
    syncAllBtn: document.getElementById('syncAllBtn'),
    refreshLogsBtn: document.getElementById('refreshLogsBtn'),
    clearLogsBtn: document.getElementById('clearLogsBtn'),
    
    // 状态显示
    systemStatus: document.getElementById('systemStatus'),
    activeRepos: document.getElementById('activeRepos'),
    monitorStatus: document.getElementById('monitorStatus'),
    lastSync: document.getElementById('lastSync'),
    repositoriesList: document.getElementById('repositoriesList'),
    repositoriesConfig: document.getElementById('repositoriesConfig'),
    logsContainer: document.getElementById('logsContainer'),
    
    // 表单
    monitoringEnabled: document.getElementById('monitoringEnabled'),
    monitoringMethod: document.getElementById('monitoringMethod'),
    debounceSeconds: document.getElementById('debounceSeconds'),
    watchSubdirs: document.getElementById('watchSubdirs'),
    logLevel: document.getElementById('logLevel'),
    logRetentionDays: document.getElementById('logRetentionDays'),
    logMaxSize: document.getElementById('logMaxSize'),
    backupEnabled: document.getElementById('backupEnabled'),
    backupRetention: document.getElementById('backupRetention'),
    maxConcurrentSyncs: document.getElementById('maxConcurrentSyncs'),
    timeoutSeconds: document.getElementById('timeoutSeconds'),
    
    // 模态框
    toast: document.getElementById('toast'),
    confirmDialog: document.getElementById('confirmDialog'),
    confirmTitle: document.getElementById('confirmTitle'),
    confirmMessage: document.getElementById('confirmMessage'),
    confirmOk: document.getElementById('confirmOk'),
    confirmCancel: document.getElementById('confirmCancel')
};

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    initializeEventListeners();
    loadConfig();
    loadStatus();
    loadLogs();
    
    // 定期刷新状态
    setInterval(loadStatus, 30000);
});

// 事件监听器
function initializeEventListeners() {
    // 导航切换
    elements.navItems.forEach(item => {
        item.addEventListener('click', () => {
            const tab = item.dataset.tab;
            switchTab(tab);
        });
    });
    
    // 主要按钮
    elements.refreshBtn?.addEventListener('click', () => {
        loadConfig();
        loadStatus();
        showToast('数据已刷新', 'success');
    });
    
    elements.saveBtn?.addEventListener('click', saveConfig);
    elements.addRepoBtn?.addEventListener('click', addRepository);
    elements.startMonitorBtn?.addEventListener('click', () => controlMonitor('start'));
    elements.stopMonitorBtn?.addEventListener('click', () => controlMonitor('stop'));
    elements.syncAllBtn?.addEventListener('click', syncAllRepositories);
    elements.refreshLogsBtn?.addEventListener('click', loadLogs);
    elements.clearLogsBtn?.addEventListener('click', clearLogs);
    
    // 确认对话框
    elements.confirmCancel?.addEventListener('click', hideConfirmDialog);
    
    // 表单变更监听
    const formElements = [
        elements.monitoringEnabled, elements.monitoringMethod, elements.debounceSeconds,
        elements.watchSubdirs, elements.logLevel, elements.logRetentionDays,
        elements.logMaxSize, elements.backupEnabled, elements.backupRetention,
        elements.maxConcurrentSyncs, elements.timeoutSeconds
    ];
    
    formElements.forEach(element => {
        if (element) {
            element.addEventListener('change', updateConfigFromForm);
        }
    });
}

// 标签切换
function switchTab(tab) {
    currentTab = tab;
    
    // 更新导航状态
    elements.navItems.forEach(item => {
        item.classList.toggle('active', item.dataset.tab === tab);
    });
    
    // 显示对应内容
    elements.tabContents.forEach(content => {
        content.classList.toggle('active', content.id === tab);
    });
    
    // 特定标签的额外操作
    if (tab === 'logs') {
        loadLogs();
    }
}

// API调用
async function apiCall(endpoint, options = {}) {
    try {
        const response = await fetch(endpoint, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '请求失败');
        }
        
        return await response.json();
    } catch (error) {
        console.error('API Error:', error);
        showToast(error.message, 'error');
        throw error;
    }
}

// 加载配置
async function loadConfig() {
    try {
        const config = await apiCall('/api/config');
        currentConfig = config;
        updateFormFromConfig(config);
        updateRepositoriesList(config.repositories || []);
        updateRepositoriesConfig(config.repositories || []);
    } catch (error) {
        showToast('加载配置失败', 'error');
    }
}

// 保存配置
async function saveConfig() {
    if (!currentConfig) {
        showToast('没有配置数据', 'error');
        return;
    }
    
    try {
        await apiCall('/api/config', {
            method: 'POST',
            body: JSON.stringify(currentConfig)
        });
        showToast('配置保存成功', 'success');
    } catch (error) {
        showToast('保存配置失败', 'error');
    }
}

// 加载状态
async function loadStatus() {
    try {
        const status = await apiCall('/api/status');
        updateStatusDisplay(status);
    } catch (error) {
        console.error('加载状态失败:', error);
        updateStatusDisplay({ status: 'error', error: error.message });
    }
}

// 更新状态显示
function updateStatusDisplay(status) {
    if (elements.systemStatus) {
        const statusText = status.status === 'error' ? '错误' : 
                          status.status === 'running' ? '运行中' : '停止';
        const statusClass = status.status === 'error' ? 'offline' : 
                           status.status === 'running' ? 'online' : 'warning';
        
        elements.systemStatus.textContent = statusText;
        elements.systemStatus.className = `status-badge ${statusClass}`;
    }
    
    if (elements.activeRepos && currentConfig) {
        const activeCount = currentConfig.repositories?.filter(r => r.enabled).length || 0;
        elements.activeRepos.textContent = activeCount;
    }
    
    if (elements.monitorStatus) {
        elements.monitorStatus.textContent = status.monitor_status || '未知';
    }
    
    if (elements.lastSync) {
        elements.lastSync.textContent = status.last_sync ? 
            new Date(status.last_sync).toLocaleString() : '从未';
    }
}

// 从配置更新表单
function updateFormFromConfig(config) {
    if (!config) return;
    
    // 监控设置
    if (elements.monitoringEnabled) {
        elements.monitoringEnabled.checked = config.monitoring?.enabled || false;
    }
    if (elements.monitoringMethod) {
        elements.monitoringMethod.value = config.monitoring?.method || 'inotify';
    }
    if (elements.debounceSeconds) {
        elements.debounceSeconds.value = config.monitoring?.debounce_seconds || 5;
    }
    if (elements.watchSubdirs) {
        elements.watchSubdirs.checked = config.monitoring?.watch_subdirectories !== false;
    }
    
    // 全局设置
    if (elements.logLevel) {
        elements.logLevel.value = config.global?.log_level || 'INFO';
    }
    if (elements.logRetentionDays) {
        elements.logRetentionDays.value = config.global?.log_retention_days || 30;
    }
    if (elements.logMaxSize) {
        elements.logMaxSize.value = config.global?.log_max_size_mb || 100;
    }
    if (elements.backupEnabled) {
        elements.backupEnabled.checked = config.global?.backup_enabled !== false;
    }
    if (elements.backupRetention) {
        elements.backupRetention.value = config.global?.backup_retention || 10;
    }
    if (elements.maxConcurrentSyncs) {
        elements.maxConcurrentSyncs.value = config.global?.max_concurrent_syncs || 3;
    }
    if (elements.timeoutSeconds) {
        elements.timeoutSeconds.value = config.global?.timeout_seconds || 300;
    }
}

// 从表单更新配置
function updateConfigFromForm() {
    if (!currentConfig) return;
    
    // 确保必要的对象存在
    currentConfig.monitoring = currentConfig.monitoring || {};
    currentConfig.global = currentConfig.global || {};
    
    // 监控设置
    currentConfig.monitoring.enabled = elements.monitoringEnabled?.checked || false;
    currentConfig.monitoring.method = elements.monitoringMethod?.value || 'inotify';
    currentConfig.monitoring.debounce_seconds = parseInt(elements.debounceSeconds?.value) || 5;
    currentConfig.monitoring.watch_subdirectories = elements.watchSubdirs?.checked !== false;
    
    // 全局设置
    currentConfig.global.log_level = elements.logLevel?.value || 'INFO';
    currentConfig.global.log_retention_days = parseInt(elements.logRetentionDays?.value) || 30;
    currentConfig.global.log_max_size_mb = parseInt(elements.logMaxSize?.value) || 100;
    currentConfig.global.backup_enabled = elements.backupEnabled?.checked !== false;
    currentConfig.global.backup_retention = parseInt(elements.backupRetention?.value) || 10;
    currentConfig.global.max_concurrent_syncs = parseInt(elements.maxConcurrentSyncs?.value) || 3;
    currentConfig.global.timeout_seconds = parseInt(elements.timeoutSeconds?.value) || 300;
}

// 更新仓库列表
function updateRepositoriesList(repositories) {
    if (!elements.repositoriesList) return;
    
    elements.repositoriesList.innerHTML = repositories.map(repo => `
        <div class="repo-item">
            <div class="repo-info">
                <h4>${repo.name || repo.id}</h4>
                <p>${repo.source} → ${repo.target}</p>
                <p>策略: ${repo.strategy} | 状态: ${repo.enabled ? '启用' : '禁用'}</p>
            </div>
            <div class="repo-actions">
                <button class="btn btn-primary btn-sm" onclick="syncRepository('${repo.id}')">
                    <span class="icon">🔄</span>
                    同步
                </button>
                <button class="btn btn-secondary btn-sm" onclick="toggleRepository('${repo.id}')">
                    ${repo.enabled ? '禁用' : '启用'}
                </button>
            </div>
        </div>
    `).join('');
}

// 更新仓库配置
function updateRepositoriesConfig(repositories) {
    if (!elements.repositoriesConfig) return;
    
    elements.repositoriesConfig.innerHTML = repositories.map((repo, index) => `
        <div class="repo-config" data-repo-index="${index}">
            <div class="repo-config-header">
                <h3>${repo.name || repo.id}</h3>
                <button class="btn btn-danger btn-sm" onclick="removeRepository(${index})">
                    <span class="icon">🗑️</span>
                    删除
                </button>
            </div>
            <div class="repo-config-grid">
                <div class="form-group">
                    <label>仓库ID</label>
                    <input type="text" class="input" value="${repo.id}" 
                           onchange="updateRepository(${index}, 'id', this.value)">
                </div>
                <div class="form-group">
                    <label>显示名称</label>
                    <input type="text" class="input" value="${repo.name || ''}" 
                           onchange="updateRepository(${index}, 'name', this.value)">
                </div>
                <div class="form-group">
                    <label>源路径</label>
                    <input type="text" class="input" value="${repo.source}" 
                           onchange="updateRepository(${index}, 'source', this.value)">
                </div>
                <div class="form-group">
                    <label>目标路径</label>
                    <input type="text" class="input" value="${repo.target}" 
                           onchange="updateRepository(${index}, 'target', this.value)">
                </div>
                <div class="form-group">
                    <label>同步策略</label>
                    <select class="select" onchange="updateRepository(${index}, 'strategy', this.value)">
                        <option value="unidirectional" ${repo.strategy === 'unidirectional' ? 'selected' : ''}>单向同步</option>
                        <option value="bidirectional" ${repo.strategy === 'bidirectional' ? 'selected' : ''}>双向同步</option>
                        <option value="mirror" ${repo.strategy === 'mirror' ? 'selected' : ''}>镜像模式</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>冲突解决策略</label>
                    <select class="select" onchange="updateRepository(${index}, 'conflict_resolution', this.value)">
                        <option value="source-wins" ${repo.conflict_resolution === 'source-wins' ? 'selected' : ''}>源优先</option>
                        <option value="target-wins" ${repo.conflict_resolution === 'target-wins' ? 'selected' : ''}>目标优先</option>
                        <option value="manual" ${repo.conflict_resolution === 'manual' ? 'selected' : ''}>手动解决</option>
                        <option value="keep-both" ${repo.conflict_resolution === 'keep-both' ? 'selected' : ''}>保留两者</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>启用状态</label>
                    <input type="checkbox" class="toggle" ${repo.enabled ? 'checked' : ''} 
                           onchange="updateRepository(${index}, 'enabled', this.checked)">
                </div>
                <div class="form-group">
                    <label>描述</label>
                    <input type="text" class="input" value="${repo.description || ''}" 
                           onchange="updateRepository(${index}, 'description', this.value)">
                </div>
            </div>
        </div>
    `).join('');
}

// 仓库操作函数
function updateRepository(index, field, value) {
    if (currentConfig && currentConfig.repositories[index]) {
        currentConfig.repositories[index][field] = value;
    }
}

function addRepository() {
    if (!currentConfig) {
        showToast('请先加载配置', 'error');
        return;
    }
    
    const newRepo = {
        id: `repo_${Date.now()}`,
        name: '新仓库',
        description: '',
        source: './projects/new_project',
        target: './backups/new_project',
        strategy: 'unidirectional',
        conflict_resolution: 'source-wins',
        enabled: false,
        created_at: new Date().toISOString(),
        sync_settings: {
            include_git: true,
            include_hidden: false,
            follow_symlinks: false,
            preserve_permissions: true,
            preserve_timestamps: true,
            compress_transfer: false
        },
        exclude_patterns: [
            ".git/objects/**",
            "node_modules/**",
            "*.log",
            "*.tmp"
        ],
        include_patterns: [],
        schedule: {
            enabled: false,
            interval: "30m",
            cron: "",
            run_at_startup: false
        },
        hooks: {
            pre_sync: "",
            post_sync: "",
            on_error: "",
            on_conflict: ""
        }
    };
    
    currentConfig.repositories = currentConfig.repositories || [];
    currentConfig.repositories.push(newRepo);
    
    updateRepositoriesConfig(currentConfig.repositories);
    updateRepositoriesList(currentConfig.repositories);
    
    showToast('新仓库已添加', 'success');
}

function removeRepository(index) {
    showConfirmDialog(
        '删除仓库',
        '确定要删除这个仓库配置吗？此操作不可撤销。',
        () => {
            if (currentConfig && currentConfig.repositories) {
                currentConfig.repositories.splice(index, 1);
                updateRepositoriesConfig(currentConfig.repositories);
                updateRepositoriesList(currentConfig.repositories);
                showToast('仓库已删除', 'success');
            }
        }
    );
}

function toggleRepository(repoId) {
    if (!currentConfig) return;
    
    const repo = currentConfig.repositories.find(r => r.id === repoId);
    if (repo) {
        repo.enabled = !repo.enabled;
        updateRepositoriesList(currentConfig.repositories);
        showToast(`仓库已${repo.enabled ? '启用' : '禁用'}`, 'success');
    }
}

async function syncRepository(repoId) {
    try {
        await apiCall(`/api/sync/${repoId}`, { method: 'POST' });
        showToast('同步完成', 'success');
        loadStatus();
    } catch (error) {
        showToast('同步失败', 'error');
    }
}

async function syncAllRepositories() {
    if (!currentConfig?.repositories) {
        showToast('没有配置的仓库', 'error');
        return;
    }
    
    const activeRepos = currentConfig.repositories.filter(r => r.enabled);
    if (activeRepos.length === 0) {
        showToast('没有启用的仓库', 'warning');
        return;
    }
    
    showToast('开始同步所有仓库...', 'success');
    
    for (const repo of activeRepos) {
        try {
            await syncRepository(repo.id);
        } catch (error) {
            console.error(`同步仓库 ${repo.id} 失败:`, error);
        }
    }
}

// 监控控制
async function controlMonitor(action) {
    try {
        await apiCall(`/api/monitor/${action}`, { method: 'POST' });
        showToast(`监控已${action === 'start' ? '启动' : '停止'}`, 'success');
        setTimeout(loadStatus, 1000);
    } catch (error) {
        showToast(`监控${action === 'start' ? '启动' : '停止'}失败`, 'error');
    }
}

// 日志相关
async function loadLogs() {
    try {
        const response = await apiCall('/api/logs?lines=100');
        displayLogs(response.logs || []);
    } catch (error) {
        showToast('加载日志失败', 'error');
    }
}

function displayLogs(logs) {
    if (!elements.logsContainer) return;
    
    elements.logsContainer.innerHTML = logs.map(log => `
        <div class="log-entry">
            <span class="log-timestamp">${log.timestamp}</span>
            <span class="log-level ${log.level}">${log.level}</span>
            <span class="log-message">${log.message}</span>
        </div>
    `).join('');
    
    elements.logsContainer.scrollTop = elements.logsContainer.scrollHeight;
}

function clearLogs() {
    if (elements.logsContainer) {
        elements.logsContainer.innerHTML = '<div class="log-entry"><span class="log-message">日志显示已清空</span></div>';
    }
}

// UI辅助函数
function showToast(message, type = 'success') {
    if (!elements.toast) return;
    
    elements.toast.textContent = message;
    elements.toast.className = `toast ${type} show`;
    
    setTimeout(() => {
        elements.toast.classList.remove('show');
    }, 3000);
}

function showConfirmDialog(title, message, onConfirm) {
    if (!elements.confirmDialog) return;
    
    elements.confirmTitle.textContent = title;
    elements.confirmMessage.textContent = message;
    elements.confirmDialog.classList.add('show');
    
    elements.confirmOk.onclick = () => {
        hideConfirmDialog();
        onConfirm();
    };
}

function hideConfirmDialog() {
    if (elements.confirmDialog) {
        elements.confirmDialog.classList.remove('show');
    }
}