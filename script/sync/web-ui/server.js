const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// 配置文件路径
const CONFIG_FILE = path.join(__dirname, '..', 'sync-config.json');
const SCRIPT_DIR = path.join(__dirname, '..');

// API路由
app.get('/api/config', (req, res) => {
    try {
        if (!fs.existsSync(CONFIG_FILE)) {
            return res.status(404).json({ error: '配置文件不存在' });
        }
        
        const config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
        res.json(config);
    } catch (error) {
        res.status(500).json({ error: '读取配置文件失败', details: error.message });
    }
});

app.post('/api/config', (req, res) => {
    try {
        const config = req.body;
        
        // 更新时间戳
        config.metadata.updated_at = new Date().toISOString();
        
        // 备份现有配置
        if (fs.existsSync(CONFIG_FILE)) {
            const backupFile = CONFIG_FILE + '.backup.' + Date.now();
            fs.copyFileSync(CONFIG_FILE, backupFile);
        }
        
        // 写入新配置
        fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
        
        res.json({ success: true, message: '配置保存成功' });
    } catch (error) {
        res.status(500).json({ error: '保存配置文件失败', details: error.message });
    }
});

// 获取同步状态
app.get('/api/status', (req, res) => {
    exec('cd ' + SCRIPT_DIR + ' && ./sync-manager.sh status --json', (error, stdout, stderr) => {
        if (error) {
            return res.status(500).json({ error: '获取状态失败', details: stderr });
        }
        
        try {
            const status = JSON.parse(stdout);
            res.json(status);
        } catch (parseError) {
            res.json({ status: 'unknown', output: stdout });
        }
    });
});

// 执行同步操作
app.post('/api/sync/:repoId', (req, res) => {
    const { repoId } = req.params;
    const command = `cd ${SCRIPT_DIR} && ./sync-manager.sh sync --repo ${repoId}`;
    
    exec(command, (error, stdout, stderr) => {
        if (error) {
            return res.status(500).json({ error: '同步失败', details: stderr });
        }
        
        res.json({ success: true, output: stdout });
    });
});

// 启动/停止监控
app.post('/api/monitor/:action', (req, res) => {
    const { action } = req.params; // start or stop
    const command = `cd ${SCRIPT_DIR} && ./sync-monitor.sh ${action}`;
    
    exec(command, (error, stdout, stderr) => {
        if (error) {
            return res.status(500).json({ error: `监控${action}失败`, details: stderr });
        }
        
        res.json({ success: true, output: stdout });
    });
});

// 获取日志
app.get('/api/logs', (req, res) => {
    const { lines = 100 } = req.query;
    const logDir = path.join(SCRIPT_DIR, 'logs');
    
    if (!fs.existsSync(logDir)) {
        return res.json({ logs: [] });
    }
    
    const logFiles = fs.readdirSync(logDir)
        .filter(file => file.endsWith('.log'))
        .sort()
        .reverse();
    
    if (logFiles.length === 0) {
        return res.json({ logs: [] });
    }
    
    const latestLog = path.join(logDir, logFiles[0]);
    exec(`tail -n ${lines} "${latestLog}"`, (error, stdout, stderr) => {
        if (error) {
            return res.status(500).json({ error: '读取日志失败', details: stderr });
        }
        
        const logs = stdout.split('\n').filter(line => line.trim()).map(line => {
            const match = line.match(/\[(.+?)\] \[(.+?)\] (.+)/);
            if (match) {
                return {
                    timestamp: match[1],
                    level: match[2],
                    message: match[3]
                };
            }
            return { timestamp: '', level: 'INFO', message: line };
        });
        
        res.json({ logs });
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 同步配置界面已启动: http://localhost:${PORT}`);
    console.log(`📂 配置文件路径: ${CONFIG_FILE}`);
});