# 本地仓库同步系统使用指南

## 使用方式

### 🌐 Web界面 (推荐)

提供可视化配置和管理界面：

```bash
cd web-ui
./install.sh      # 安装依赖
./start-ui.sh     # 启动界面
```

访问 http://localhost:3000 使用图形界面进行配置和管理。

### 💻 命令行界面

## 快速入门

### 1. 系统初始化

```bash
# 进入同步系统目录
cd xiaoli_application_ros2/scripts/sync

# 初始化系统
./sync-manager.sh init
```

### 2. 添加第一个同步对

```bash
# 交互式添加同步对
./sync-manager.sh add

# 或直接指定路径
./sync-manager.sh add /path/to/source /path/to/target
```

示例输入：
```
请输入源仓库路径: /home/<USER>/xiaoli_application_ros2
请输入目标仓库路径: /backup/xiaoli_application_ros2
选择同步策略:
1) unidirectional - 单向同步 (源 -> 目标)
2) bidirectional - 双向同步 (源 ↔ 目标)
3) mirror - 镜像同步 (完全复制)
4) selective - 选择性同步
请选择 (1-4): 1
选择冲突解决策略:
1) source-wins - 优先源文件
2) target-wins - 优先目标文件
3) manual - 手动解决
4) keep-both - 保留所有版本
请选择 (1-4): 1
```

### 3. 执行同步

```bash
# 同步所有启用的仓库
./sync-manager.sh sync all

# 同步特定仓库
./sync-manager.sh sync xiaoli_application_ros2
```

## 详细功能说明

### 仓库管理

#### 查看仓库列表
```bash
./sync-manager.sh list
```

输出示例：
```
ID               名称             状态     策略             源路径
──────────────────────────────────────────────────────────────────────
xiaoli_app       XiaoLi应用同步   启用     unidirectional   /home/<USER>/xiaoli_application_ros2
backup_repo      备份仓库         禁用     mirror           /work/project
```

#### 启用/禁用仓库
```bash
# 禁用仓库
./sync-manager.sh disable xiaoli_app

# 启用仓库
./sync-manager.sh enable xiaoli_app
```

#### 删除仓库配置
```bash
./sync-manager.sh remove xiaoli_app
```

### 同步操作

#### 基本同步
```bash
# 同步单个仓库
./sync-manager.sh sync xiaoli_app

# 强制同步（无确认提示）
./sync-manager.sh -f sync xiaoli_app

# 详细模式同步
./sync-manager.sh -v sync xiaoli_app
```

#### 查看同步状态
```bash
./sync-manager.sh status
```

输出示例：
```
系统状态:
  配置文件: /path/to/sync-config.json
  日志目录: /path/to/logs
  备份目录: /path/to/backups

仓库状态:
  xiaoli_app: unidirectional (/home/<USER>/xiaoli_application_ros2 -> /backup/xiaoli_application_ros2)

监控状态:
  状态: running
  启动时间: 2024-01-01 10:00:00
  监控仓库数: 1
  队列大小: 0
```

### 监控系统

#### 启动监控
```bash
# 启动所有仓库监控
./sync-monitor.sh start

# 监控特定仓库
./sync-monitor.sh start xiaoli_app

# 后台运行
./sync-monitor.sh start --daemon
```

#### 查看监控状态
```bash
./sync-monitor.sh status
```

#### 停止监控
```bash
./sync-monitor.sh stop
```

#### 重启监控
```bash
./sync-monitor.sh restart
```

### 配置管理

#### 查看配置
```bash
# 显示完整配置
./sync-manager.sh config

# 显示特定配置段
./sync-manager.sh config show global
./sync-manager.sh config show repositories
./sync-manager.sh config show monitoring
```

#### 修改配置
```bash
# 修改全局配置
./sync-manager.sh config set global.log_level=DEBUG
./sync-manager.sh config set global.backup_enabled=true

# 修改监控配置
./sync-manager.sh config set monitoring.debounce_seconds=10
./sync-manager.sh config set monitoring.method=polling
```

#### 配置示例

```json
{
  "global": {
    "log_level": "INFO",
    "backup_enabled": true,
    "backup_retention": 10
  },
  "repositories": [
    {
      "id": "xiaoli_app",
      "source": "./test_projects/example_project",
      "target": "./backups/example_project",
      "strategy": "unidirectional",
      "conflict_resolution": "source-wins",
      "enabled": true,
      "exclude_patterns": [
        "build/**",
        "install/**",
        "*.log"
      ]
    }
  ]
}
```

## 高级用法

### 同步策略详解

#### 1. 单向同步 (unidirectional)
- 只从源目录同步到目标目录
- 目标目录的更改会被覆盖
- 适用于备份、部署场景

```bash
# 配置示例
{
  "strategy": "unidirectional",
  "conflict_resolution": "source-wins"
}
```

#### 2. 双向同步 (bidirectional)
- 源和目标目录相互同步
- 需要处理冲突的可能性
- 适用于多环境开发

```bash
# 配置示例
{
  "strategy": "bidirectional",
  "conflict_resolution": "manual"
}
```

#### 3. 镜像同步 (mirror)
- 目标目录完全镜像源目录
- 会删除目标目录中多余的文件
- 适用于完整备份

```bash
# 配置示例
{
  "strategy": "mirror",
  "conflict_resolution": "source-wins"
}
```

### 冲突解决策略

#### source-wins
```bash
# 源文件优先，覆盖目标文件
"conflict_resolution": "source-wins"
```

#### target-wins
```bash
# 目标文件优先，保留目标文件
"conflict_resolution": "target-wins"
```

#### manual
```bash
# 手动解决，需要用户干预
"conflict_resolution": "manual"
```

#### keep-both
```bash
# 保留两个版本，添加时间戳后缀
"conflict_resolution": "keep-both"
```

### 排除模式

#### 在配置文件中设置
```json
{
  "exclude_patterns": [
    ".git/objects/**",
    "build/**",
    "install/**",
    "*.pyc",
    "__pycache__/**",
    "*.log",
    "*.tmp"
  ]
}
```

#### 包含模式
```json
{
  "include_patterns": [
    "src/**",
    "launch/**",
    "config/**",
    "*.md",
    "*.txt"
  ]
}
```

### 定时同步

#### 使用cron设置定时任务
```bash
# 编辑crontab
crontab -e

# 每30分钟同步一次
*/30 * * * * /path/to/sync-manager.sh sync all >/dev/null 2>&1

# 每天凌晨2点完整同步
0 2 * * * /path/to/sync-manager.sh sync all

# 工作日每小时同步
0 * * * 1-5 /path/to/sync-manager.sh sync xiaoli_app
```

#### 使用系统定时器 (systemd)
```bash
# 创建服务文件
sudo tee /etc/systemd/system/repo-sync.service << EOF
[Unit]
Description=Repository Sync Service
After=network.target

[Service]
Type=oneshot
User=your_username
ExecStart=/path/to/sync-manager.sh sync all
EOF

# 创建定时器文件
sudo tee /etc/systemd/system/repo-sync.timer << EOF
[Unit]
Description=Run repo sync every 30 minutes
Requires=repo-sync.service

[Timer]
OnCalendar=*:0/30
Persistent=true

[Install]
WantedBy=timers.target
EOF

# 启用定时器
sudo systemctl enable repo-sync.timer
sudo systemctl start repo-sync.timer
```

### Git集成

#### 启用Git集成
```json
{
  "git_integration": {
    "enabled": true,
    "auto_commit": true,
    "commit_message_template": "Auto sync: {timestamp}",
    "push_after_sync": false,
    "pull_before_sync": true
  }
}
```

#### Git钩子集成
```bash
# 在Git仓库中创建post-commit钩子
cat > .git/hooks/post-commit << 'EOF'
#!/bin/bash
# 提交后自动触发同步
/path/to/sync-manager.sh sync xiaoli_app
EOF

chmod +x .git/hooks/post-commit
```

### 备份管理

#### 配置自动备份
```json
{
  "global": {
    "backup_enabled": true,
    "backup_retention": 10,
    "backup_location": "backups/"
  }
}
```

#### 手动备份
```bash
# 备份当前配置
./sync-manager.sh backup

# 查看备份列表
ls -la backups/

# 恢复备份
./sync-manager.sh restore config-20240101_120000.json
```

## 监控和日志

### 日志管理

#### 查看日志
```bash
# 查看同步日志
./sync-manager.sh logs sync

# 查看监控日志
./sync-manager.sh logs monitor

# 查看最近50行
./sync-manager.sh logs sync 50

# 实时查看日志
tail -f logs/sync-$(date +%Y%m%d).log
```

#### 清理日志
```bash
# 清理30天前的日志
./sync-manager.sh logs clean 30

# 清理所有日志
./sync-manager.sh logs clean 0
```

### 系统监控

#### 检查系统状态
```bash
./sync-manager.sh check
```

#### 监控队列
```bash
# 查看同步队列
./sync-monitor.sh queue show

# 清空队列
./sync-monitor.sh queue clear
```

## 故障排除

### 常见问题

#### 1. 同步失败
```bash
# 检查错误日志
./sync-manager.sh logs sync | grep ERROR

# 详细模式重新同步
./sync-manager.sh -v sync xiaoli_app

# 检查源和目标路径权限
ls -la /path/to/source
ls -la /path/to/target
```

#### 2. 冲突处理
```bash
# 查看冲突详情
grep "CONFLICT" logs/sync-$(date +%Y%m%d).log

# 手动解决后重新同步
./sync-manager.sh sync xiaoli_app
```

#### 3. 监控不工作
```bash
# 检查监控状态
./sync-monitor.sh status

# 重启监控
./sync-monitor.sh restart

# 检查inotifywait是否安装
which inotifywait
```

#### 4. 性能问题
```bash
# 查看同步统计
./sync-manager.sh status

# 运行性能测试
./test-sync.sh performance

# 优化配置
./sync-manager.sh config set performance.max_parallel_files=10
./sync-manager.sh config set performance.compression_enabled=true
```

### 调试技巧

#### 启用调试模式
```bash
# 详细输出
./sync-manager.sh -v sync xiaoli_app

# 调试模式
DEBUG=true ./sync-manager.sh sync xiaoli_app

# 组合使用
DEBUG=true ./sync-manager.sh -v sync xiaoli_app
```

#### 分析日志
```bash
# 查找错误
grep -E "(ERROR|FAIL)" logs/sync-*.log

# 查找冲突
grep "CONFLICT" logs/sync-*.log

# 统计同步次数
grep "同步完成" logs/sync-*.log | wc -l
```

## 最佳实践

### 1. 安全建议
- 定期备份配置文件
- 使用排除模式过滤敏感文件
- 设置适当的文件权限
- 定期检查日志异常

### 2. 性能优化
- 合理设置并行传输数量
- 启用压缩减少网络传输
- 使用排除模式避免不必要的文件
- 定期清理日志和临时文件

### 3. 监控建议
- 启用实时监控提高响应速度
- 设置合适的防抖时间
- 监控系统资源使用情况
- 定期检查同步状态

### 4. 备份策略
- 启用自动备份功能
- 设置合理的备份保留期
- 定期测试备份恢复
- 异地备份重要数据

## 示例场景

### 场景1: 开发环境备份
```bash
# 1. 添加开发环境到备份的同步对
./sync-manager.sh add /home/<USER>/xiaoli_application_ros2 /backup/xiaoli_app

# 2. 设置为单向同步，源优先
./sync-manager.sh config set repositories[0].strategy=unidirectional
./sync-manager.sh config set repositories[0].conflict_resolution=source-wins

# 3. 启动实时监控
./sync-monitor.sh start

# 4. 设置定时备份
echo "0 */2 * * * /path/to/sync-manager.sh sync all" | crontab -
```

### 场景2: 多机开发同步
```bash
# 1. 设置双向同步
./sync-manager.sh add /work/project /laptop/project

# 2. 配置手动冲突解决
./sync-manager.sh config set repositories[0].strategy=bidirectional
./sync-manager.sh config set repositories[0].conflict_resolution=manual

# 3. 启用Git集成
./sync-manager.sh config set git_integration.enabled=true
./sync-manager.sh config set git_integration.pull_before_sync=true
```

### 场景3: 持续集成环境
```bash
# 1. 设置部署同步
./sync-manager.sh add /dev/xiaoli_app /ci/xiaoli_app

# 2. 配置镜像模式
./sync-manager.sh config set repositories[0].strategy=mirror

# 3. 设置Git钩子触发
cat > .git/hooks/post-receive << 'EOF'
#!/bin/bash
/path/to/sync-manager.sh sync xiaoli_app
EOF
```