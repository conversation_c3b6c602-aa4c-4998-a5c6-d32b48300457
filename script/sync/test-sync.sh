#!/bin/bash

# =============================================================================
# 本地仓库同步系统测试脚本
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m'

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="$SCRIPT_DIR/test_environment"
LOG_FILE="$SCRIPT_DIR/logs/test-$(date +%Y%m%d_%H%M%S).log"

# 创建日志目录
mkdir -p "$SCRIPT_DIR/logs"

# =============================================================================
# 测试工具函数
# =============================================================================

# 日志函数
log_test() {
    local level="$1"
    local message="$2"
    local color="$3"
    
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_entry="[$timestamp] [TEST-$level] $message"
    
    # 写入日志文件
    echo "$log_entry" >> "$LOG_FILE"
    
    # 控制台输出
    if [ -n "$color" ]; then
        echo -e "${color}$log_entry${NC}"
    else
        echo "$log_entry"
    fi
}

log_info() {
    log_test "INFO" "$1" "$BLUE"
}

log_success() {
    log_test "SUCCESS" "$1" "$GREEN"
}

log_warning() {
    log_test "WARNING" "$1" "$YELLOW"
}

log_error() {
    log_test "ERROR" "$1" "$RED"
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试断言
assert_equal() {
    local expected="$1"
    local actual="$2"
    local test_name="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$expected" = "$actual" ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ $test_name"
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ $test_name - 期望: '$expected', 实际: '$actual'"
        return 1
    fi
}

assert_file_exists() {
    local file_path="$1"
    local test_name="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ -f "$file_path" ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ $test_name - 文件存在: $file_path"
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ $test_name - 文件不存在: $file_path"
        return 1
    fi
}

assert_dir_exists() {
    local dir_path="$1"
    local test_name="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ -d "$dir_path" ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ $test_name - 目录存在: $dir_path"
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ $test_name - 目录不存在: $dir_path"
        return 1
    fi
}

assert_command_success() {
    local command="$1"
    local test_name="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$command" >/dev/null 2>&1; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ $test_name - 命令执行成功"
        return 0
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ $test_name - 命令执行失败: $command"
        return 1
    fi
}

# =============================================================================
# 测试环境设置
# =============================================================================

setup_test_environment() {
    log_info "设置测试环境..."
    
    # 清理旧的测试环境
    if [ -d "$TEST_DIR" ]; then
        rm -rf "$TEST_DIR"
    fi
    
    # 创建测试目录结构
    mkdir -p "$TEST_DIR"/{source_repo,target_repo,backup_repo}
    mkdir -p "$TEST_DIR"/source_repo/{src,config,docs}
    
    # 创建测试文件
    cat > "$TEST_DIR/source_repo/README.md" << EOF
# 测试仓库

这是一个用于测试同步功能的仓库。

## 文件列表
- src/main.py
- config/settings.json
- docs/guide.md
EOF
    
    cat > "$TEST_DIR/source_repo/src/main.py" << EOF
#!/usr/bin/env python3
"""
测试主程序
"""

def main():
    print("Hello, World!")
    return 0

if __name__ == "__main__":
    main()
EOF
    
    cat > "$TEST_DIR/source_repo/config/settings.json" << EOF
{
  "app_name": "test_app",
  "version": "1.0.0",
  "debug": true,
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "test_db"
  }
}
EOF
    
    cat > "$TEST_DIR/source_repo/docs/guide.md" << EOF
# 用户指南

## 安装

\`\`\`bash
pip install test_app
\`\`\`

## 使用

\`\`\`python
from test_app import main
main()
\`\`\`
EOF
    
    # 初始化Git仓库
    cd "$TEST_DIR/source_repo" || exit 1
    git init >/dev/null 2>&1
    git config user.name "Test User"
    git config user.email "<EMAIL>"
    git add .
    git commit -m "Initial commit" >/dev/null 2>&1
    cd - >/dev/null || exit 1
    
    log_success "测试环境设置完成"
}

cleanup_test_environment() {
    log_info "清理测试环境..."
    
    # 停止监控进程
    "$SCRIPT_DIR/sync-monitor.sh" stop >/dev/null 2>&1 || true
    
    # 清理测试文件
    if [ -d "$TEST_DIR" ]; then
        rm -rf "$TEST_DIR"
    fi
    
    # 恢复配置文件
    if [ -f "$SCRIPT_DIR/sync-config.json.backup" ]; then
        mv "$SCRIPT_DIR/sync-config.json.backup" "$SCRIPT_DIR/sync-config.json"
    fi
    
    log_success "测试环境清理完成"
}

# =============================================================================
# 核心功能测试
# =============================================================================

test_config_management() {
    log_info "测试配置管理功能..."
    
    # 备份现有配置
    if [ -f "$SCRIPT_DIR/sync-config.json" ]; then
        cp "$SCRIPT_DIR/sync-config.json" "$SCRIPT_DIR/sync-config.json.backup"
    fi
    
    # 测试初始化
    assert_command_success "$SCRIPT_DIR/sync-manager.sh init" "配置初始化"
    assert_file_exists "$SCRIPT_DIR/sync-config.json" "配置文件创建"
    
    # 测试配置读取
    local log_level
    log_level=$(jq -r '.global.log_level' "$SCRIPT_DIR/sync-config.json" 2>/dev/null)
    assert_equal "INFO" "$log_level" "默认日志级别"
    
    # 测试配置修改
    "$SCRIPT_DIR/sync-manager.sh" config set "global.log_level=DEBUG" >/dev/null 2>&1
    log_level=$(jq -r '.global.log_level' "$SCRIPT_DIR/sync-config.json" 2>/dev/null)
    assert_equal "DEBUG" "$log_level" "配置修改"
    
    log_success "配置管理测试完成"
}

test_repository_management() {
    log_info "测试仓库管理功能..."
    
    local source_path="$TEST_DIR/source_repo"
    local target_path="$TEST_DIR/target_repo"
    
    # 测试添加仓库
    echo -e "test_repo\n$source_path\n$target_path\n1\n1\nn" | "$SCRIPT_DIR/sync-manager.sh" add >/dev/null 2>&1
    
    # 验证仓库添加
    local repo_count
    repo_count=$(jq '.repositories | length' "$SCRIPT_DIR/sync-config.json" 2>/dev/null)
    assert_equal "1" "$repo_count" "仓库添加"
    
    # 测试仓库列表
    local repo_id
    repo_id=$(jq -r '.repositories[0].id' "$SCRIPT_DIR/sync-config.json" 2>/dev/null)
    assert_equal "source_repo" "$repo_id" "仓库ID生成"
    
    # 测试仓库启用/禁用
    "$SCRIPT_DIR/sync-manager.sh" disable "$repo_id" >/dev/null 2>&1
    local enabled
    enabled=$(jq -r '.repositories[0].enabled' "$SCRIPT_DIR/sync-config.json" 2>/dev/null)
    assert_equal "false" "$enabled" "仓库禁用"
    
    "$SCRIPT_DIR/sync-manager.sh" enable "$repo_id" >/dev/null 2>&1
    enabled=$(jq -r '.repositories[0].enabled' "$SCRIPT_DIR/sync-config.json" 2>/dev/null)
    assert_equal "true" "$enabled" "仓库启用"
    
    log_success "仓库管理测试完成"
}

test_sync_functionality() {
    log_info "测试同步功能..."
    
    local source_path="$TEST_DIR/source_repo"
    local target_path="$TEST_DIR/target_repo"
    
    # 执行同步
    "$SCRIPT_DIR/sync-manager.sh" sync source_repo >/dev/null 2>&1
    
    # 验证文件同步
    assert_file_exists "$target_path/README.md" "README文件同步"
    assert_file_exists "$target_path/src/main.py" "Python文件同步"
    assert_file_exists "$target_path/config/settings.json" "配置文件同步"
    assert_file_exists "$target_path/docs/guide.md" "文档文件同步"
    
    # 验证内容一致性
    local source_md5
    local target_md5
    source_md5=$(md5sum "$source_path/README.md" | cut -d' ' -f1)
    target_md5=$(md5sum "$target_path/README.md" | cut -d' ' -f1)
    assert_equal "$source_md5" "$target_md5" "文件内容一致性"
    
    # 测试增量同步
    echo "新增内容" >> "$source_path/README.md"
    "$SCRIPT_DIR/sync-manager.sh" sync source_repo >/dev/null 2>&1
    
    # 验证增量更新
    if grep -q "新增内容" "$target_path/README.md"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ 增量同步功能"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ 增量同步功能失败"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    log_success "同步功能测试完成"
}

test_conflict_resolution() {
    log_info "测试冲突解决功能..."
    
    local source_path="$TEST_DIR/source_repo"
    local target_path="$TEST_DIR/target_repo"
    
    # 创建冲突情况
    echo "源仓库修改" > "$source_path/conflict_file.txt"
    echo "目标仓库修改" > "$target_path/conflict_file.txt"
    
    # 测试source-wins策略
    jq '(.repositories[0].conflict_resolution) = "source-wins"' "$SCRIPT_DIR/sync-config.json" > "$SCRIPT_DIR/sync-config.json.tmp" && mv "$SCRIPT_DIR/sync-config.json.tmp" "$SCRIPT_DIR/sync-config.json"
    
    "$SCRIPT_DIR/sync-manager.sh" sync source_repo >/dev/null 2>&1
    
    local content
    content=$(cat "$target_path/conflict_file.txt" 2>/dev/null)
    assert_equal "源仓库修改" "$content" "source-wins冲突解决"
    
    log_success "冲突解决测试完成"
}

test_monitoring_system() {
    log_info "测试监控系统..."
    
    # 检查监控工具
    if ! command -v inotifywait >/dev/null 2>&1; then
        log_warning "inotifywait未安装，跳过监控测试"
        return 0
    fi
    
    # 启动监控
    "$SCRIPT_DIR/sync-monitor.sh" start source_repo >/dev/null 2>&1
    sleep 2
    
    # 检查监控状态
    local monitor_status
    monitor_status=$("$SCRIPT_DIR/sync-monitor.sh" status 2>/dev/null | grep -c "running" || echo "0")
    
    if [ "$monitor_status" -gt 0 ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ 监控系统启动"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ 监控系统启动失败"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 停止监控
    "$SCRIPT_DIR/sync-monitor.sh" stop >/dev/null 2>&1
    
    log_success "监控系统测试完成"
}

test_git_integration() {
    log_info "测试Git集成功能..."
    
    local source_path="$TEST_DIR/source_repo"
    
    # 启用Git集成
    jq '.git_integration.enabled = true | .git_integration.auto_commit = true' "$SCRIPT_DIR/sync-config.json" > "$SCRIPT_DIR/sync-config.json.tmp" && mv "$SCRIPT_DIR/sync-config.json.tmp" "$SCRIPT_DIR/sync-config.json"
    
    # 在源仓库添加文件
    echo "Git测试文件" > "$source_path/git_test.txt"
    cd "$source_path" || exit 1
    git add git_test.txt
    git commit -m "添加Git测试文件" >/dev/null 2>&1
    cd - >/dev/null || exit 1
    
    # 执行同步
    "$SCRIPT_DIR/sync-manager.sh" sync source_repo >/dev/null 2>&1
    
    # 验证Git集成
    assert_file_exists "$TEST_DIR/target_repo/git_test.txt" "Git文件同步"
    
    log_success "Git集成测试完成"
}

test_backup_functionality() {
    log_info "测试备份功能..."
    
    # 启用备份
    jq '.global.backup_enabled = true' "$SCRIPT_DIR/sync-config.json" > "$SCRIPT_DIR/sync-config.json.tmp" && mv "$SCRIPT_DIR/sync-config.json.tmp" "$SCRIPT_DIR/sync-config.json"
    
    # 执行同步（会创建备份）
    "$SCRIPT_DIR/sync-manager.sh" sync source_repo >/dev/null 2>&1
    
    # 检查备份目录
    local backup_count
    backup_count=$(ls -1 "$SCRIPT_DIR/backups"/ 2>/dev/null | wc -l)
    
    if [ "$backup_count" -gt 0 ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ 备份功能"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ 备份功能失败"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    log_success "备份功能测试完成"
}

test_performance() {
    log_info "测试性能..."
    
    local source_path="$TEST_DIR/source_repo"
    
    # 创建大量测试文件
    mkdir -p "$source_path/performance_test"
    for i in {1..100}; do
        echo "测试文件 $i 的内容" > "$source_path/performance_test/file_$i.txt"
    done
    
    # 测量同步时间
    local start_time
    local end_time
    local duration
    
    start_time=$(date +%s)
    "$SCRIPT_DIR/sync-manager.sh" sync source_repo >/dev/null 2>&1
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    log_info "同步100个文件耗时: ${duration}秒"
    
    # 验证所有文件都已同步
    local synced_count
    synced_count=$(ls -1 "$TEST_DIR/target_repo/performance_test"/ 2>/dev/null | wc -l)
    assert_equal "100" "$synced_count" "批量文件同步"
    
    log_success "性能测试完成"
}

# =============================================================================
# 错误处理测试
# =============================================================================

test_error_handling() {
    log_info "测试错误处理..."
    
    # 测试无效路径
    local result
    result=$("$SCRIPT_DIR/sync-manager.sh" sync non_existent_repo 2>&1 | grep -c "不存在" || echo "0")
    
    if [ "$result" -gt 0 ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_success "✓ 无效仓库ID错误处理"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_error "✗ 无效仓库ID错误处理"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 测试权限错误
    local restricted_dir="/root/test_restricted"
    if [ ! -d "$restricted_dir" ]; then
        mkdir -p "$TEST_DIR/restricted" 2>/dev/null || true
        chmod 000 "$TEST_DIR/restricted" 2>/dev/null || true
        restricted_dir="$TEST_DIR/restricted"
    fi
    
    # 添加受限目录到配置（预期会失败）
    echo -e "restricted_test\n$TEST_DIR/source_repo\n$restricted_dir\n1\n1\nn" | "$SCRIPT_DIR/sync-manager.sh" add >/dev/null 2>&1 || true
    
    log_success "错误处理测试完成"
}

# =============================================================================
# 主测试套件
# =============================================================================

run_all_tests() {
    log_info "开始运行完整测试套件..."
    
    # 设置测试环境
    setup_test_environment
    
    # 运行各项测试
    test_config_management
    test_repository_management
    test_sync_functionality
    test_conflict_resolution
    test_monitoring_system
    test_git_integration
    test_backup_functionality
    test_performance
    test_error_handling
    
    # 清理测试环境
    cleanup_test_environment
}

# 显示测试结果
show_test_results() {
    echo
    echo -e "${BOLD}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${BOLD}                        测试结果统计                           ${NC}"
    echo -e "${BOLD}═══════════════════════════════════════════════════════════════${NC}"
    echo
    echo -e "总测试数: ${BOLD}$TOTAL_TESTS${NC}"
    echo -e "通过测试: ${GREEN}${BOLD}$PASSED_TESTS${NC}"
    echo -e "失败测试: ${RED}${BOLD}$FAILED_TESTS${NC}"
    echo
    
    local success_rate
    if [ $TOTAL_TESTS -gt 0 ]; then
        success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
        echo -e "成功率: ${BOLD}$success_rate%${NC}"
    else
        echo -e "成功率: ${BOLD}0%${NC}"
    fi
    
    echo
    echo -e "详细日志: $LOG_FILE"
    echo
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}${BOLD}🎉 所有测试通过！${NC}"
        exit 0
    else
        echo -e "${RED}${BOLD}❌ 有测试失败，请检查日志${NC}"
        exit 1
    fi
}

# 显示帮助
show_help() {
    cat << EOF
本地仓库同步系统测试工具

用法: $0 [test_name]

可用测试:
  all                 运行所有测试 (默认)
  config              配置管理测试
  repository          仓库管理测试
  sync                同步功能测试
  conflict            冲突解决测试
  monitor             监控系统测试
  git                 Git集成测试
  backup              备份功能测试
  performance         性能测试
  error               错误处理测试

选项:
  -h, --help          显示此帮助信息
  -v, --verbose       详细输出
  -c, --clean         只清理测试环境

示例:
  $0                  # 运行所有测试
  $0 sync             # 只运行同步测试
  $0 -c               # 清理测试环境

EOF
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    local test_name="${1:-all}"
    
    case "$test_name" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            cleanup_test_environment
            exit 0
            ;;
        -v|--verbose)
            DEBUG=true
            test_name="${2:-all}"
            ;;
    esac
    
    echo -e "${CYAN}${BOLD}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              本地仓库同步系统测试工具                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    log_info "开始测试: $test_name"
    log_info "测试日志: $LOG_FILE"
    
    case "$test_name" in
        "all")
            run_all_tests
            ;;
        "config")
            setup_test_environment
            test_config_management
            cleanup_test_environment
            ;;
        "repository")
            setup_test_environment
            test_repository_management
            cleanup_test_environment
            ;;
        "sync")
            setup_test_environment
            test_sync_functionality
            cleanup_test_environment
            ;;
        "conflict")
            setup_test_environment
            test_conflict_resolution
            cleanup_test_environment
            ;;
        "monitor")
            setup_test_environment
            test_monitoring_system
            cleanup_test_environment
            ;;
        "git")
            setup_test_environment
            test_git_integration
            cleanup_test_environment
            ;;
        "backup")
            setup_test_environment
            test_backup_functionality
            cleanup_test_environment
            ;;
        "performance")
            setup_test_environment
            test_performance
            cleanup_test_environment
            ;;
        "error")
            setup_test_environment
            test_error_handling
            cleanup_test_environment
            ;;
        *)
            log_error "未知测试: $test_name"
            show_help
            exit 1
            ;;
    esac
    
    show_test_results
}

# 执行主函数
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi