#!/bin/bash

# =============================================================================
# 本地仓库同步管理器 - 主要命令行工具
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m'

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/sync-config.json"
LOG_DIR="$SCRIPT_DIR/logs"
BACKUP_DIR="$SCRIPT_DIR/backups"
TEMP_DIR="$SCRIPT_DIR/tmp"

# 创建必要目录
mkdir -p "$LOG_DIR" "$BACKUP_DIR" "$TEMP_DIR"

# 加载核心模块
source "$SCRIPT_DIR/sync-core.sh"

# 版本信息
VERSION="1.0.0"
AUTHOR="XiaoLi Robot Sync System"

# =============================================================================
# 工具函数
# =============================================================================

# 打印标题
print_header() {
    echo -e "${CYAN}${BOLD}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              本地仓库同步管理器 v$VERSION              ║"
    echo "║                     $AUTHOR                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 打印分隔线
print_separator() {
    echo -e "${BLUE}$1${NC}"
    echo "────────────────────────────────────────────────────────────────"
}

# 确认提示
confirm() {
    local message="$1"
    local default="${2:-n}"
    
    echo -e "${YELLOW}$message${NC}"
    read -p "确认吗? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0
    else
        return 1
    fi
}

# 输入验证
validate_path() {
    local path="$1"
    local must_exist="${2:-true}"
    
    if [ -z "$path" ]; then
        echo "路径不能为空"
        return 1
    fi
    
    if [ "$must_exist" = "true" ] && [ ! -d "$path" ]; then
        echo "路径不存在: $path"
        return 1
    fi
    
    # 转换为绝对路径
    if [ -d "$path" ]; then
        realpath "$path"
    else
        echo "$path"
    fi
}

# 生成唯一ID
generate_repo_id() {
    local base_name="$1"
    local counter=1
    local repo_id="$base_name"
    
    # 检查ID是否已存在
    while jq -e ".repositories[] | select(.id == \"$repo_id\")" "$CONFIG_FILE" >/dev/null 2>&1; do
        repo_id="${base_name}_${counter}"
        counter=$((counter + 1))
    done
    
    echo "$repo_id"
}

# =============================================================================
# 配置管理
# =============================================================================

# 初始化配置
init_config() {
    print_separator "初始化同步系统"
    
    if [ -f "$CONFIG_FILE" ]; then
        if ! confirm "配置文件已存在，是否重新初始化?"; then
            log_info "取消初始化"
            return 0
        fi
    fi
    
    log_info "创建默认配置文件..."
    
    # 复制默认配置
    cp "$SCRIPT_DIR/sync-config.json" "$CONFIG_FILE.backup" 2>/dev/null || true
    
    # 更新配置时间戳
    local temp_file
    temp_file=$(mktemp)
    jq ".metadata.created_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\" | .metadata.updated_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$CONFIG_FILE" > "$temp_file" && mv "$temp_file" "$CONFIG_FILE"
    
    log_success "配置文件初始化完成: $CONFIG_FILE"
    
    # 创建必要的目录结构
    mkdir -p logs backups tmp pids
    
    log_info "目录结构创建完成"
    
    # 安装依赖检查
    check_dependencies
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 必需的工具
    local required_tools=("jq" "rsync" "git")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_deps+=("$tool")
        fi
    done
    
    # 可选的工具
    local optional_tools=("inotifywait")
    for tool in "${optional_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            log_warning "可选工具未安装: $tool (建议安装以启用实时监控)"
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少必需的依赖: ${missing_deps[*]}"
        echo "请安装缺少的工具："
        echo "  Ubuntu/Debian: sudo apt-get install ${missing_deps[*]}"
        echo "  CentOS/RHEL: sudo yum install ${missing_deps[*]}"
        return 1
    fi
    
    log_success "依赖检查完成"
    return 0
}

# =============================================================================
# 仓库管理
# =============================================================================

# 添加同步对
add_sync_pair() {
    local source="$1"
    local target="$2"
    local strategy="${3:-unidirectional}"
    local conflict_resolution="${4:-source-wins}"
    
    print_separator "添加同步对"
    
    # 交互式输入
    if [ -z "$source" ]; then
        echo -e "${BLUE}请输入源仓库路径:${NC}"
        read -r source
    fi
    
    if [ -z "$target" ]; then
        echo -e "${BLUE}请输入目标仓库路径:${NC}"
        read -r target
    fi
    
    # 验证路径
    source=$(validate_path "$source" true) || return 1
    target=$(validate_path "$target" false) || return 1
    
    # 策略选择
    if [ -z "$strategy" ] || [ "$strategy" = "interactive" ]; then
        echo -e "${BLUE}选择同步策略:${NC}"
        echo "1) unidirectional - 单向同步 (源 -> 目标)"
        echo "2) bidirectional - 双向同步 (源 ↔ 目标)"
        echo "3) mirror - 镜像同步 (完全复制)"
        echo "4) selective - 选择性同步"
        read -p "请选择 (1-4): " -n 1 -r
        echo
        
        case $REPLY in
            1) strategy="unidirectional" ;;
            2) strategy="bidirectional" ;;
            3) strategy="mirror" ;;
            4) strategy="selective" ;;
            *) strategy="unidirectional" ;;
        esac
    fi
    
    # 冲突解决策略
    if [ -z "$conflict_resolution" ] || [ "$conflict_resolution" = "interactive" ]; then
        echo -e "${BLUE}选择冲突解决策略:${NC}"
        echo "1) source-wins - 优先源文件"
        echo "2) target-wins - 优先目标文件"
        echo "3) manual - 手动解决"
        echo "4) keep-both - 保留所有版本"
        read -p "请选择 (1-4): " -n 1 -r
        echo
        
        case $REPLY in
            1) conflict_resolution="source-wins" ;;
            2) conflict_resolution="target-wins" ;;
            3) conflict_resolution="manual" ;;
            4) conflict_resolution="keep-both" ;;
            *) conflict_resolution="source-wins" ;;
        esac
    fi
    
    # 生成仓库ID
    local base_name
    base_name=$(basename "$source")
    local repo_id
    repo_id=$(generate_repo_id "$base_name")
    
    # 添加到配置
    local temp_file
    temp_file=$(mktemp)
    
    local new_repo
    new_repo=$(cat << EOF
{
  "id": "$repo_id",
  "name": "$base_name 同步",
  "description": "从 $source 到 $target 的同步",
  "source": "$source",
  "target": "$target",
  "strategy": "$strategy",
  "conflict_resolution": "$conflict_resolution",
  "enabled": true,
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "sync_settings": {
    "include_git": true,
    "include_hidden": false,
    "follow_symlinks": false,
    "preserve_permissions": true,
    "preserve_timestamps": true,
    "compress_transfer": false
  },
  "exclude_patterns": [
    ".git/objects/**",
    ".git/logs/**",
    "build/**",
    "install/**",
    "log/**",
    "*.pyc",
    "__pycache__/**",
    "node_modules/**",
    "*.log",
    "*.tmp"
  ],
  "include_patterns": [],
  "schedule": {
    "enabled": false,
    "interval": "30m",
    "cron": "",
    "run_at_startup": false
  },
  "hooks": {
    "pre_sync": "",
    "post_sync": "",
    "on_error": "",
    "on_conflict": ""
  }
}
EOF
)
    
    jq ".repositories += [$new_repo] | .metadata.updated_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$CONFIG_FILE" > "$temp_file" && mv "$temp_file" "$CONFIG_FILE"
    
    log_success "同步对添加成功"
    echo "  仓库ID: $repo_id"
    echo "  源路径: $source"
    echo "  目标路径: $target"
    echo "  策略: $strategy"
    echo "  冲突解决: $conflict_resolution"
    
    # 询问是否立即同步
    if confirm "是否立即执行首次同步?"; then
        sync_repository "$repo_id"
    fi
}

# 列出仓库
list_repositories() {
    print_separator "仓库列表"
    
    if ! jq -e '.repositories | length > 0' "$CONFIG_FILE" >/dev/null 2>&1; then
        echo "没有配置的仓库"
        return 0
    fi
    
    echo -e "${BOLD}ID\t\t名称\t\t状态\t策略\t\t源路径${NC}"
    echo "──────────────────────────────────────────────────────────────────────────"
    
    jq -r '.repositories[] | [.id, .name, (if .enabled then "启用" else "禁用" end), .strategy, .source] | @tsv' "$CONFIG_FILE" | \
    while IFS=$'\t' read -r id name enabled strategy source; do
        local color="$GREEN"
        [ "$enabled" = "禁用" ] && color="$RED"
        
        printf "${color}%-16s${NC} %-16s %-8s %-16s %s\n" "$id" "$name" "$enabled" "$strategy" "$source"
    done
}

# 删除仓库
remove_repository() {
    local repo_id="$1"
    
    if [ -z "$repo_id" ]; then
        echo -e "${BLUE}请输入要删除的仓库ID:${NC}"
        read -r repo_id
    fi
    
    # 检查仓库是否存在
    if ! jq -e ".repositories[] | select(.id == \"$repo_id\")" "$CONFIG_FILE" >/dev/null 2>&1; then
        log_error "仓库不存在: $repo_id"
        return 1
    fi
    
    # 显示仓库信息
    echo -e "${BLUE}要删除的仓库信息:${NC}"
    jq -r ".repositories[] | select(.id == \"$repo_id\") | \"ID: \\(.id)\\n名称: \\(.name)\\n源: \\(.source)\\n目标: \\(.target)\"" "$CONFIG_FILE"
    
    if confirm "确认删除此仓库配置? (此操作不会删除实际文件)"; then
        local temp_file
        temp_file=$(mktemp)
        jq ".repositories = [.repositories[] | select(.id != \"$repo_id\")] | .metadata.updated_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$CONFIG_FILE" > "$temp_file" && mv "$temp_file" "$CONFIG_FILE"
        
        log_success "仓库配置已删除: $repo_id"
    else
        log_info "取消删除操作"
    fi
}

# 启用/禁用仓库
toggle_repository() {
    local repo_id="$1"
    local action="$2"
    
    if [ -z "$repo_id" ]; then
        echo -e "${BLUE}请输入仓库ID:${NC}"
        read -r repo_id
    fi
    
    # 检查仓库是否存在
    if ! jq -e ".repositories[] | select(.id == \"$repo_id\")" "$CONFIG_FILE" >/dev/null 2>&1; then
        log_error "仓库不存在: $repo_id"
        return 1
    fi
    
    local enabled_value
    case "$action" in
        "enable"|"启用")
            enabled_value="true"
            ;;
        "disable"|"禁用")
            enabled_value="false"
            ;;
        *)
            # 切换状态
            local current_state
            current_state=$(jq -r ".repositories[] | select(.id == \"$repo_id\") | .enabled" "$CONFIG_FILE")
            enabled_value=$( [ "$current_state" = "true" ] && echo "false" || echo "true" )
            ;;
    esac
    
    local temp_file
    temp_file=$(mktemp)
    jq "(.repositories[] | select(.id == \"$repo_id\") | .enabled) = $enabled_value | .metadata.updated_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$CONFIG_FILE" > "$temp_file" && mv "$temp_file" "$CONFIG_FILE"
    
    local status_text
    status_text=$( [ "$enabled_value" = "true" ] && echo "启用" || echo "禁用" )
    
    log_success "仓库 $repo_id 已$status_text"
}

# =============================================================================
# 同步操作
# =============================================================================

# 执行同步
execute_sync() {
    local repo_id="$1"
    local force="${2:-false}"
    
    print_separator "执行同步"
    
    if [ -z "$repo_id" ]; then
        echo -e "${BLUE}同步选项:${NC}"
        echo "1) 同步所有启用的仓库"
        echo "2) 同步特定仓库"
        read -p "请选择 (1-2): " -n 1 -r
        echo
        
        case $REPLY in
            1)
                repo_id="all"
                ;;
            2)
                echo -e "${BLUE}请输入仓库ID:${NC}"
                read -r repo_id
                ;;
            *)
                repo_id="all"
                ;;
        esac
    fi
    
    if [ "$repo_id" = "all" ]; then
        if [ "$force" = "false" ] && ! confirm "确认同步所有启用的仓库?"; then
            log_info "取消同步操作"
            return 0
        fi
        
        sync_all_repositories
    else
        # 检查仓库是否存在
        if ! jq -e ".repositories[] | select(.id == \"$repo_id\")" "$CONFIG_FILE" >/dev/null 2>&1; then
            log_error "仓库不存在: $repo_id"
            return 1
        fi
        
        if [ "$force" = "false" ] && ! confirm "确认同步仓库 $repo_id?"; then
            log_info "取消同步操作"
            return 0
        fi
        
        sync_repository "$repo_id"
    fi
}

# 同步状态
sync_status() {
    print_separator "同步状态"
    
    # 显示全局状态
    echo -e "${BOLD}系统状态:${NC}"
    echo "  配置文件: $CONFIG_FILE"
    echo "  日志目录: $LOG_DIR"
    echo "  备份目录: $BACKUP_DIR"
    
    # 显示仓库状态
    echo -e "\n${BOLD}仓库状态:${NC}"
    jq -r '.repositories[] | select(.enabled == true) | "\(.id): \(.strategy) (\(.source) -> \(.target))"' "$CONFIG_FILE" | while read -r line; do
        echo "  $line"
    done
    
    # 显示监控状态
    echo -e "\n${BOLD}监控状态:${NC}"
    "$SCRIPT_DIR/sync-monitor.sh" status
}

# =============================================================================
# 配置管理接口
# =============================================================================

# 显示配置
show_config() {
    local section="$1"
    
    print_separator "配置信息"
    
    case "$section" in
        "global"|"全局")
            echo -e "${BOLD}全局配置:${NC}"
            jq -r '.global | to_entries[] | "  \(.key): \(.value)"' "$CONFIG_FILE"
            ;;
        "repositories"|"仓库")
            list_repositories
            ;;
        "monitoring"|"监控")
            echo -e "${BOLD}监控配置:${NC}"
            jq -r '.monitoring | to_entries[] | "  \(.key): \(.value)"' "$CONFIG_FILE"
            ;;
        *)
            echo -e "${BOLD}完整配置:${NC}"
            jq '.' "$CONFIG_FILE"
            ;;
    esac
}

# 修改配置
modify_config() {
    local key="$1"
    local value="$2"
    
    if [ -z "$key" ] || [ -z "$value" ]; then
        echo -e "${BLUE}修改配置项${NC}"
        echo "示例: global.log_level=DEBUG"
        echo "示例: monitoring.debounce_seconds=10"
        read -p "请输入配置项 (key=value): " -r config_input
        
        if [[ "$config_input" =~ ^([^=]+)=(.+)$ ]]; then
            key="${BASH_REMATCH[1]}"
            value="${BASH_REMATCH[2]}"
        else
            log_error "配置格式无效"
            return 1
        fi
    fi
    
    # 构建jq路径
    local jq_path=".$key"
    
    # 尝试更新配置
    local temp_file
    temp_file=$(mktemp)
    
    # 检测值类型并相应处理
    if [[ "$value" =~ ^[0-9]+$ ]]; then
        # 数字
        jq "$jq_path = $value | .metadata.updated_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$CONFIG_FILE" > "$temp_file"
    elif [[ "$value" =~ ^(true|false)$ ]]; then
        # 布尔值
        jq "$jq_path = $value | .metadata.updated_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$CONFIG_FILE" > "$temp_file"
    else
        # 字符串
        jq "$jq_path = \"$value\" | .metadata.updated_at = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$CONFIG_FILE" > "$temp_file"
    fi
    
    if [ $? -eq 0 ]; then
        mv "$temp_file" "$CONFIG_FILE"
        log_success "配置已更新: $key = $value"
    else
        rm -f "$temp_file"
        log_error "配置更新失败"
        return 1
    fi
}

# =============================================================================
# 日志和历史
# =============================================================================

# 显示日志
show_logs() {
    local log_type="$1"
    local lines="${2:-50}"
    
    case "$log_type" in
        "sync"|"同步")
            local latest_log
            latest_log=$(ls -t "$LOG_DIR"/sync-*.log 2>/dev/null | head -1)
            if [ -n "$latest_log" ]; then
                echo -e "${BOLD}同步日志 (最近 $lines 行):${NC}"
                tail -n "$lines" "$latest_log"
            else
                echo "没有找到同步日志"
            fi
            ;;
        "monitor"|"监控")
            local latest_log
            latest_log=$(ls -t "$LOG_DIR"/monitor-*.log 2>/dev/null | head -1)
            if [ -n "$latest_log" ]; then
                echo -e "${BOLD}监控日志 (最近 $lines 行):${NC}"
                tail -n "$lines" "$latest_log"
            else
                echo "没有找到监控日志"
            fi
            ;;
        *)
            echo -e "${BOLD}所有日志文件:${NC}"
            ls -la "$LOG_DIR"/*.log 2>/dev/null || echo "没有找到日志文件"
            ;;
    esac
}

# 清理日志
clean_logs() {
    local days="${1:-30}"
    
    if confirm "删除 $days 天前的日志文件?"; then
        find "$LOG_DIR" -name "*.log" -mtime +$days -delete
        log_success "旧日志文件已清理"
    fi
}

# =============================================================================
# 帮助系统
# =============================================================================

show_help() {
    cat << EOF
本地仓库同步管理器 v$VERSION

用法: $0 <command> [options]

初始化命令:
  init                    初始化同步系统

仓库管理:
  add [source] [target]   添加同步对
  list                    列出所有仓库
  remove [repo_id]        删除仓库配置
  enable [repo_id]        启用仓库
  disable [repo_id]       禁用仓库

同步操作:
  sync [repo_id|all]      执行同步
  status                  显示同步状态

监控管理:
  monitor start [repo_id] 启动监控
  monitor stop            停止监控
  monitor status          监控状态

配置管理:
  config                  显示完整配置
  config show [section]   显示配置段 (global|repositories|monitoring)
  config set key=value    修改配置项

日志管理:
  logs [sync|monitor]     显示日志
  logs clean [days]       清理旧日志

工具命令:
  check                   检查系统状态
  backup                  备份配置
  restore [backup_file]   恢复配置
  version                 显示版本信息

选项:
  -h, --help             显示帮助
  -v, --verbose          详细输出
  -f, --force            强制执行
  -q, --quiet            静默模式

示例:
  $0 init                                    # 初始化系统
  $0 add /home/<USER>/project /backup/project  # 添加同步对
  $0 sync all                               # 同步所有仓库
  $0 monitor start                          # 启动监控
  $0 config set global.log_level=DEBUG     # 修改日志级别

详细文档: $SCRIPT_DIR/README.md

EOF
}

# 显示版本信息
show_version() {
    echo "本地仓库同步管理器 v$VERSION"
    echo "作者: $AUTHOR"
    echo "路径: $SCRIPT_DIR"
    echo "配置: $CONFIG_FILE"
}

# =============================================================================
# 系统工具
# =============================================================================

# 系统检查
system_check() {
    print_separator "系统检查"
    
    echo -e "${BOLD}1. 依赖检查${NC}"
    check_dependencies
    
    echo -e "\n${BOLD}2. 配置检查${NC}"
    if [ -f "$CONFIG_FILE" ]; then
        if jq '.' "$CONFIG_FILE" >/dev/null 2>&1; then
            log_success "配置文件格式正确"
        else
            log_error "配置文件格式错误"
        fi
    else
        log_warning "配置文件不存在，请运行 'init' 命令"
    fi
    
    echo -e "\n${BOLD}3. 目录结构检查${NC}"
    for dir in "$LOG_DIR" "$BACKUP_DIR" "$TEMP_DIR"; do
        if [ -d "$dir" ]; then
            log_success "目录存在: $dir"
        else
            log_warning "目录不存在: $dir"
        fi
    done
    
    echo -e "\n${BOLD}4. 权限检查${NC}"
    if [ -w "$SCRIPT_DIR" ]; then
        log_success "脚本目录可写"
    else
        log_error "脚本目录不可写: $SCRIPT_DIR"
    fi
}

# 备份配置
backup_config() {
    local backup_file="$BACKUP_DIR/config-$(date +%Y%m%d_%H%M%S).json"
    
    if [ -f "$CONFIG_FILE" ]; then
        cp "$CONFIG_FILE" "$backup_file"
        log_success "配置已备份到: $backup_file"
    else
        log_error "配置文件不存在"
        return 1
    fi
}

# 恢复配置
restore_config() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        echo -e "${BLUE}可用的备份文件:${NC}"
        ls -la "$BACKUP_DIR"/config-*.json 2>/dev/null || {
            echo "没有找到备份文件"
            return 1
        }
        echo
        read -p "请输入备份文件名: " -r backup_file
    fi
    
    if [ ! -f "$backup_file" ]; then
        # 尝试在备份目录中查找
        if [ -f "$BACKUP_DIR/$backup_file" ]; then
            backup_file="$BACKUP_DIR/$backup_file"
        else
            log_error "备份文件不存在: $backup_file"
            return 1
        fi
    fi
    
    if confirm "确认恢复配置? (当前配置将被覆盖)"; then
        cp "$backup_file" "$CONFIG_FILE"
        log_success "配置已恢复"
    fi
}

# =============================================================================
# 主函数和命令路由
# =============================================================================

# 解析参数
parse_args() {
    VERBOSE=false
    FORCE=false
    QUIET=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--verbose)
                VERBOSE=true
                DEBUG=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -q|--quiet)
                QUIET=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                break
                ;;
        esac
    done
    
    # 设置日志级别
    if [ "$VERBOSE" = "true" ]; then
        export LOG_LEVEL="DEBUG"
    elif [ "$QUIET" = "true" ]; then
        export LOG_LEVEL="ERROR"
    fi
}

# 主函数
main() {
    # 解析通用参数
    parse_args "$@"
    
    # 显示标题（除非静默模式）
    if [ "$QUIET" != "true" ]; then
        print_header
    fi
    
    local command="$1"
    shift
    
    case "$command" in
        # 初始化
        "init")
            init_config
            ;;
        
        # 仓库管理
        "add")
            add_sync_pair "$@"
            ;;
        "list")
            list_repositories
            ;;
        "remove"|"rm")
            remove_repository "$@"
            ;;
        "enable")
            toggle_repository "$1" "enable"
            ;;
        "disable")
            toggle_repository "$1" "disable"
            ;;
        
        # 同步操作
        "sync")
            execute_sync "$1" "$FORCE"
            ;;
        "status")
            sync_status
            ;;
        
        # 监控管理
        "monitor")
            "$SCRIPT_DIR/sync-monitor.sh" "$@"
            ;;
        
        # 配置管理
        "config")
            local config_cmd="$1"
            shift
            case "$config_cmd" in
                "show"|"")
                    show_config "$@"
                    ;;
                "set")
                    modify_config "$@"
                    ;;
                *)
                    show_config "$config_cmd" "$@"
                    ;;
            esac
            ;;
        
        # 日志管理
        "logs")
            local log_cmd="$1"
            shift
            case "$log_cmd" in
                "clean")
                    clean_logs "$@"
                    ;;
                *)
                    show_logs "$log_cmd" "$@"
                    ;;
            esac
            ;;
        
        # 工具命令
        "check")
            system_check
            ;;
        "backup")
            backup_config
            ;;
        "restore")
            restore_config "$@"
            ;;
        "version")
            show_version
            ;;
        
        # 帮助
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        
        # 未知命令
        *)
            echo -e "${RED}未知命令: $command${NC}"
            echo "使用 '$0 help' 查看可用命令"
            exit 1
            ;;
    esac
}

# 执行主函数
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi