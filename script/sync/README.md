# 本地仓库同步系统

## 概述

这是一个专为本地Git仓库设计的双向同步系统，支持实时监控、冲突解决和自动化同步。适用于在不同工作环境间保持代码同步，或实现本地备份策略。

## 功能特性

### 🔄 同步模式
- **单向同步**: 源仓库 → 目标仓库
- **双向同步**: 仓库A ↔ 仓库B 
- **多仓库同步**: 一对多或多对多同步
- **增量同步**: 只同步变更的文件

### 🛡️ 冲突处理
- **智能合并**: 自动处理非冲突变更
- **冲突策略**: 
  - 优先源仓库 (source-wins)
  - 优先目标仓库 (target-wins)  
  - 手动解决 (manual)
  - 保留所有版本 (keep-both)

### ⚡ 监控方式
- **实时监控**: 基于inotify的文件系统监控
- **定时同步**: Cron定时任务
- **手动同步**: 命令行触发
- **Git Hook同步**: 基于Git提交触发

### 📊 同步状态
- **状态跟踪**: 实时同步状态监控
- **历史记录**: 完整的同步操作日志
- **性能统计**: 同步速度和成功率统计
- **错误报告**: 详细的错误分析和恢复建议

## 架构设计

```
本地仓库同步系统
├── 配置管理
│   ├── 同步配置文件
│   ├── 仓库映射配置
│   └── 策略配置
├── 核心同步引擎
│   ├── Git操作封装
│   ├── 冲突检测算法
│   ├── 合并策略实现
│   └── 回滚机制
├── 监控系统
│   ├── 文件系统监控
│   ├── Git状态监控
│   └── 网络状态检查
├── 用户界面
│   ├── 命令行工具
│   ├── 配置向导
│   └── 状态查看器
└── 日志与报告
    ├── 操作日志
    ├── 错误报告
    └── 性能统计
```

## 快速开始

### 方式1: Web界面配置 (推荐)

```bash
# 安装并启动Web配置界面
cd web-ui
./install.sh
./start-ui.sh

# 浏览器访问: http://localhost:3000
```

Web界面提供：
- 🎛️ 可视化配置管理
- 📊 实时状态监控  
- 🔄 一键同步操作
- 📝 日志查看分析

### 方式2: 命令行配置

### 1. 基础配置

```bash
# 初始化同步系统
./sync-manager.sh init

# 添加同步对
./sync-manager.sh add-pair /path/to/repo1 /path/to/repo2

# 设置同步策略
./sync-manager.sh config --strategy bidirectional --conflict-resolution manual
```

### 2. 启动同步

```bash
# 一次性同步
./sync-manager.sh sync

# 启动实时监控
./sync-manager.sh monitor --daemon

# 定时同步
./sync-manager.sh schedule --interval 30m
```

### 3. 状态查看

```bash
# 查看同步状态
./sync-manager.sh status

# 查看同步历史
./sync-manager.sh history

# 查看配置信息
./sync-manager.sh config --show
```

## 配置说明

### 主配置文件 (sync-config.json)

```json
{
  "version": "1.0",
  "global": {
    "log_level": "INFO",
    "log_retention_days": 30,
    "backup_enabled": true,
    "backup_retention": 10
  },
  "repositories": [
    {
      "id": "main_backup",
      "source": "./projects/my_project",
      "target": "./backups/my_project",
      "strategy": "unidirectional",
      "conflict_resolution": "source-wins",
      "enabled": true,
      "exclude_patterns": [
        ".git/objects/**",
        "node_modules/**",
        "*.log"
      ]
    }
  ],
  "monitoring": {
    "enabled": true,
    "method": "inotify",
    "debounce_seconds": 5
  },
  "notifications": {
    "enabled": true,
    "on_error": true,
    "on_conflict": true,
    "email": "<EMAIL>"
  }
}
```

### 策略配置

| 策略类型 | 说明 | 适用场景 |
|---------|------|----------|
| `unidirectional` | 单向同步，只从源到目标 | 备份、部署 |
| `bidirectional` | 双向同步，变更会相互同步 | 多环境开发 |
| `mirror` | 镜像模式，目标完全复制源 | 完整备份 |
| `selective` | 选择性同步，基于规则过滤 | 部分同步 |

## 使用场景

### 场景1: 开发环境备份

```bash
# 配置开发环境到备份目录的单向同步
./sync-manager.sh add-pair \
  --source ./projects/dev_project \
  --target ./backups/dev_project \
  --strategy unidirectional \
  --conflict-resolution source-wins \
  --auto-start
```

### 场景2: 多机开发同步

```bash
# 配置工作机和笔记本的双向同步
./sync-manager.sh add-pair \
  --source ./projects/work_project \
  --target ./projects/laptop_project \
  --strategy bidirectional \
  --conflict-resolution manual \
  --monitor realtime
```

### 场景3: 持续集成环境

```bash
# 配置开发分支到CI环境的自动同步
./sync-manager.sh add-pair \
  --source /dev/project \
  --target /ci/project \
  --strategy unidirectional \
  --trigger "git-push" \
  --branch "develop"
```

## 故障排除

### 常见问题

1. **同步失败**
   ```bash
   # 查看详细错误
   ./sync-manager.sh status --verbose
   
   # 重置同步状态
   ./sync-manager.sh reset --repo-id main_backup
   ```

2. **冲突解决**
   ```bash
   # 查看冲突详情
   ./sync-manager.sh conflicts --show
   
   # 手动解决冲突
   ./sync-manager.sh resolve --conflict-id 12345 --strategy manual
   ```

3. **性能优化**
   ```bash
   # 查看性能统计
   ./sync-manager.sh stats
   
   # 优化配置
   ./sync-manager.sh optimize --profile performance
   ```

## 高级特性

### 1. 自定义钩子

支持在同步过程的不同阶段执行自定义脚本：

```bash
# 同步前钩子
hooks/pre-sync.sh

# 同步后钩子  
hooks/post-sync.sh

# 冲突检测钩子
hooks/on-conflict.sh
```

### 2. 插件系统

```bash
# 安装插件
./sync-manager.sh plugin install git-lfs-sync

# 启用插件
./sync-manager.sh plugin enable git-lfs-sync
```

### 3. API接口

提供RESTful API用于集成到其他系统：

```bash
# 启动API服务
./sync-manager.sh api --port 8080

# API使用示例
curl -X POST http://localhost:8080/api/sync/trigger
```

## 安全考虑

- 🔐 支持SSH密钥认证
- 🛡️ 权限检查和访问控制
- 📝 操作审计和日志记录
- 🔒 敏感信息加密存储

## 性能优化

- ⚡ 增量同步减少数据传输
- 🗜️ 智能压缩降低存储占用
- 🚀 并行处理提升同步速度
- 📈 缓存机制减少重复计算

## 支持与贡献

- 📖 详细文档: [docs/](docs/)
- 🐛 问题报告: [issues](issues/)
- 💡 功能建议: [features](features/)
- 🤝 贡献指南: [CONTRIBUTING.md](CONTRIBUTING.md)