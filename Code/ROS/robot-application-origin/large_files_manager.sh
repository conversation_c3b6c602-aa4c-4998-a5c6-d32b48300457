#!/bin/bash

# 大文件管理器 - 统一管理脚本
# 集成移动和恢复功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}🗂️  大文件管理器${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_menu() {
    echo ""
    echo -e "${BLUE}请选择操作:${NC}"
    echo "1. 🔍 查找大文件（不移动）"
    echo "2. 📦 移动大文件到备份目录"
    echo "3. 📋 列出可恢复的文件"
    echo "4. 🔄 恢复文件（交互式）"
    echo "5. ⚡ 强制恢复所有文件"
    echo "6. 📊 查看移动日志"
    echo "7. 🧹 清理备份目录"
    echo "8. ❓ 显示帮助"
    echo "0. 🚪 退出"
    echo ""
    echo -n "请输入选择 (0-8): "
}

# 查找大文件
find_large_files() {
    echo -e "${YELLOW}🔍 查找大于50M的文件...${NC}"
    echo "=================================="
    
    local count=0
    local total_size=0
    
    find . -type f -size +50M \
        -not -path "*/.git/*" \
        -not -path "*/node_modules/*" \
        -not -path "*/.cache/*" \
        -not -path "*/.npm/*" \
        -not -path "*/.yarn/*" \
        -not -path "*/build/*" \
        -not -path "*/dist/*" \
        -not -path "*/large_files_backup/*" | while read file; do
        
        file_size=$(du -h "$file" | cut -f1)
        file_size_mb=$(du -m "$file" | cut -f1)
        echo "📄 $file (大小: $file_size)"
        
        ((count++))
        ((total_size += file_size_mb))
    done
    
    echo ""
    echo "📊 找到 $count 个大文件，总大小约 ${total_size}MB"
}

# 移动大文件
move_large_files() {
    echo -e "${YELLOW}📦 移动大文件...${NC}"
    if [ -f "./move_large_files.sh" ]; then
        ./move_large_files.sh
    else
        echo -e "${RED}❌ 找不到 move_large_files.sh 脚本${NC}"
    fi
}

# 列出可恢复文件
list_restorable_files() {
    echo -e "${YELLOW}📋 列出可恢复的文件...${NC}"
    if [ -f "./restore_large_files.sh" ]; then
        ./restore_large_files.sh --list
    else
        echo -e "${RED}❌ 找不到 restore_large_files.sh 脚本${NC}"
    fi
}

# 交互式恢复
interactive_restore() {
    echo -e "${YELLOW}🔄 交互式恢复文件...${NC}"
    if [ -f "./restore_large_files.sh" ]; then
        ./restore_large_files.sh
    else
        echo -e "${RED}❌ 找不到 restore_large_files.sh 脚本${NC}"
    fi
}

# 强制恢复
force_restore() {
    echo -e "${YELLOW}⚡ 强制恢复所有文件...${NC}"
    echo -e "${RED}警告: 这将覆盖已存在的文件！${NC}"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -f "./restore_large_files.sh" ]; then
            ./restore_large_files.sh --force
        else
            echo -e "${RED}❌ 找不到 restore_large_files.sh 脚本${NC}"
        fi
    else
        echo "操作已取消"
    fi
}

# 查看移动日志
view_log() {
    echo -e "${YELLOW}📊 查看移动日志...${NC}"
    local log_file="large_files_backup/move_log.txt"
    
    if [ -f "$log_file" ]; then
        echo "=================================="
        cat "$log_file"
        echo "=================================="
        echo ""
        echo "📈 统计信息:"
        local success_count=$(grep "SUCCESS" "$log_file" | wc -l)
        local failed_count=$(grep "FAILED" "$log_file" | wc -l)
        echo "   - 成功移动: $success_count 个文件"
        echo "   - 移动失败: $failed_count 个文件"
    else
        echo -e "${YELLOW}⚠️  没有找到移动日志文件${NC}"
        echo "可能还没有执行过移动操作"
    fi
}

# 清理备份目录
clean_backup() {
    echo -e "${YELLOW}🧹 清理备份目录...${NC}"
    local backup_dir="large_files_backup"
    
    if [ -d "$backup_dir" ]; then
        echo "备份目录内容:"
        ls -la "$backup_dir"
        echo ""
        echo -e "${RED}警告: 这将永久删除备份目录及其所有内容！${NC}"
        read -p "确定要删除备份目录吗? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$backup_dir"
            echo -e "${GREEN}✅ 备份目录已删除${NC}"
        else
            echo "操作已取消"
        fi
    else
        echo -e "${YELLOW}⚠️  备份目录不存在${NC}"
    fi
}

# 显示帮助
show_help() {
    echo -e "${CYAN}❓ 大文件管理器帮助${NC}"
    echo "===================="
    echo ""
    echo "功能说明:"
    echo "1. 查找大文件 - 扫描当前目录下大于50M的文件"
    echo "2. 移动大文件 - 将大文件移动到备份目录"
    echo "3. 列出可恢复文件 - 显示备份目录中的文件"
    echo "4. 恢复文件 - 交互式恢复选定的文件"
    echo "5. 强制恢复 - 恢复所有文件，覆盖已存在的"
    echo "6. 查看日志 - 显示移动操作的详细日志"
    echo "7. 清理备份 - 删除备份目录及其内容"
    echo ""
    echo "排除目录:"
    echo "- .git (所有层级的Git仓库)"
    echo "- node_modules (Node.js依赖)"
    echo "- .cache, .npm, .yarn (缓存目录)"
    echo "- build, dist (构建输出)"
    echo ""
    echo "相关文件:"
    echo "- move_large_files.sh - 移动脚本"
    echo "- restore_large_files.sh - 恢复脚本"
    echo "- large_files_backup/ - 备份目录"
    echo "- large_files_backup/move_log.txt - 操作日志"
}

# 主循环
main() {
    while true; do
        print_header
        print_menu
        
        read choice
        echo ""
        
        case $choice in
            1) find_large_files ;;
            2) move_large_files ;;
            3) list_restorable_files ;;
            4) interactive_restore ;;
            5) force_restore ;;
            6) view_log ;;
            7) clean_backup ;;
            8) show_help ;;
            0) 
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请输入 0-8${NC}"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..." -r
    done
}

# 检查依赖脚本
check_dependencies() {
    local missing=false
    
    if [ ! -f "move_large_files.sh" ]; then
        echo -e "${RED}❌ 缺少 move_large_files.sh${NC}"
        missing=true
    fi
    
    if [ ! -f "restore_large_files.sh" ]; then
        echo -e "${RED}❌ 缺少 restore_large_files.sh${NC}"
        missing=true
    fi
    
    if [ "$missing" = true ]; then
        echo ""
        echo "请确保以下文件存在于当前目录:"
        echo "- move_large_files.sh"
        echo "- restore_large_files.sh"
        exit 1
    fi
}

# 启动程序
check_dependencies
main
