# Git Worktree 快速切换工具

这个工具集提供了便捷的 Git Worktree 管理功能，让你可以快速在多个分支之间切换而无需频繁的 stash 和 checkout 操作。

## 文件说明

- `worktree-manager.sh` - 完整的 worktree 管理工具
- `wt.sh` - 快速切换工具（推荐日常使用）
- `worktree-config.sh` - 分支别名配置文件

## 快速开始

### 1. 基本使用

```bash
# 切换到 master 分支（自动创建 worktree）
./wt.sh master

# 切换到 dev-1.1.0 分支
./wt.sh dev-1.1.0

# 列出所有 worktrees
./wt.sh list

# 删除指定 worktree
./wt.sh remove dev-1.1.0
```

### 2. 使用别名（推荐）

首先加载 worktree 工具：

```bash
# 方法1: 使用安装脚本（推荐）
source ./setup-wt.sh

# 方法2: 直接加载 wt.sh
source ./wt.sh

# 方法3: 添加到 shell 配置文件以永久启用
echo 'source /mine/robot-application-origin/wt.sh' >> ~/.bashrc
source ~/.bashrc
```

然后就可以使用简短的命令：

```bash
# 使用别名快速切换
wt m        # 切换到 master
wt d        # 切换到 dev-1.1.0
wt u        # 切换到 unitree-debug
wt x        # 切换到 xuhui

# 列出 worktrees
wtlist

# 删除 worktree
wtremove dev-1.1.0
```

### 3. 配置的别名

当前配置的分支别名：

| 别名 | 对应分支 |
|------|----------|
| m, main | master |
| d, dev | dev-1.1.0 |
| std | dev-1.1.0-std |
| pre, sale | pre-sale |
| u, unitree | unitree-debug |
| x | xuhui |
| xu | xuhui-unitree |
| debug | debug |

## 高级功能

### 完整管理工具

使用 `worktree-manager.sh` 获得更多功能：

```bash
# 显示帮助
./worktree-manager.sh help

# 添加 worktree 并指定目录名
./worktree-manager.sh add dev-1.1.0 my-dev

# 查看所有 worktree 状态
./worktree-manager.sh status

# 清理无效的 worktrees
./worktree-manager.sh clean
```

### 自定义别名

编辑 `worktree-config.sh` 文件来添加你自己的分支别名：

```bash
# 在 BRANCH_ALIASES 数组中添加新的别名
["my"]="my-feature-branch"
["test"]="test-branch"
```

## 工作原理

1. **Worktree 目录结构**：
   ```
   robot-application-unitree/          # 主仓库
   ├── worktree-manager.sh
   ├── wt.sh
   └── worktree-config.sh
   
   ../worktrees/                       # worktree 目录
   ├── master/                         # master 分支的 worktree
   ├── dev-1.1.0/                     # dev-1.1.0 分支的 worktree
   └── unitree-debug/                  # unitree-debug 分支的 worktree
   ```

2. **自动创建**：如果指定的分支 worktree 不存在，工具会自动创建它
3. **智能分支检测**：自动从本地分支、origin 或 gitlab-remote 创建 worktree
4. **状态显示**：切换时显示当前分支和未提交更改

## 优势

- **无需 stash**：每个分支都有独立的工作目录
- **快速切换**：秒级切换，无需重新编译或重新加载
- **保持状态**：每个分支的工作状态都被保留
- **IDE 友好**：IDE 可以同时打开多个 worktree
- **简单易用**：支持别名，命令简短

## 注意事项

1. 每个 worktree 都会占用磁盘空间
2. 建议定期清理不需要的 worktrees
3. 共享的文件（如 .git/config）会影响所有 worktrees
4. 不要手动删除 worktree 目录，使用 `git worktree remove` 命令

## 故障排除

### 常见问题

1. **分支不存在**：
   ```bash
   # 查看所有可用分支
   git branch -a
   ```

2. **Worktree 创建失败**：
   ```bash
   # 清理无效的 worktrees
   ./worktree-manager.sh clean
   ```

3. **目录权限问题**：
   ```bash
   # 确保脚本有执行权限
   chmod +x wt.sh worktree-manager.sh
   ```

## 示例工作流

```bash
# 1. 在 master 分支开发
wt m
# 进行一些开发工作...

# 2. 需要紧急修复 bug，切换到 unitree-debug
wt u
# 修复 bug...

# 3. 回到 master 继续之前的工作
wt m
# 之前的工作状态完全保留

# 4. 开始新功能开发
wt dev
# 在 dev-1.1.0 分支开发新功能...
```
