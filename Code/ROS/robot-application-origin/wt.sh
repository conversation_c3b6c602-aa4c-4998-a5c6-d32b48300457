#!/bin/bash

# 快速 Git Worktree 切换工具
# 用法: ./wt.sh <分支名>
# 或者: source wt.sh 然后使用 wt <分支名>

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKTREE_BASE_DIR="/mine/worktrees"

# 加载配置文件
if [[ -f "$SCRIPT_DIR/worktree-config.sh" ]]; then
    source "$SCRIPT_DIR/worktree-config.sh"
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 快速切换函数
wt() {
    local branch_input="$1"

    # 检查是否在 git 仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${RED}错误: 当前目录不是 Git 仓库${NC}"
        return 1
    fi

    if [[ -z "$branch_input" ]]; then
        echo -e "${YELLOW}用法: wt <分支名或别名>${NC}"
        echo -e "${BLUE}可用的 worktrees:${NC}"
        if [[ -d "$WORKTREE_BASE_DIR" ]]; then
            ls -1 "$WORKTREE_BASE_DIR" 2>/dev/null | sed 's/^/  /'
        else
            echo "  无可用的 worktrees"
        fi
        echo ""
        if command -v show_aliases &> /dev/null; then
            show_aliases
        fi
        return 1
    fi

    # 解析别名
    local branch_name="$branch_input"
    if command -v resolve_branch_alias &> /dev/null; then
        branch_name=$(resolve_branch_alias "$branch_input")
        if [[ "$branch_name" != "$branch_input" ]]; then
            echo -e "${BLUE}别名 '$branch_input' -> '$branch_name'${NC}"
        fi
    fi
    
    local worktree_path="$WORKTREE_BASE_DIR/$branch_name"
    
    # 如果 worktree 不存在，尝试创建
    if [[ ! -d "$worktree_path" ]]; then
        echo -e "${YELLOW}Worktree '$branch_name' 不存在，正在创建...${NC}"

        # 创建目录
        mkdir -p "$WORKTREE_BASE_DIR"

        # 检查当前分支是否就是目标分支
        current_branch=$(git branch --show-current)
        if [[ "$current_branch" == "$branch_name" ]]; then
            echo -e "${BLUE}当前主仓库已在 '$branch_name' 分支，直接使用主仓库${NC}"
            cd "$SCRIPT_DIR"
            echo -e "${GREEN}已在主仓库的 '$branch_name' 分支${NC}"
            return 0
        fi

        # 检查分支是否已在其他 worktree 中被检出
        if git worktree list | grep -q "\[$branch_name\]"; then
            existing_path=$(git worktree list | grep "\[$branch_name\]" | awk '{print $1}')
            echo -e "${BLUE}分支 '$branch_name' 已在 worktree 中: $existing_path${NC}"

            # 检查目录是否真的存在
            if [[ -d "$existing_path" ]]; then
                cd "$existing_path"
                echo -e "${GREEN}切换到现有 worktree: $existing_path${NC}"
                return 0
            else
                echo -e "${YELLOW}警告: worktree 目录不存在，正在清理并重新创建...${NC}"
                git worktree prune
            fi
        fi

        # 检查分支是否存在并创建 worktree
        if git show-ref --verify --quiet refs/heads/"$branch_name"; then
            git worktree add "$worktree_path" "$branch_name"
        elif git show-ref --verify --quiet refs/remotes/origin/"$branch_name"; then
            git worktree add "$worktree_path" -b "$branch_name" "origin/$branch_name"
        elif git show-ref --verify --quiet refs/remotes/gitlab-remote/"$branch_name"; then
            git worktree add "$worktree_path" -b "$branch_name" "gitlab-remote/$branch_name"
        else
            echo -e "${RED}错误: 分支 '$branch_name' 不存在${NC}"
            return 1
        fi

        if [[ $? -ne 0 ]]; then
            echo -e "${RED}创建 worktree 失败${NC}"
            return 1
        fi
    fi
    
    echo -e "${GREEN}切换到: $worktree_path${NC}"
    cd "$worktree_path"
    
    # 显示当前分支和状态
    echo -e "${BLUE}当前分支: $(git branch --show-current)${NC}"
    
    # 如果有未提交的更改，显示简要状态
    if ! git diff-index --quiet HEAD --; then
        echo -e "${YELLOW}有未提交的更改:${NC}"
        git status --short
    fi
}

# 列出所有 worktrees
wtlist() {
    echo -e "${BLUE}所有 Git Worktrees:${NC}"
    git worktree list
    echo ""
    echo -e "${BLUE}可用分支别名:${NC}"
    if command -v show_aliases &> /dev/null; then
        show_aliases
    else
        echo "  (未加载别名配置)"
    fi
}

# 删除 worktree
wtremove() {
    local branch_name="$1"
    if [[ -z "$branch_name" ]]; then
        echo -e "${YELLOW}用法: wtremove <分支名>${NC}"
        return 1
    fi

    local worktree_path="$WORKTREE_BASE_DIR/$branch_name"
    if [[ -d "$worktree_path" ]]; then
        git worktree remove "$worktree_path"
        echo -e "${GREEN}已删除 worktree: $branch_name${NC}"
    else
        echo -e "${RED}Worktree '$branch_name' 不存在${NC}"
    fi
}

# 显示当前位置和状态
wtstatus() {
    echo -e "${BLUE}=== Git Worktree 状态 ===${NC}"
    echo ""

    # 显示当前位置
    local current_path=$(pwd)
    local current_branch=$(git branch --show-current 2>/dev/null || echo "未知")

    echo -e "${YELLOW}当前位置:${NC} $current_path"
    echo -e "${YELLOW}当前分支:${NC} $current_branch"
    echo ""

    # 显示所有 worktrees
    echo -e "${BLUE}所有 Worktrees:${NC}"
    git worktree list | while IFS= read -r line; do
        local path=$(echo "$line" | awk '{print $1}')
        if [[ "$path" == "$current_path" ]]; then
            echo -e "${GREEN}→ $line${NC} ${YELLOW}(当前)${NC}"
        else
            echo "  $line"
        fi
    done

    # 显示未提交的更改
    if ! git diff-index --quiet HEAD -- 2>/dev/null; then
        echo ""
        echo -e "${YELLOW}当前分支有未提交的更改:${NC}"
        git status --short
    fi
}

# 如果脚本被直接执行（而不是被 source）
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 检查是否在 git 仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${RED}错误: 当前目录不是 Git 仓库${NC}"
        exit 1
    fi
    
    case "$1" in
        "list"|"ls")
            wtlist
            ;;
        "remove"|"rm")
            wtremove "$2"
            ;;
        "status"|"st")
            wtstatus
            ;;
        "")
            echo -e "${YELLOW}快速 Git Worktree 工具${NC}"
            echo ""
            echo "用法:"
            echo "  $0 <分支名>        - 切换到指定分支 (自动创建 worktree)"
            echo "  $0 list           - 列出所有 worktrees 和别名"
            echo "  $0 status         - 显示当前状态和位置"
            echo "  $0 remove <分支名> - 删除指定 worktree"
            echo ""
            echo "或者 source 此脚本后使用:"
            echo "  wt <分支名>       - 快速切换"
            echo "  wtlist           - 列出 worktrees"
            echo "  wtstatus         - 显示状态"
            echo "  wtremove <分支名> - 删除 worktree"
            echo ""
            echo "支持的别名: t(test), m(master), d(dev-1.1.0), u(unitree-debug), x(xuhui)"
            ;;
        *)
            wt "$1"
            ;;
    esac
fi
