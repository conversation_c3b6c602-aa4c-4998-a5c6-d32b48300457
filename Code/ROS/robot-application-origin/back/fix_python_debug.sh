#!/bin/bash

echo "正在修复 Python 被 Cursor 劫持的问题..."

# 清除 Cursor 相关的环境变量
unset APPIMAGE
unset CURSOR_TRACE_ID
unset BUNDLED_DEBUGPY_PATH

# 清除 LD_LIBRARY_PATH 中的 Cursor 路径
export LD_LIBRARY_PATH=$(echo $LD_LIBRARY_PATH | tr ':' '\n' | grep -v "Cursor" | grep -v "\.mount_" | tr '\n' ':' | sed 's/:$//')

# 强制使用系统 Python
export PATH=/usr/bin:$PATH

echo "修复后的环境变量："
echo "PATH: $PATH"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

echo ""
echo "测试 Python 解释器："
/usr/bin/python3.10 -c "import sys; print('Python executable:', sys.executable)"

echo ""
echo "修复完成！现在请在 VSCode 中："
echo "1. 按 Ctrl+Shift+P 打开命令面板"
echo "2. 输入 'Python: Select Interpreter'"
echo "3. 选择 '/usr/bin/python3.10'"
echo "4. 重新加载 VSCode 窗口"
echo "5. 尝试设置断点并调试" 