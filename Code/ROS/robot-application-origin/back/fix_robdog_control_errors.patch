--- a/src/robdog_control/src/robdogCenter/robdog_center_mgr.cpp
+++ b/src/robdog_control/src/robdogCenter/robdog_center_mgr.cpp
@@ -836,7 +836,7 @@ void RobdogCenter::sendChargeMarkResponse(int code, const std::string& msg) {
     Json::Value body;
     body["code"] = code;
     body["msg"] = msg;
-    body["mapId"] = std::stoll(charge_pile_id);
+    body["mapId"] = Json::Value(Json::Int64(std::stoll(charge_pile_id)));
     response["body"] = body;
 
     Json::FastWriter writer;
@@ -876,7 +876,7 @@ void RobdogCenter::startTimeout(std::function<void()> timeoutCallback, std::funct
         }
         std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 每100毫秒检查一次
     }
-    if (waiting_flag) {
+    if (waiting_flag.load()) {
         waiting_flag = false;
         timeoutCallback(); // 执行超时回调函数
     }
@@ -1093,7 +1093,7 @@ void RobdogCenter::initEventHandlers() {
     eventHandlers_ = {
         {"robot_action", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotAction(inValue); }},
         {"robot_move", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotMove(body); }},
-        {"robot_view", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotView(body); }},
+        {"robot_view", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { (void)inValue; (void)name; handleRobotView(body); }},
         {"mode_set", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleModeSet(inValue); }},
         {"properties_write", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePropertiesWrite(inValue); }},
         {"properties_read", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePropertiesRead(inValue); }},
@@ -1102,7 +1102,7 @@ void RobdogCenter::initEventHandlers() {
         {"phone_call", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePhoneCall(body); }},
         {"user_connect_change", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUserConnectChange(body,name); }},
         {"map_draw", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapDraw(body); }},
-        {"data_update", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDataUpdate(inValue, body); }},
+        {"data_update", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { (void)name; handleDataUpdate(inValue, body); }},
         {"move_points", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMovePoints(inValue); }},
         {"point_report", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePointReport(body); }},
         {"remind_ontime", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRemindOnTime(body); }},
@@ -1114,7 +1114,7 @@ void RobdogCenter::initEventHandlers() {
         {"navigation_request", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleNavigationRequest(inValue); }},
         {"trip_simple_query", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTripSimpleQuery(inValue); }},
         {"bind_status_response", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleBindStatusResponse(body); }},
-        {"coord_report", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleCoordReportRequest(body); }},
+        {"coord_report", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { (void)inValue; (void)name; handleCoordReportRequest(body); }},
         {"device_settings_read", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDeviceSettingResponse(body); }},
         {"map_reposition", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapReposition(inValue); }},
         {"robot_skill_organize", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotSkillOrganize(body); }},
@@ -1124,7 +1124,7 @@ void RobdogCenter::initEventHandlers() {
         {"charging_action",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleChargingAction(body);}},
         {"create_trace_action",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTraceTrip(inValue);}},
         {"data_report_ctl",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDataReportCtl(inValue);}},
-        {"trace_trip_request",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTracetripNaviRequest(inValue);}},
+        {"trace_trip_request",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { (void)body; (void)name; handleTracetripNaviRequest(inValue);}},
         {"unknow", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) {}}
     };
 }
@@ -1416,7 +1416,7 @@ void RobdogCenter::handleInteractionSkills(const Json::Value& jBody) {
 }
 
 void RobdogCenter::handleGuardDone(const Json::Value &jBody) {
-    // TODO: 实现守卫完成处理逻辑
+    (void)jBody; // TODO: 实现守卫完成处理逻辑
 }
 
 void RobdogCenter::handleChargeMark(const Json::Value& jBody) {
@@ -1901,7 +1901,7 @@ void RobdogCenter::handleChargeMark(const Json::Value& jBody) {
     }
     
     Json::Value response;
-    int actionInt;
+    // int actionInt; // 未使用的变量
     
     response["deviceId"] = RobotState::getInstance().getDeviceId();
     response["domain"] = "DEVICE_MAP";
@@ -2512,7 +2512,7 @@ void RobdogCenter::handleBindNotify(const Json::Value& jBody) {
 }
 
 void RobdogCenter::handleUnbindNotify(const Json::Value &jBody) {
-    // TODO: 实现解绑通知处理逻辑
+    (void)jBody; // TODO: 实现解绑通知处理逻辑
 }
 
 void RobdogCenter::handleUnbindNotifyVoice(const Json::Value &jBody) {
@@ -3814,8 +3814,8 @@ void RobdogCenter::setProperties(const Json::Value& jBody) {
         }
         case 2: { // WiFi开关
             std::string wifiSwitch = jBody["value"].asString();
-            int value = (RobotState::getInstance().getWifiSwitch() == "on") ? 1 : 0;
-            int exvalue=(RobotState::getInstance().getMobileDataSwitch() == "on") ? 1 : 0;
+            // int value = (RobotState::getInstance().getWifiSwitch() == "on") ? 1 : 0;
+            // int exvalue=(RobotState::getInstance().getMobileDataSwitch() == "on") ? 1 : 0;
             // TODO: 实现WiFi开关设置
             break;
         }
@@ -4082,7 +4082,7 @@ void RobdogCenter::parseWsActionMsg(Json::Value& value) {
         }
         case 2: { // 导航相关
             Json::Value params = value["params"];
-            int type = params["type"].asInt();
+            // int type = params["type"].asInt(); // 未使用的变量
             // TODO: 实现导航相关处理
             break;
         }
@@ -4139,7 +4139,7 @@ void RobdogCenter::handleMapCompleteStatus(int taskStatusCode, const Json::Value
 }
 
 void RobdogCenter::handleMapInitialStatus(int taskStatusCode, const Json::Value& msgValue) {
-    // TODO: 实现地图初始化状态处理
+    (void)msgValue; // TODO: 实现地图初始化状态处理
     if (isInitializationFailure(taskStatusCode)) {
         RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL);
         sendMapInitialResponse(taskStatusCode, getDescription(taskStatusCode));
@@ -4217,7 +4217,7 @@ void RobdogCenter::handleChargeMarkStatus(int taskStatusCode, const Json::Value&
 }
 
 void RobdogCenter::handleChargeActionStatus(int taskStatusCode, const Json::Value& msgValue) {
-    // TODO: 实现充电动作状态处理
+    (void)msgValue; // TODO: 实现充电动作状态处理
     if (isChargeMarkFailure(taskStatusCode)) {
         RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL);
         sendChargeMarkResponse(taskStatusCode, getDescription(taskStatusCode));
@@ -4728,7 +4728,7 @@ void RobdogCenter::checkDeliverCakeStatus(const Json::Value &jBody) {
 }
 
 void RobdogCenter::checkBatteryStatus(const Json::Value &jBody) {
-    // TODO: 实现电池状态检查
+    (void)jBody; // TODO: 实现电池状态检查
 }
 
 void RobdogCenter::checkGoHomeStatus(const Json::Value &jBody) {
@@ -5010,7 +5010,7 @@ void RobdogCenter::SendBrocastCallback() {
         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start Brocasting: %s, Pub times: %d, Total Count_: %d", 
-            brocast_text, brocast_send_count_, brocast_total_count_);
+            brocast_text.c_str(), brocast_send_count_, brocast_total_count_);
     }
 }
 
@@ -5085,7 +5085,7 @@ void RobdogCenter::checkNvidia_srv_status(bool openNavigationIfClosed, std::strin
 }
 
 void RobdogCenter::checkNvidiaServiceStatus(bool openNavigationIfClosed,std::string mapId) {
-    // TODO: 实现NVIDIA服务状态检查
+    (void)openNavigationIfClosed; (void)mapId; // TODO: 实现NVIDIA服务状态检查
 }
 
 --- a/src/robdog_control/src/robotTrip/trip.cpp
+++ b/src/robdog_control/src/robotTrip/trip.cpp
@@ -429,8 +429,8 @@ void RobdogCenter::check_pos_timeout() {
                 RCLCPP_WARN(node_->get_logger(), 
                     "Timeout triggered! Last move time: %lld, Current time: %lld",
-                    std::chrono::duration_cast<std::chrono::seconds>(last_move_time_.time_since_epoch()).count(),
-                    std::chrono::duration_cast<std::chrono::seconds>(Clock::now().time_since_epoch()).count());
+                    (long long)std::chrono::duration_cast<std::chrono::seconds>(last_move_time_.time_since_epoch()).count(),
+                    (long long)std::chrono::duration_cast<std::chrono::seconds>(Clock::now().time_since_epoch()).count());
             }
             timeout_triggered_ = true;
             setTripStatus(ABNORMAL);
@@ -449,7 +449,7 @@ void RobdogCenter::actionTripAbnormal(int checkResultFail){
         RCLCPP_WARN(node_->get_logger(), "ERROR_TRIP Code:%d,Error Message: %s ", 
-            error.errorTripCode,error.errorMessage);
+            error.errorTripCode,error.errorMessage.c_str());
     }
 }
 
@@ -624,7 +624,7 @@ void RobdogCenter::traceRecordDynamicRep(){
     body["traceTaskId"]=(Json::Int64)getTripId();
     body["checkResult"]=getTripErrorCode();
-    body["status"]=(getTripStatus()==TRACE_GOING?GOING:getTripStatus());
+    body["status"]=Json::Value(Json::Int64(getTripStatus()==TRACE_GOING?GOING:getTripStatus()));
     body["maxDistance"] = MAXDIS;
     response["body"] = body;
     Json::FastWriter writer;
``` 
</rewritten_file>