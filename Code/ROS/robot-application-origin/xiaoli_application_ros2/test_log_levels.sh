#!/bin/bash

# 测试日志级别配置的脚本
# 用于验证多种日志级别配置方式

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🧪 测试日志级别配置功能${NC}"
echo -e "${BLUE}================================${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置环境
source install/setup.bash

echo -e "${YELLOW}测试1: 命令行参数 --log-level DEBUG${NC}"
timeout 3s python3 src/network/network/network_node.py --ros-args --params-file src/launch_package/configs/robot_config.yaml --log-level DEBUG || true
echo ""

echo -e "${YELLOW}测试2: 环境变量 ROS_LOG_LEVEL=WARN${NC}"
timeout 3s env ROS_LOG_LEVEL=WARN python3 src/network/network/network_node.py --ros-args --params-file src/launch_package/configs/robot_config.yaml || true
echo ""

echo -e "${YELLOW}测试3: 配置文件默认级别 (INFO)${NC}"
timeout 3s python3 src/network/network/network_node.py --ros-args --params-file src/launch_package/configs/robot_config.yaml || true
echo ""

echo -e "${YELLOW}测试4: 优先级测试 - 命令行覆盖环境变量${NC}"
timeout 3s env ROS_LOG_LEVEL=ERROR python3 src/network/network/network_node.py --ros-args --params-file src/launch_package/configs/robot_config.yaml --log-level DEBUG || true
echo ""

echo -e "${GREEN}✅ 日志级别配置测试完成${NC}"
echo -e "${GREEN}📊 支持的配置方式：${NC}"
echo -e "${GREEN}  1. 命令行参数 --log-level (最高优先级)${NC}"
echo -e "${GREEN}  2. 环境变量 ROS_LOG_LEVEL${NC}"
echo -e "${GREEN}  3. 配置文件 robot_config.yaml${NC}"
echo -e "${GREEN}  4. 默认值 INFO (最低优先级)${NC}"
