# Network Node 调试完整指南

## 🎯 调试目标
`xiaoli_application_ros2/src/network/network/network_node.py`

## 📦 第一步：编译依赖（必须）

### 自动编译（推荐）
```bash
cd xiaoli_application_ros2
./debug_network_node.sh --test
```
脚本会自动检查和构建所有必需的依赖包。

### 手动编译
```bash
cd xiaoli_application_ros2

# 1. 编译homi_speech_interface包（必需依赖）
colcon build --packages-select homi_speech_interface --cmake-args -DCMAKE_EXPORT_COMPILE_COMMANDS=1

# 2. 编译network包
colcon build --packages-select network --cmake-args -DCMAKE_EXPORT_COMPILE_COMMANDS=1

# 3. 设置环境
source install/setup.bash
```

## 🚀 第二步：配置文件设置

### 默认配置文件
调试使用的配置文件：`src/launch_package/configs/robot_config.yaml`

### 关键配置参数
```yaml
network_node:
  ros__parameters:
    log_level: "INFO"                       # 日志级别 (DEBUG|INFO|WARN|ERROR|FATAL)
    wifi_connect_interface: "wlp1s0"        # WiFi接口
    p2p_connect_interface: "wlan1"          # AP热点接口
    mobile_connect_interface: "enx00e04c6801d0"  # 蜂窝网络接口
    ssid: "xiaoli51"                        # AP热点名称
    static_ip: "***********"                # 静态IP地址
    device_type: "yes"                      # 设备类型
    cellular_option: "disable"              # 蜂窝网络选项
    timer_interval: 20                      # 定时器间隔
    dns_primary_servers: ["*********", "************", "***************"]
    dns_backup_servers: ["************", "*******", "*******"]
```

## 📊 日志级别配置（多种方式）

### 配置方式和优先级
1. **命令行参数** (最高优先级)
2. **环境变量**
3. **配置文件参数**
4. **默认值INFO** (最低优先级)

### 方式一：命令行参数
```bash
# 调试脚本方式
./debug_network_node.sh --log-level DEBUG
./debug_network_node.sh --log-level INFO

# 直接运行方式
python3 src/network/network/network_node.py --log-level DEBUG
```

### 方式二：环境变量
```bash
# 设置环境变量
export ROS_LOG_LEVEL=DEBUG
./debug_network_node.sh

# 或者使用脚本参数
./debug_network_node.sh --env-log-level DEBUG
```

### 方式三：配置文件
修改 `src/launch_package/configs/robot_config.yaml`:
```yaml
network_node:
  ros__parameters:
    log_level: "DEBUG"  # 修改这里
```

### 方式四：VS Code调试配置
- "Debug Network Node" - 使用配置文件级别
- "Debug Network Node (DEBUG Level)" - 强制DEBUG级别
- "Debug Network Node (ENV Log Level)" - 使用环境变量级别
- "Debug Network Node (Custom Config)" - 可选择级别

### 支持的日志级别
- **DEBUG**: 详细调试信息，包含所有日志
- **INFO**: 一般信息，默认级别
- **WARN**: 警告信息
- **ERROR**: 错误信息
- **FATAL**: 致命错误信息

## 🚀 第三步：开始调试

### 方法一：一键调试脚本（最简单）
```bash
cd xiaoli_application_ros2
./debug_network_node.sh                    # 使用配置文件日志级别
./debug_network_node.sh --test             # 测试模式
./debug_network_node.sh --log-level DEBUG  # 命令行指定日志级别
./debug_network_node.sh --env-log-level INFO --test  # 环境变量+测试模式
```

### 方法二：VS Code图形调试（最强大）
1. 确保已完成编译步骤
2. 在VS Code中打开 `src/network/network/network_node.py`
3. 设置断点（推荐第46、136、200行）
4. 按 `F5` 启动调试
5. 选择调试配置：
   - "Debug Network Node" - 使用配置文件日志级别
   - "Debug Network Node (Test Mode)" - 测试模式
   - "Debug Network Node (DEBUG Level)" - 强制DEBUG级别
   - "Debug Network Node (ENV Log Level)" - 使用环境变量级别
   - "Debug Network Node (Custom Config)" - 选择自定义配置文件

## 🔧 第四步：调试技巧

### 关键断点位置
```python
# network_node.py 第46行 - 节点初始化
def __init__(self):
    super().__init__('network_node')
    # 在这里设置断点查看初始化过程

# network_node.py 第136行 - 服务创建
self.service = self.create_service(NetCtrl, '/homi_speech/network_service', self.handle_network_status)
# 在这里设置断点查看服务创建

# network_node.py 第200行 - 服务请求处理
def handle_network_status(self, request, response):
    # 在这里设置断点查看网络请求处理
```

### 调试控制台命令
在VS Code调试控制台中可以执行：
```python
# 查看网络配置
self.network_manager.config

# 查看网络接口
self.wifi_interface
self.mobile_interface
self.ap_interface

# 查看配置参数
self.static_ip
self.ssid
self.device_type
self.dns_primary_servers

# 查看节点状态
self.get_logger().info("调试信息")
```

### 环境变量
- `NETWORK_DEBUG_MODE=1` - 启用调试模式
- `NETWORK_TEST_MODE=1` - 启用测试模式（模拟硬件）
- `ROS_DOMAIN_ID=0` - ROS2域ID

## 📁 配置文件说明

### 调试脚本
- `debug_network_node.sh` - 一键调试脚本

### VS Code配置
- `.vscode/launch.json` - 调试启动配置（含自动编译）
- `.vscode/settings.json` - Python路径设置
- `.vscode/tasks.json` - 编译任务配置

## 🐛 调试技巧

### 1. 设置断点
在VS Code中点击行号左侧设置断点，或在代码中添加：
```python
import pdb; pdb.set_trace()  # 调试断点
```

### 2. 查看变量
- 在调试面板中查看局部变量和全局变量
- 使用调试控制台执行Python表达式
- 监视特定变量的值变化

### 3. 调试网络状态
```python
# 在network_manager.py中添加调试代码
def check_network_status(self):
    self.logger.debug("🔍 开始检查网络状态")
    # 设置断点查看网络接口状态
    import pdb; pdb.set_trace()
    
    wifi_connected = self.is_interface_connected(self.wifi_interface)
    mobile_connected = self.is_interface_connected(self.mobile_interface)
    
    self.logger.debug(f"WiFi: {wifi_connected}, Mobile: {mobile_connected}")
```

### 4. 模拟测试环境
测试模式下会跳过实际的硬件操作，适合开发调试：
```bash
export NETWORK_TEST_MODE=1
python3 debug_network.py --test-mode
```

## 📊 日志调试

### 日志级别
- `DEBUG` - 详细调试信息
- `INFO` - 一般信息
- `WARNING` - 警告信息
- `ERROR` - 错误信息

### 日志文件位置
- 调试日志: `debug_network.log`
- ROS2日志: `$ROS_LOG_DIR/`

## 🔍 常见问题解决

### 1. 编译错误
```bash
# 清理并重新编译
rm -rf build/ install/
colcon build --packages-select homi_speech_interface network --cmake-args -DCMAKE_EXPORT_COMPILE_COMMANDS=1
```

### 2. 导入错误
```bash
# 确保环境正确设置
source install/setup.bash
export PYTHONPATH="$PWD/src:$PWD/install/homi_speech_interface/local/lib/python3.10/dist-packages:$PYTHONPATH"
```

### 3. 类型支持错误
```bash
# 确保库路径正确
export LD_LIBRARY_PATH="$PWD/install/homi_speech_interface/lib:$LD_LIBRARY_PATH"
```

### 4. 权限问题
某些网络操作需要root权限，测试模式下会跳过这些操作。

## 🚀 高级调试

### 远程调试
可以配置远程调试连接到目标设备：
```python
import debugpy
debugpy.listen(5678)
debugpy.wait_for_client()
```

### 性能分析
使用cProfile进行性能分析：
```bash
python3 -m cProfile -o network_profile.prof debug_network.py
```

## 📝 调试日志示例

```
2025-07-21 10:30:15 - NetworkNode - INFO - 🚀 初始化 networkNode 节点
2025-07-21 10:30:15 - NetworkNode - INFO - 🐛 调试模式已启用
2025-07-21 10:30:15 - NetworkNode - INFO - 🧪 测试模式已启用 - 将模拟硬件操作
2025-07-21 10:30:15 - NetworkManager - DEBUG - 🔍 开始检查网络状态
2025-07-21 10:30:15 - NetworkManager - DEBUG - WiFi接口wlan0状态: connected
2025-07-21 10:30:15 - NetworkManager - DEBUG - 移动网络接口eth1状态: disconnected
```

## ✅ 验证调试环境

运行以下命令验证环境：
```bash
cd xiaoli_application_ros2
./debug_network_node.sh --test
```

成功输出示例：
```
🚀 Network Node Debug - 最优解
================================
📦 检查和构建依赖包...
✅ homi_speech_interface包已存在
✅ network包已存在
🔧 设置ROS2环境...
🧪 测试模式已启用
🐛 启动network_node.py调试...
[INFO] [network_node]: 初始化 networkNode 节点
[INFO] [network_node]: 🐛 调试模式已启用
[INFO] [network_node]: 🧪 测试模式已启用
```

## 🎯 解决的关键问题

✅ **导入问题** - 修复了相对导入和绝对导入路径
✅ **服务依赖** - 自动构建homi_speech_interface包
✅ **类型支持** - 正确设置C++类型支持库路径
✅ **环境配置** - 完整的ROS2环境变量设置
✅ **真实服务** - 使用真实的NetCtrl和SIGCData服务
✅ **自动编译** - VS Code调试前自动编译依赖

---
**现在你可以开始调试了！** 🎉
