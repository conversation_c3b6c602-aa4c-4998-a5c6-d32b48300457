#!/bin/bash

# Network Node Debug Script - 最优解
# 自动构建homi_speech_interface并启动network_node.py调试

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 Network Node Debug - 最优解${NC}"
echo -e "${BLUE}================================${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查并构建依赖包
echo -e "${YELLOW}📦 检查和构建依赖包...${NC}"

# 检查homi_speech_interface包
if [ ! -d "install/homi_speech_interface" ]; then
    echo -e "${YELLOW}🔨 构建homi_speech_interface包...${NC}"
    colcon build --packages-select homi_speech_interface --cmake-args -DCMAKE_EXPORT_COMPILE_COMMANDS=1
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ homi_speech_interface构建失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ homi_speech_interface构建成功${NC}"
else
    echo -e "${GREEN}✅ homi_speech_interface包已存在${NC}"
fi

# 检查network包
if [ ! -d "install/network" ]; then
    echo -e "${YELLOW}🔨 构建network包...${NC}"
    colcon build --packages-select network --cmake-args -DCMAKE_EXPORT_COMPILE_COMMANDS=1
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ network包构建失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ network包构建成功${NC}"
else
    echo -e "${GREEN}✅ network包已存在${NC}"
fi

# 检查是否需要重新构建（源码比安装文件新）
if [ "src/network/network/network_node.py" -nt "install/network" ] || [ "src/homi_speech_interface" -nt "install/homi_speech_interface" ]; then
    echo -e "${YELLOW}🔄 检测到源码更新，重新构建相关包...${NC}"
    colcon build --packages-select homi_speech_interface network --cmake-args -DCMAKE_EXPORT_COMPILE_COMMANDS=1
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 重新构建失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 重新构建成功${NC}"
fi

# 设置环境
echo -e "${YELLOW}🔧 设置ROS2环境...${NC}"
source install/setup.bash

# 设置调试环境变量
export ROS_DOMAIN_ID=0
export NETWORK_DEBUG_MODE=1
export RCUTILS_LOGGING_BUFFERED_STREAM=1
export RCUTILS_COLORIZED_OUTPUT=1

# 确保库路径正确
export LD_LIBRARY_PATH="$SCRIPT_DIR/install/homi_speech_interface/lib:$LD_LIBRARY_PATH"
export PYTHONPATH="$SCRIPT_DIR/src:$SCRIPT_DIR/install/homi_speech_interface/local/lib/python3.10/dist-packages:$PYTHONPATH"

# 设置配置文件路径
CONFIG_FILE="$SCRIPT_DIR/src/launch_package/configs/robot_config.yaml"
if [ -f "$CONFIG_FILE" ]; then
    echo -e "${GREEN}📋 配置文件: $CONFIG_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  配置文件不存在: $CONFIG_FILE${NC}"
    echo -e "${YELLOW}   将使用默认参数${NC}"
fi

echo -e "${GREEN}📚 LD_LIBRARY_PATH: $LD_LIBRARY_PATH${NC}"
echo -e "${GREEN}🐍 PYTHONPATH: $PYTHONPATH${NC}"

# 解析命令行参数
DEBUG_MODE=""
TEST_MODE=""
LOG_LEVEL="INFO"
USE_ENV_LOG_LEVEL=false
EXTRA_ARGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            DEBUG_MODE="--debug"
            echo -e "${YELLOW}🐛 启用断点调试模式${NC}"
            shift
            ;;
        --test)
            TEST_MODE="--test-mode"
            export NETWORK_TEST_MODE=1
            echo -e "${YELLOW}🧪 启用测试模式${NC}"
            shift
            ;;
        --log-level)
            LOG_LEVEL="$2"
            EXTRA_ARGS="$EXTRA_ARGS --log-level $LOG_LEVEL"
            echo -e "${YELLOW}📊 命令行日志级别: $LOG_LEVEL${NC}"
            shift 2
            ;;
        --env-log-level)
            LOG_LEVEL="$2"
            export ROS_LOG_LEVEL="$LOG_LEVEL"
            echo -e "${YELLOW}📊 环境变量日志级别: $LOG_LEVEL${NC}"
            shift 2
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --debug           启用断点调试模式"
            echo "  --test            启用测试模式（模拟硬件）"
            echo "  --log-level LEVEL 设置命令行日志级别 (DEBUG|INFO|WARN|ERROR|FATAL)"
            echo "  --env-log-level LEVEL 设置环境变量日志级别"
            echo "  --help            显示此帮助信息"
            echo ""
            echo "日志级别配置优先级："
            echo "  1. 命令行参数 --log-level (最高)"
            echo "  2. 环境变量 ROS_LOG_LEVEL"
            echo "  3. 配置文件 robot_config.yaml"
            echo "  4. 默认值 INFO (最低)"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 未知参数: $1${NC}"
            exit 1
            ;;
    esac
done

echo -e "${GREEN}📁 工作目录: $SCRIPT_DIR${NC}"
echo -e "${GREEN}🎯 目标文件: src/network/network/network_node.py${NC}"

# 启动调试
echo -e "${BLUE}🐛 启动network_node.py调试...${NC}"
echo -e "${BLUE}================================${NC}"

# 使用配置文件启动节点
if [ -f "$CONFIG_FILE" ]; then
    echo -e "${GREEN}🚀 使用配置文件启动节点${NC}"
    python3 src/network/network/network_node.py --ros-args --params-file "$CONFIG_FILE" $EXTRA_ARGS
else
    echo -e "${YELLOW}🚀 使用默认参数启动节点${NC}"
    python3 src/network/network/network_node.py $EXTRA_ARGS
fi

echo -e "${GREEN}✅ 调试会话结束${NC}"
