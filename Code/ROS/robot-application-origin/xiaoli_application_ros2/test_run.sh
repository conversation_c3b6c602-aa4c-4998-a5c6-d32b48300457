#!/bin/bash

# =============================================================================
# 运行功能测试脚本
# =============================================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🧪 测试运行功能${NC}"
echo "=================================="

# 测试1: 基础编译脚本运行功能
echo -e "${YELLOW}测试1: 基础编译脚本运行功能${NC}"
echo "测试命令: ./build.sh -r network --run-args '--help'"
echo "预期结果: 编译network包并运行，显示帮助信息"
echo ""

# 测试2: 高级编译脚本运行功能
echo -e "${YELLOW}测试2: 高级编译脚本运行功能${NC}"
echo "测试命令: ./build_advanced.sh -r network --run-args '--help'"
echo "预期结果: 编译network包并运行，显示帮助信息"
echo ""

# 测试3: 运行所有包
echo -e "${YELLOW}测试3: 运行所有包${NC}"
echo "测试命令: ./build.sh -r"
echo "预期结果: 编译所有包并运行主程序"
echo ""

# 测试4: 带参数的运行
echo -e "${YELLOW}测试4: 带参数的运行${NC}"
echo "测试命令: ./build.sh -r network --run-args '--test-mode --debug'"
echo "预期结果: 编译network包并以测试模式运行"
echo ""

echo -e "${GREEN}✅ 测试说明完成${NC}"
echo ""
echo "要执行测试，请运行以下命令："
echo "1. ./build.sh -r network --run-args '--help'"
echo "2. ./build_advanced.sh -r network --run-args '--help'"
echo "3. ./build.sh -r"
echo "4. ./build.sh -r network --run-args '--test-mode --debug'"
