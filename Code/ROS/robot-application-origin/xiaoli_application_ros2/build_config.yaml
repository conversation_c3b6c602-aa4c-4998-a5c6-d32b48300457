# =============================================================================
# ROS2 编译配置文件
# 支持多架构、多版本、多编译选项配置
# =============================================================================

# 全局配置
global:
  default_ros_distro: "humble"
  default_arch: "x86_64"
  default_build_type: "Release"
  default_jobs: 4
  log_level: "INFO"

# 支持的ROS2版本配置
ros_distros:
  humble:
    version: "2.0.0"
    supported_archs: ["x86_64", "aarch64", "arm64"]
    cmake_args: ["-DCMAKE_BUILD_TYPE=Release", "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"]
    python_version: "3.10"
    
  iron:
    version: "2.1.0"
    supported_archs: ["x86_64", "aarch64", "arm64"]
    cmake_args: ["-DCMAKE_BUILD_TYPE=Release", "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"]
    python_version: "3.10"
    
  rolling:
    version: "2.2.0"
    supported_archs: ["x86_64", "aarch64", "arm64"]
    cmake_args: ["-DCMAKE_BUILD_TYPE=Release", "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"]
    python_version: "3.10"
    
  galactic:
    version: "1.0.0"
    supported_archs: ["x86_64", "aarch64", "arm64"]
    cmake_args: ["-DCMAKE_BUILD_TYPE=Release", "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"]
    python_version: "3.8"
    
  foxy:
    version: "0.9.0"
    supported_archs: ["x86_64", "aarch64", "arm64"]
    cmake_args: ["-DCMAKE_BUILD_TYPE=Release", "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"]
    python_version: "3.8"

# 架构特定配置
architectures:
  x86_64:
    compiler: "gcc"
    compiler_flags: ["-march=native", "-mtune=native"]
    cmake_generator: "Unix Makefiles"
    cross_compile: false
    
  aarch64:
    compiler: "gcc"
    compiler_flags: ["-march=armv8-a"]
    cmake_generator: "Unix Makefiles"
    cross_compile: false
    
  arm64:
    compiler: "gcc"
    compiler_flags: ["-march=armv8-a"]
    cmake_generator: "Unix Makefiles"
    cross_compile: false
    
  armv7l:
    compiler: "gcc"
    compiler_flags: ["-march=armv7-a", "-mfpu=neon", "-mfloat-abi=hard"]
    cmake_generator: "Unix Makefiles"
    cross_compile: false
    
  armv8l:
    compiler: "gcc"
    compiler_flags: ["-march=armv8-a"]
    cmake_generator: "Unix Makefiles"
    cross_compile: false

# 构建类型配置
build_types:
  Release:
    cmake_args: ["-DCMAKE_BUILD_TYPE=Release", "-DNDEBUG"]
    compiler_flags: ["-O2", "-DNDEBUG"]
    debug_info: false
    
  Debug:
    cmake_args: ["-DCMAKE_BUILD_TYPE=Debug"]
    compiler_flags: ["-g", "-O0", "-DDEBUG"]
    debug_info: true
    
  RelWithDebInfo:
    cmake_args: ["-DCMAKE_BUILD_TYPE=RelWithDebInfo"]
    compiler_flags: ["-g", "-O2"]
    debug_info: true
    
  MinSizeRel:
    cmake_args: ["-DCMAKE_BUILD_TYPE=MinSizeRel"]
    compiler_flags: ["-Os", "-DNDEBUG"]
    debug_info: false

# 包特定配置
packages:
  network:
    dependencies: ["homi_speech_interface"]
    build_type: "Release"
    cmake_args: ["-DNETWORK_DEBUG=OFF"]
    test_enabled: true
    
  homi_speech_interface:
    dependencies: []
    build_type: "Release"
    cmake_args: ["-DHOMI_SPEECH_DEBUG=OFF"]
    test_enabled: true
    
  launch_package:
    dependencies: ["network", "homi_speech_interface"]
    build_type: "Release"
    cmake_args: []
    test_enabled: false

# 编译器配置
compilers:
  gcc:
    name: "GNU Compiler Collection"
    version_check: "gcc --version"
    default_flags: ["-Wall", "-Wextra", "-Wpedantic"]
    
  clang:
    name: "Clang Compiler"
    version_check: "clang --version"
    default_flags: ["-Wall", "-Wextra", "-Wpedantic"]
    
  clangd:
    name: "Clangd Language Server"
    version_check: "clangd --version"
    config_file: ".clangd"

# 测试配置
testing:
  enabled: true
  timeout: 300  # 秒
  parallel: true
  coverage: false
  report_format: "xml"

# 文档配置
documentation:
  enabled: true
  generator: "doxygen"
  output_dir: "docs"
  include_diagrams: true
  include_examples: true

# 安装配置
installation:
  prefix: "/usr/local"
  lib_dir: "lib"
  include_dir: "include"
  bin_dir: "bin"
  share_dir: "share"

# 环境变量配置
environment:
  ROS_DOMAIN_ID: 0
  ROS_LOCALHOST_ONLY: false
  RCUTILS_CONSOLE_OUTPUT_FORMAT: "[{severity}] [{name}]: {message}"
  RCUTILS_LOGGING_BUFFERED_STREAM: 1

# 调试配置
debugging:
  gdb_enabled: true
  valgrind_enabled: false
  sanitizer_enabled: false
  profiling_enabled: false

# 性能配置
performance:
  parallel_build: true
  parallel_test: true
  cache_enabled: true
  ccache_enabled: false

# 安全配置
security:
  sanitizer: false
  stack_protector: true
  fortify_source: true
  pie_enabled: true

# 日志配置
logging:
  level: "INFO"
  format: "colored"
  file_enabled: true
  file_path: "build.log"
  max_size: "10MB"
  max_files: 5 