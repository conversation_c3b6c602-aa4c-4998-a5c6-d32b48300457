// generated from rosidl_adapter/resource/action.idl.em
// with input from follow_msgs/action/Followcfg.action
// generated code does not contain a copyright notice


module follow_msgs {
  module action {
    @verbatim (language="comment", text=
      "Request")
    struct Followcfg_Goal {
      string request;
    };
    @verbatim (language="comment", text=
      "Result")
    struct Followcfg_Result {
      string result;
    };
    @verbatim (language="comment", text=
      "Feedback")
    struct Followcfg_Feedback {
      string feedback;
    };
  };
};
