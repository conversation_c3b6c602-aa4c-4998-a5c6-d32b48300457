set(_AMENT_PACKAGE_NAME "expression")
set(expression_VERSION "0.0.0")
set(expression_MAINTAINER "root <<EMAIL>>")
set(expression_BUILD_DEPENDS "rclcpp")
set(expression_BUILDTOOL_DEPENDS "ament_cmake")
set(expression_BUILD_EXPORT_DEPENDS "rclcpp")
set(expression_BUILDTOOL_EXPORT_DEPENDS )
set(expression_EXEC_DEPENDS "rclcpp")
set(expression_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(expression_GROUP_DEPENDS )
set(expression_MEMBER_OF_GROUPS )
set(expression_DEPRECATED "")
set(expression_EXPORT_TAGS)
list(APPEND expression_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
