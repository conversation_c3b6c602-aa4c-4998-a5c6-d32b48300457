# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/CMakeFiles /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named andlink_uninstall

# Build rule for target.
andlink_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 andlink_uninstall
.PHONY : andlink_uninstall

# fast build rule for target.
andlink_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_uninstall.dir/build.make CMakeFiles/andlink_uninstall.dir/build
.PHONY : andlink_uninstall/fast

#=============================================================================
# Target rules for targets named andlink_node

# Build rule for target.
andlink_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 andlink_node
.PHONY : andlink_node

# fast build rule for target.
andlink_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/build
.PHONY : andlink_node/fast

src/adapt/andlink_adapt.o: src/adapt/andlink_adapt.c.o
.PHONY : src/adapt/andlink_adapt.o

# target to build an object file
src/adapt/andlink_adapt.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_adapt.c.o
.PHONY : src/adapt/andlink_adapt.c.o

src/adapt/andlink_adapt.i: src/adapt/andlink_adapt.c.i
.PHONY : src/adapt/andlink_adapt.i

# target to preprocess a source file
src/adapt/andlink_adapt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_adapt.c.i
.PHONY : src/adapt/andlink_adapt.c.i

src/adapt/andlink_adapt.s: src/adapt/andlink_adapt.c.s
.PHONY : src/adapt/andlink_adapt.s

# target to generate assembly for a file
src/adapt/andlink_adapt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_adapt.c.s
.PHONY : src/adapt/andlink_adapt.c.s

src/adapt/andlink_app_test.o: src/adapt/andlink_app_test.c.o
.PHONY : src/adapt/andlink_app_test.o

# target to build an object file
src/adapt/andlink_app_test.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_app_test.c.o
.PHONY : src/adapt/andlink_app_test.c.o

src/adapt/andlink_app_test.i: src/adapt/andlink_app_test.c.i
.PHONY : src/adapt/andlink_app_test.i

# target to preprocess a source file
src/adapt/andlink_app_test.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_app_test.c.i
.PHONY : src/adapt/andlink_app_test.c.i

src/adapt/andlink_app_test.s: src/adapt/andlink_app_test.c.s
.PHONY : src/adapt/andlink_app_test.s

# target to generate assembly for a file
src/adapt/andlink_app_test.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_app_test.c.s
.PHONY : src/adapt/andlink_app_test.c.s

src/adapt/andlink_app_utils.o: src/adapt/andlink_app_utils.c.o
.PHONY : src/adapt/andlink_app_utils.o

# target to build an object file
src/adapt/andlink_app_utils.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_app_utils.c.o
.PHONY : src/adapt/andlink_app_utils.c.o

src/adapt/andlink_app_utils.i: src/adapt/andlink_app_utils.c.i
.PHONY : src/adapt/andlink_app_utils.i

# target to preprocess a source file
src/adapt/andlink_app_utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_app_utils.c.i
.PHONY : src/adapt/andlink_app_utils.c.i

src/adapt/andlink_app_utils.s: src/adapt/andlink_app_utils.c.s
.PHONY : src/adapt/andlink_app_utils.s

# target to generate assembly for a file
src/adapt/andlink_app_utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_app_utils.c.s
.PHONY : src/adapt/andlink_app_utils.c.s

src/adapt/andlink_control_adapt.o: src/adapt/andlink_control_adapt.c.o
.PHONY : src/adapt/andlink_control_adapt.o

# target to build an object file
src/adapt/andlink_control_adapt.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_control_adapt.c.o
.PHONY : src/adapt/andlink_control_adapt.c.o

src/adapt/andlink_control_adapt.i: src/adapt/andlink_control_adapt.c.i
.PHONY : src/adapt/andlink_control_adapt.i

# target to preprocess a source file
src/adapt/andlink_control_adapt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_control_adapt.c.i
.PHONY : src/adapt/andlink_control_adapt.c.i

src/adapt/andlink_control_adapt.s: src/adapt/andlink_control_adapt.c.s
.PHONY : src/adapt/andlink_control_adapt.s

# target to generate assembly for a file
src/adapt/andlink_control_adapt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_control_adapt.c.s
.PHONY : src/adapt/andlink_control_adapt.c.s

src/adapt/andlink_ota_adapt.o: src/adapt/andlink_ota_adapt.c.o
.PHONY : src/adapt/andlink_ota_adapt.o

# target to build an object file
src/adapt/andlink_ota_adapt.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_ota_adapt.c.o
.PHONY : src/adapt/andlink_ota_adapt.c.o

src/adapt/andlink_ota_adapt.i: src/adapt/andlink_ota_adapt.c.i
.PHONY : src/adapt/andlink_ota_adapt.i

# target to preprocess a source file
src/adapt/andlink_ota_adapt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_ota_adapt.c.i
.PHONY : src/adapt/andlink_ota_adapt.c.i

src/adapt/andlink_ota_adapt.s: src/adapt/andlink_ota_adapt.c.s
.PHONY : src/adapt/andlink_ota_adapt.s

# target to generate assembly for a file
src/adapt/andlink_ota_adapt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_ota_adapt.c.s
.PHONY : src/adapt/andlink_ota_adapt.c.s

src/adapt/andlink_wifi_adapt.o: src/adapt/andlink_wifi_adapt.c.o
.PHONY : src/adapt/andlink_wifi_adapt.o

# target to build an object file
src/adapt/andlink_wifi_adapt.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_wifi_adapt.c.o
.PHONY : src/adapt/andlink_wifi_adapt.c.o

src/adapt/andlink_wifi_adapt.i: src/adapt/andlink_wifi_adapt.c.i
.PHONY : src/adapt/andlink_wifi_adapt.i

# target to preprocess a source file
src/adapt/andlink_wifi_adapt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_wifi_adapt.c.i
.PHONY : src/adapt/andlink_wifi_adapt.c.i

src/adapt/andlink_wifi_adapt.s: src/adapt/andlink_wifi_adapt.c.s
.PHONY : src/adapt/andlink_wifi_adapt.s

# target to generate assembly for a file
src/adapt/andlink_wifi_adapt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/andlink_wifi_adapt.c.s
.PHONY : src/adapt/andlink_wifi_adapt.c.s

src/adapt/wpa_suppliant_api.o: src/adapt/wpa_suppliant_api.c.o
.PHONY : src/adapt/wpa_suppliant_api.o

# target to build an object file
src/adapt/wpa_suppliant_api.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/wpa_suppliant_api.c.o
.PHONY : src/adapt/wpa_suppliant_api.c.o

src/adapt/wpa_suppliant_api.i: src/adapt/wpa_suppliant_api.c.i
.PHONY : src/adapt/wpa_suppliant_api.i

# target to preprocess a source file
src/adapt/wpa_suppliant_api.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/wpa_suppliant_api.c.i
.PHONY : src/adapt/wpa_suppliant_api.c.i

src/adapt/wpa_suppliant_api.s: src/adapt/wpa_suppliant_api.c.s
.PHONY : src/adapt/wpa_suppliant_api.s

# target to generate assembly for a file
src/adapt/wpa_suppliant_api.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/wpa_suppliant_api.c.s
.PHONY : src/adapt/wpa_suppliant_api.c.s

src/adapt/xxx_adapt.o: src/adapt/xxx_adapt.c.o
.PHONY : src/adapt/xxx_adapt.o

# target to build an object file
src/adapt/xxx_adapt.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/xxx_adapt.c.o
.PHONY : src/adapt/xxx_adapt.c.o

src/adapt/xxx_adapt.i: src/adapt/xxx_adapt.c.i
.PHONY : src/adapt/xxx_adapt.i

# target to preprocess a source file
src/adapt/xxx_adapt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/xxx_adapt.c.i
.PHONY : src/adapt/xxx_adapt.c.i

src/adapt/xxx_adapt.s: src/adapt/xxx_adapt.c.s
.PHONY : src/adapt/xxx_adapt.s

# target to generate assembly for a file
src/adapt/xxx_adapt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/adapt/xxx_adapt.c.s
.PHONY : src/adapt/xxx_adapt.c.s

src/andlink_node.o: src/andlink_node.cpp.o
.PHONY : src/andlink_node.o

# target to build an object file
src/andlink_node.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/andlink_node.cpp.o
.PHONY : src/andlink_node.cpp.o

src/andlink_node.i: src/andlink_node.cpp.i
.PHONY : src/andlink_node.i

# target to preprocess a source file
src/andlink_node.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/andlink_node.cpp.i
.PHONY : src/andlink_node.cpp.i

src/andlink_node.s: src/andlink_node.cpp.s
.PHONY : src/andlink_node.s

# target to generate assembly for a file
src/andlink_node.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/andlink_node.cpp.s
.PHONY : src/andlink_node.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/andlink_node.dir/build.make CMakeFiles/andlink_node.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... andlink_uninstall"
	@echo "... uninstall"
	@echo "... andlink_node"
	@echo "... src/adapt/andlink_adapt.o"
	@echo "... src/adapt/andlink_adapt.i"
	@echo "... src/adapt/andlink_adapt.s"
	@echo "... src/adapt/andlink_app_test.o"
	@echo "... src/adapt/andlink_app_test.i"
	@echo "... src/adapt/andlink_app_test.s"
	@echo "... src/adapt/andlink_app_utils.o"
	@echo "... src/adapt/andlink_app_utils.i"
	@echo "... src/adapt/andlink_app_utils.s"
	@echo "... src/adapt/andlink_control_adapt.o"
	@echo "... src/adapt/andlink_control_adapt.i"
	@echo "... src/adapt/andlink_control_adapt.s"
	@echo "... src/adapt/andlink_ota_adapt.o"
	@echo "... src/adapt/andlink_ota_adapt.i"
	@echo "... src/adapt/andlink_ota_adapt.s"
	@echo "... src/adapt/andlink_wifi_adapt.o"
	@echo "... src/adapt/andlink_wifi_adapt.i"
	@echo "... src/adapt/andlink_wifi_adapt.s"
	@echo "... src/adapt/wpa_suppliant_api.o"
	@echo "... src/adapt/wpa_suppliant_api.i"
	@echo "... src/adapt/wpa_suppliant_api.s"
	@echo "... src/adapt/xxx_adapt.o"
	@echo "... src/adapt/xxx_adapt.i"
	@echo "... src/adapt/xxx_adapt.s"
	@echo "... src/andlink_node.o"
	@echo "... src/andlink_node.i"
	@echo "... src/andlink_node.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

