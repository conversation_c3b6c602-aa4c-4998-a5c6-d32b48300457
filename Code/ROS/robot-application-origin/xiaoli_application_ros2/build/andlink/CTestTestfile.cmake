# CMake generated Testfile for 
# Source directory: /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink
# Build directory: /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(cppcheck "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/cppcheck.xunit.xml" "--package-name" "andlink" "--output-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/ament_cppcheck/cppcheck.txt" "--command" "/opt/ros/humble/bin/ament_cppcheck" "--xunit-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/cppcheck.xunit.xml" "--include_dirs" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/include/andlink" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/include")
set_tests_properties(cppcheck PROPERTIES  LABELS "cppcheck;linter" TIMEOUT "300" WORKING_DIRECTORY "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/ament_cmake_cppcheck/cmake/ament_cppcheck.cmake;66;ament_add_test;/opt/ros/humble/share/ament_cmake_cppcheck/cmake/ament_cmake_cppcheck_lint_hook.cmake;87;ament_cppcheck;/opt/ros/humble/share/ament_cmake_cppcheck/cmake/ament_cmake_cppcheck_lint_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;21;ament_execute_extensions;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake;66;ament_execute_extensions;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;61;ament_package;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;0;")
add_test(lint_cmake "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/lint_cmake.xunit.xml" "--package-name" "andlink" "--output-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/ament_lint_cmake/lint_cmake.txt" "--command" "/opt/ros/humble/bin/ament_lint_cmake" "--xunit-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/lint_cmake.xunit.xml")
set_tests_properties(lint_cmake PROPERTIES  LABELS "lint_cmake;linter" TIMEOUT "60" WORKING_DIRECTORY "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/ament_cmake_lint_cmake/cmake/ament_lint_cmake.cmake;47;ament_add_test;/opt/ros/humble/share/ament_cmake_lint_cmake/cmake/ament_cmake_lint_cmake_lint_hook.cmake;21;ament_lint_cmake;/opt/ros/humble/share/ament_cmake_lint_cmake/cmake/ament_cmake_lint_cmake_lint_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;21;ament_execute_extensions;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake;66;ament_execute_extensions;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;61;ament_package;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;0;")
add_test(uncrustify "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/uncrustify.xunit.xml" "--package-name" "andlink" "--output-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/ament_uncrustify/uncrustify.txt" "--command" "/opt/ros/humble/bin/ament_uncrustify" "--xunit-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/uncrustify.xunit.xml")
set_tests_properties(uncrustify PROPERTIES  LABELS "uncrustify;linter" TIMEOUT "60" WORKING_DIRECTORY "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/ament_cmake_uncrustify/cmake/ament_uncrustify.cmake;70;ament_add_test;/opt/ros/humble/share/ament_cmake_uncrustify/cmake/ament_cmake_uncrustify_lint_hook.cmake;43;ament_uncrustify;/opt/ros/humble/share/ament_cmake_uncrustify/cmake/ament_cmake_uncrustify_lint_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;21;ament_execute_extensions;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake;66;ament_execute_extensions;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;61;ament_package;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;0;")
add_test(xmllint "/usr/bin/python3" "-u" "/opt/ros/humble/share/ament_cmake_test/cmake/run_test.py" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/xmllint.xunit.xml" "--package-name" "andlink" "--output-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/ament_xmllint/xmllint.txt" "--command" "/opt/ros/humble/bin/ament_xmllint" "--xunit-file" "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/andlink/test_results/andlink/xmllint.xunit.xml")
set_tests_properties(xmllint PROPERTIES  LABELS "xmllint;linter" TIMEOUT "60" WORKING_DIRECTORY "/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink" _BACKTRACE_TRIPLES "/opt/ros/humble/share/ament_cmake_test/cmake/ament_add_test.cmake;125;add_test;/opt/ros/humble/share/ament_cmake_xmllint/cmake/ament_xmllint.cmake;50;ament_add_test;/opt/ros/humble/share/ament_cmake_xmllint/cmake/ament_cmake_xmllint_lint_hook.cmake;18;ament_xmllint;/opt/ros/humble/share/ament_cmake_xmllint/cmake/ament_cmake_xmllint_lint_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;21;ament_execute_extensions;/opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_package_hook.cmake;0;;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake;48;include;/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake;66;ament_execute_extensions;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;61;ament_package;/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/andlink/CMakeLists.txt;0;")
