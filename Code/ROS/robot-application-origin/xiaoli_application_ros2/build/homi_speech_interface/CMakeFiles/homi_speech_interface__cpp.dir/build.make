# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/homi_speech_interface

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface

# Utility rule file for homi_speech_interface__cpp.

# Include any custom commands dependencies for this target.
include CMakeFiles/homi_speech_interface__cpp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/homi_speech_interface__cpp.dir/progress.make

CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/trakerimageinfo.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/follow_image_info.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/target_vel_info.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/sigc_event.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/robotcfg.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/robot_cmd.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/assistant_event.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/continue_move.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/exec_step.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/iot_control_param.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/jpeg_stream.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/new_udp_connect.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/pcm_stream.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/proper_to_app.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/propriety_set.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/robdog_action.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/robdog_state.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/task.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/task_step.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/wakeup.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/live_stream_task.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/task_status.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_abort.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_ctrl.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_quiet.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_speech_text.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_take_photo.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/end_pcm_player.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/end_qt_video.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/end_video_stream.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/force_idr.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_pcm_player.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_player_status.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_qt_video.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_video_stream.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/iot_control.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/net_ctrl.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/ntrip_account.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/phone_call.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/play_wav.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/rtc_emotion_change.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/set_diy_word.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/set_nr_mode.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/set_wake_event.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/sigc_data.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/upload_image.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/upload_image_url.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/play_file.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/peripherals_ctrl.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/peripherals_status.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/ultrasonic_ctrl.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__builder.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__struct.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__traits.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__type_support.hpp
CMakeFiles/homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/rosidl_generator_cpp__visibility_control.hpp

rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/lib/rosidl_generator_cpp/rosidl_generator_cpp
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/local/lib/python3.10/dist-packages/rosidl_generator_cpp/__init__.py
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/action__builder.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/action__struct.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/action__traits.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/idl.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/idl__builder.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/idl__struct.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/idl__traits.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/msg__builder.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/msg__struct.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/msg__traits.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/srv__builder.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/srv__struct.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/rosidl_generator_cpp/resource/srv__traits.hpp.em
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/action/Followcfg.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/Trakerimageinfo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/FollowImageInfo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/TargetVelInfo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/SIGCEvent.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/action/Robotcfg.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/action/RobotCmd.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/AssistantEvent.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/ContinueMove.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/ExecStep.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/IotControlParam.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/JpegStream.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/NewUdpConnect.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/PCMStream.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/ProperToApp.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/ProprietySet.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/RobdogAction.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/RobdogState.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/Task.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/TaskStep.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/Wakeup.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/LiveStreamTask.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/msg/TaskStatus.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/AssistantAbort.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/AssistantCtrl.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/AssistantQuiet.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/AssistantSpeechText.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/AssistantTakePhoto.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/EndPcmPlayer.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/EndQtVideo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/EndVideoStream.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/ForceIDR.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/GetPcmPlayer.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/GetPlayerStatus.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/GetQtVideo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/GetVideoStream.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/IotControl.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/NetCtrl.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/NtripAccount.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/PhoneCall.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/PlayWav.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/RtcEmotionChange.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/SetDiyWord.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/SetNrMode.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/SetWakeEvent.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/SIGCData.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/UploadImage.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/UploadImageUrl.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/PlayFile.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/PeripheralsCtrl.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/PeripheralsStatus.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: rosidl_adapter/homi_speech_interface/srv/UltrasonicCtrl.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/builtin_interfaces/msg/Duration.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/builtin_interfaces/msg/Time.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Accel.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/AccelStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/AccelWithCovariance.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/AccelWithCovarianceStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Inertia.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/InertiaStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Point.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Point32.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PointStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Polygon.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PolygonInstance.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PolygonInstanceStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PolygonStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Pose.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Pose2D.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PoseArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PoseStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PoseWithCovariance.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/PoseWithCovarianceStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Quaternion.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/QuaternionStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Transform.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/TransformStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Twist.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/TwistStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/TwistWithCovariance.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/TwistWithCovarianceStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Vector3.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Vector3Stamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/VelocityStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/Wrench.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/geometry_msgs/msg/WrenchStamped.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Bool.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Byte.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/ByteMultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Char.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/ColorRGBA.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Empty.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Float32.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Float32MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Float64.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Float64MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Header.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int16.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int16MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int32.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int32MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int64.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int64MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int8.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/Int8MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/MultiArrayDimension.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/MultiArrayLayout.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/String.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt16.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt16MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt32.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt32MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt64.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt64MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt8.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/std_msgs/msg/UInt8MultiArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/BatteryState.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/CameraInfo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/ChannelFloat32.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/CompressedImage.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/FluidPressure.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/Illuminance.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/Image.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/Imu.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/JointState.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/Joy.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/JoyFeedback.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/JoyFeedbackArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/LaserEcho.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/LaserScan.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/MagneticField.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/MultiDOFJointState.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/MultiEchoLaserScan.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/NavSatFix.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/NavSatStatus.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/PointCloud.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/PointCloud2.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/PointField.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/Range.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/RegionOfInterest.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/RelativeHumidity.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/Temperature.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/msg/TimeReference.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/sensor_msgs/srv/SetCameraInfo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/action_msgs/msg/GoalInfo.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/action_msgs/msg/GoalStatus.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/action_msgs/msg/GoalStatusArray.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/action_msgs/srv/CancelGoal.idl
rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp: /opt/ros/humble/share/unique_identifier_msgs/msg/UUID.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating C++ code for ROS interfaces"
	/usr/bin/python3 /opt/ros/humble/share/rosidl_generator_cpp/cmake/../../../lib/rosidl_generator_cpp/rosidl_generator_cpp --generator-arguments-file /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/rosidl_generator_cpp__arguments.json

rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__builder.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__struct.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__traits.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/trakerimageinfo.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/trakerimageinfo.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/follow_image_info.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/follow_image_info.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/target_vel_info.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/target_vel_info.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/sigc_event.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/sigc_event.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/action/robotcfg.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/robotcfg.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__builder.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__struct.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__traits.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/action/robot_cmd.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/robot_cmd.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__builder.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__struct.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__traits.hpp

rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/assistant_event.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/assistant_event.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/continue_move.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/continue_move.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/exec_step.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/exec_step.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/iot_control_param.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/iot_control_param.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/jpeg_stream.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/jpeg_stream.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/new_udp_connect.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/new_udp_connect.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/pcm_stream.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/pcm_stream.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/proper_to_app.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/proper_to_app.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/propriety_set.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/propriety_set.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/robdog_action.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/robdog_action.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/robdog_state.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/robdog_state.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/task.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/task.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/task_step.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/task_step.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/wakeup.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/wakeup.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/live_stream_task.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/live_stream_task.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/msg/task_status.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/task_status.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__builder.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__struct.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__traits.hpp

rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/assistant_abort.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/assistant_abort.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/assistant_ctrl.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/assistant_ctrl.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/assistant_quiet.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/assistant_quiet.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/assistant_speech_text.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/assistant_speech_text.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/assistant_take_photo.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/assistant_take_photo.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/end_pcm_player.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/end_pcm_player.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/end_qt_video.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/end_qt_video.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/end_video_stream.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/end_video_stream.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/force_idr.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/force_idr.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/get_pcm_player.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/get_pcm_player.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/get_player_status.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/get_player_status.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/get_qt_video.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/get_qt_video.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/get_video_stream.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/get_video_stream.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/iot_control.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/iot_control.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/net_ctrl.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/net_ctrl.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/ntrip_account.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/ntrip_account.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/phone_call.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/phone_call.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/play_wav.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/play_wav.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/rtc_emotion_change.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/rtc_emotion_change.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/set_diy_word.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/set_diy_word.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/set_nr_mode.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/set_nr_mode.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/set_wake_event.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/set_wake_event.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/sigc_data.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/sigc_data.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/upload_image.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/upload_image.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/upload_image_url.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/upload_image_url.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/play_file.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/play_file.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/peripherals_ctrl.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/peripherals_ctrl.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/peripherals_status.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/peripherals_status.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__type_support.hpp

rosidl_generator_cpp/homi_speech_interface/srv/ultrasonic_ctrl.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/ultrasonic_ctrl.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__builder.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__builder.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__struct.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__struct.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__traits.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__traits.hpp

rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__type_support.hpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__type_support.hpp

homi_speech_interface__cpp: CMakeFiles/homi_speech_interface__cpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/robot_cmd.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/action/robotcfg.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/assistant_event.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/continue_move.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/exec_step.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/follow_image_info.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/iot_control_param.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/jpeg_stream.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/live_stream_task.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/new_udp_connect.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/pcm_stream.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/proper_to_app.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/propriety_set.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/robdog_action.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/robdog_state.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/sigc_event.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/target_vel_info.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/task.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/task_status.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/task_step.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/trakerimageinfo.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/msg/wakeup.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_abort.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_ctrl.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_quiet.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_speech_text.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/assistant_take_photo.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__builder.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__struct.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__traits.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__type_support.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/end_pcm_player.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/end_qt_video.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/end_video_stream.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/force_idr.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_pcm_player.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_player_status.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_qt_video.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/get_video_stream.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/iot_control.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/net_ctrl.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/ntrip_account.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/peripherals_ctrl.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/peripherals_status.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/phone_call.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/play_file.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/play_wav.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/rtc_emotion_change.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/set_diy_word.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/set_nr_mode.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/set_wake_event.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/sigc_data.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/ultrasonic_ctrl.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/upload_image.hpp
homi_speech_interface__cpp: rosidl_generator_cpp/homi_speech_interface/srv/upload_image_url.hpp
homi_speech_interface__cpp: CMakeFiles/homi_speech_interface__cpp.dir/build.make
.PHONY : homi_speech_interface__cpp

# Rule to build all files generated by this target.
CMakeFiles/homi_speech_interface__cpp.dir/build: homi_speech_interface__cpp
.PHONY : CMakeFiles/homi_speech_interface__cpp.dir/build

CMakeFiles/homi_speech_interface__cpp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/homi_speech_interface__cpp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/homi_speech_interface__cpp.dir/clean

CMakeFiles/homi_speech_interface__cpp.dir/depend:
	cd /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/homi_speech_interface /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/homi_speech_interface /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles/homi_speech_interface__cpp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/homi_speech_interface__cpp.dir/depend

