file(REMOVE_RECURSE
  "CMakeFiles/homi_speech_interface__cpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/followcfg__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robot_cmd__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/detail/robotcfg__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/followcfg.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/robot_cmd.hpp"
  "rosidl_generator_cpp/homi_speech_interface/action/robotcfg.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/assistant_event.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/continue_move.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/assistant_event__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/continue_move__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/exec_step__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/follow_image_info__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/iot_control_param__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/jpeg_stream__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/live_stream_task__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/new_udp_connect__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/pcm_stream__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/proper_to_app__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/propriety_set__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_action__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/robdog_state__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/sigc_event__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/target_vel_info__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_status__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/task_step__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/trakerimageinfo__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/detail/wakeup__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/exec_step.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/follow_image_info.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/iot_control_param.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/jpeg_stream.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/live_stream_task.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/new_udp_connect.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/pcm_stream.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/proper_to_app.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/propriety_set.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/robdog_action.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/robdog_state.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/sigc_event.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/target_vel_info.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/task.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/task_status.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/task_step.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/trakerimageinfo.hpp"
  "rosidl_generator_cpp/homi_speech_interface/msg/wakeup.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/assistant_abort.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/assistant_ctrl.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/assistant_quiet.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/assistant_speech_text.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/assistant_take_photo.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_abort__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_ctrl__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_quiet__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_speech_text__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/assistant_take_photo__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_pcm_player__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_qt_video__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/end_video_stream__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/force_idr__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_pcm_player__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_player_status__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_qt_video__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/get_video_stream__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/iot_control__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/net_ctrl__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ntrip_account__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_ctrl__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/peripherals_status__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/phone_call__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_file__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/play_wav__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/rtc_emotion_change__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_diy_word__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_nr_mode__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/set_wake_event__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/sigc_data__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/ultrasonic_ctrl__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__builder.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__struct.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__traits.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/detail/upload_image_url__type_support.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/end_pcm_player.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/end_qt_video.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/end_video_stream.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/force_idr.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/get_pcm_player.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/get_player_status.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/get_qt_video.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/get_video_stream.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/iot_control.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/net_ctrl.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/ntrip_account.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/peripherals_ctrl.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/peripherals_status.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/phone_call.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/play_file.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/play_wav.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/rtc_emotion_change.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/set_diy_word.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/set_nr_mode.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/set_wake_event.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/sigc_data.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/ultrasonic_ctrl.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/upload_image.hpp"
  "rosidl_generator_cpp/homi_speech_interface/srv/upload_image_url.hpp"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/homi_speech_interface__cpp.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
