# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/homi_speech_interface

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/homi_speech_interface.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/all
all: CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all
all: CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/all
all: homi_speech_interface__py/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: homi_speech_interface__py/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/uninstall.dir/clean
clean: CMakeFiles/homi_speech_interface_uninstall.dir/clean
clean: CMakeFiles/homi_speech_interface.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/clean
clean: CMakeFiles/homi_speech_interface__cpp.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/clean
clean: CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/clean
clean: CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/clean
clean: CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/clean
clean: homi_speech_interface__py/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory homi_speech_interface__py

# Recursive "all" directory target.
homi_speech_interface__py/all:
.PHONY : homi_speech_interface__py/all

# Recursive "preinstall" directory target.
homi_speech_interface__py/preinstall:
.PHONY : homi_speech_interface__py/preinstall

# Recursive "clean" directory target.
homi_speech_interface__py/clean: homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/clean
.PHONY : homi_speech_interface__py/clean

#=============================================================================
# Target rules for target CMakeFiles/uninstall.dir

# All Build rule for target.
CMakeFiles/uninstall.dir/all: CMakeFiles/homi_speech_interface_uninstall.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target uninstall"
.PHONY : CMakeFiles/uninstall.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uninstall.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uninstall.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/uninstall.dir/rule

# Convenience name for target.
uninstall: CMakeFiles/uninstall.dir/rule
.PHONY : uninstall

# clean rule for target.
CMakeFiles/uninstall.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/clean
.PHONY : CMakeFiles/uninstall.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface_uninstall.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface_uninstall.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface_uninstall.dir/build.make CMakeFiles/homi_speech_interface_uninstall.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface_uninstall.dir/build.make CMakeFiles/homi_speech_interface_uninstall.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target homi_speech_interface_uninstall"
.PHONY : CMakeFiles/homi_speech_interface_uninstall.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface_uninstall.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface_uninstall.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface_uninstall.dir/rule

# Convenience name for target.
homi_speech_interface_uninstall: CMakeFiles/homi_speech_interface_uninstall.dir/rule
.PHONY : homi_speech_interface_uninstall

# clean rule for target.
CMakeFiles/homi_speech_interface_uninstall.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface_uninstall.dir/build.make CMakeFiles/homi_speech_interface_uninstall.dir/clean
.PHONY : CMakeFiles/homi_speech_interface_uninstall.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/all
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/all
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__cpp.dir/all
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/all
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/all
CMakeFiles/homi_speech_interface.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface.dir/build.make CMakeFiles/homi_speech_interface.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface.dir/build.make CMakeFiles/homi_speech_interface.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target homi_speech_interface"
.PHONY : CMakeFiles/homi_speech_interface.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 88
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface.dir/rule

# Convenience name for target.
homi_speech_interface: CMakeFiles/homi_speech_interface.dir/rule
.PHONY : homi_speech_interface

# clean rule for target.
CMakeFiles/homi_speech_interface.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface.dir/build.make CMakeFiles/homi_speech_interface.dir/clean
.PHONY : CMakeFiles/homi_speech_interface.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_generator_c.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12 "Built target homi_speech_interface__rosidl_generator_c"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 12
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_generator_c: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rule
.PHONY : homi_speech_interface__rosidl_generator_c

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=50,51,52,53,54,55,56,57,58,59,60,61,62 "Built target homi_speech_interface__rosidl_typesupport_fastrtps_c"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_fastrtps_c: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_fastrtps_c

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=75,76,77,78,79,80,81,82,83,84,85,86,87 "Built target homi_speech_interface__rosidl_typesupport_introspection_c"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_introspection_c: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_introspection_c

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=25,26,27,28,29,30,31,32,33,34,35,36,37 "Built target homi_speech_interface__rosidl_typesupport_c"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_c: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_c

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__cpp.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__cpp.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__cpp.dir/build.make CMakeFiles/homi_speech_interface__cpp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__cpp.dir/build.make CMakeFiles/homi_speech_interface__cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target homi_speech_interface__cpp"
.PHONY : CMakeFiles/homi_speech_interface__cpp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__cpp.dir/rule

# Convenience name for target.
homi_speech_interface__cpp: CMakeFiles/homi_speech_interface__cpp.dir/rule
.PHONY : homi_speech_interface__cpp

# clean rule for target.
CMakeFiles/homi_speech_interface__cpp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__cpp.dir/build.make CMakeFiles/homi_speech_interface__cpp.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__cpp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/all: CMakeFiles/homi_speech_interface__cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=63,64,65,66,67,68,69,70,71,72,73,74 "Built target homi_speech_interface__rosidl_typesupport_fastrtps_cpp"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 12
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_fastrtps_cpp: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_fastrtps_cpp

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_cpp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/all: CMakeFiles/homi_speech_interface__cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=88,89,90,91,92,93,94,95,96,97,98,99,100 "Built target homi_speech_interface__rosidl_typesupport_introspection_cpp"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_introspection_cpp: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_introspection_cpp

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_cpp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/all: CMakeFiles/homi_speech_interface__cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=38,39,40,41,42,43,44,45,46,47,48,49 "Built target homi_speech_interface__rosidl_typesupport_cpp"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 12
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_cpp: CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_cpp

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_cpp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir

# All Build rule for target.
CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/build.make CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/build.make CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target ament_cmake_python_copy_homi_speech_interface"
.PHONY : CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/rule

# Convenience name for target.
ament_cmake_python_copy_homi_speech_interface: CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/rule
.PHONY : ament_cmake_python_copy_homi_speech_interface

# clean rule for target.
CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/build.make CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/clean
.PHONY : CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir

# All Build rule for target.
CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/all: CMakeFiles/ament_cmake_python_copy_homi_speech_interface.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/build.make CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/build.make CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target ament_cmake_python_build_homi_speech_interface_egg"
.PHONY : CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/rule

# Convenience name for target.
ament_cmake_python_build_homi_speech_interface_egg: CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/rule
.PHONY : ament_cmake_python_build_homi_speech_interface_egg

# clean rule for target.
CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/build.make CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/clean
.PHONY : CMakeFiles/ament_cmake_python_build_homi_speech_interface_egg.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_generator_py.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all: homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/build.make CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/build.make CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num=13,14,15,16,17,18,19,20,21,22,23,24 "Built target homi_speech_interface__rosidl_generator_py"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_generator_py: CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/rule
.PHONY : homi_speech_interface__rosidl_generator_py

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/build.make CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all: homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext: CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_fastrtps_c__pyext.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all: homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target homi_speech_interface__rosidl_typesupport_introspection_c__pyext"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_introspection_c__pyext: CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_introspection_c__pyext

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_introspection_c__pyext.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir

# All Build rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_typesupport_c.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/all: CMakeFiles/homi_speech_interface__rosidl_generator_py.dir/all
CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/all: homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target homi_speech_interface__rosidl_typesupport_c__pyext"
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/rule

# Convenience name for target.
homi_speech_interface__rosidl_typesupport_c__pyext: CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/rule
.PHONY : homi_speech_interface__rosidl_typesupport_c__pyext

# clean rule for target.
CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/build.make CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/clean
.PHONY : CMakeFiles/homi_speech_interface__rosidl_typesupport_c__pyext.dir/clean

#=============================================================================
# Target rules for target homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir

# All Build rule for target.
homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/all: CMakeFiles/homi_speech_interface.dir/all
	$(MAKE) $(MAKESILENT) -f homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/build.make homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/depend
	$(MAKE) $(MAKESILENT) -f homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/build.make homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles --progress-num= "Built target homi_speech_interface__py"
.PHONY : homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/all

# Build rule for subdir invocation for target.
homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 88
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech_interface/CMakeFiles 0
.PHONY : homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/rule

# Convenience name for target.
homi_speech_interface__py: homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/rule
.PHONY : homi_speech_interface__py

# clean rule for target.
homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/clean:
	$(MAKE) $(MAKESILENT) -f homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/build.make homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/clean
.PHONY : homi_speech_interface__py/CMakeFiles/homi_speech_interface__py.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

