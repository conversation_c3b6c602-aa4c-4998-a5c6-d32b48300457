file(REMOVE_RECURSE
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/action/detail/followcfg__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/action/detail/followcfg__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/action/detail/robot_cmd__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/action/detail/robot_cmd__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/action/detail/robotcfg__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/action/detail/robotcfg__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/assistant_event__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/assistant_event__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/continue_move__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/continue_move__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/exec_step__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/exec_step__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/follow_image_info__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/follow_image_info__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/iot_control_param__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/iot_control_param__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/jpeg_stream__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/jpeg_stream__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/live_stream_task__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/live_stream_task__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/new_udp_connect__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/new_udp_connect__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/pcm_stream__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/pcm_stream__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/proper_to_app__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/proper_to_app__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/propriety_set__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/propriety_set__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/robdog_action__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/robdog_action__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/robdog_state__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/robdog_state__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/sigc_event__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/sigc_event__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/target_vel_info__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/target_vel_info__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/task__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/task__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/task_status__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/task_status__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/task_step__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/task_step__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/trakerimageinfo__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/trakerimageinfo__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/wakeup__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/msg/detail/wakeup__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_abort__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_abort__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_ctrl__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_ctrl__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_quiet__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_quiet__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_speech_text__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_speech_text__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_take_photo__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/assistant_take_photo__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/end_pcm_player__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/end_pcm_player__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/end_qt_video__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/end_qt_video__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/end_video_stream__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/end_video_stream__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/force_idr__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/force_idr__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_pcm_player__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_pcm_player__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_player_status__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_player_status__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_qt_video__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_qt_video__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_video_stream__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/get_video_stream__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/iot_control__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/iot_control__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/net_ctrl__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/net_ctrl__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/ntrip_account__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/ntrip_account__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_ctrl__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_ctrl__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_status__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_status__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/phone_call__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/phone_call__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/play_file__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/play_file__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/play_wav__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/play_wav__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/rtc_emotion_change__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/rtc_emotion_change__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/set_diy_word__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/set_diy_word__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/set_nr_mode__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/set_nr_mode__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/set_wake_event__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/set_wake_event__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/sigc_data__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/sigc_data__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/ultrasonic_ctrl__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/ultrasonic_ctrl__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/upload_image__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/upload_image__functions.c.o.d"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/upload_image_url__functions.c.o"
  "CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/rosidl_generator_c/homi_speech_interface/srv/detail/upload_image_url__functions.c.o.d"
  "libhomi_speech_interface__rosidl_generator_c.pdb"
  "libhomi_speech_interface__rosidl_generator_c.so"
  "rosidl_generator_c/homi_speech_interface/action/detail/followcfg__functions.c"
  "rosidl_generator_c/homi_speech_interface/action/detail/followcfg__functions.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/followcfg__struct.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/followcfg__type_support.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/robot_cmd__functions.c"
  "rosidl_generator_c/homi_speech_interface/action/detail/robot_cmd__functions.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/robot_cmd__struct.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/robot_cmd__type_support.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/robotcfg__functions.c"
  "rosidl_generator_c/homi_speech_interface/action/detail/robotcfg__functions.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/robotcfg__struct.h"
  "rosidl_generator_c/homi_speech_interface/action/detail/robotcfg__type_support.h"
  "rosidl_generator_c/homi_speech_interface/action/followcfg.h"
  "rosidl_generator_c/homi_speech_interface/action/robot_cmd.h"
  "rosidl_generator_c/homi_speech_interface/action/robotcfg.h"
  "rosidl_generator_c/homi_speech_interface/msg/assistant_event.h"
  "rosidl_generator_c/homi_speech_interface/msg/continue_move.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/assistant_event__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/assistant_event__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/assistant_event__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/assistant_event__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/continue_move__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/continue_move__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/continue_move__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/continue_move__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/exec_step__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/exec_step__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/exec_step__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/exec_step__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/follow_image_info__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/follow_image_info__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/follow_image_info__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/follow_image_info__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/iot_control_param__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/iot_control_param__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/iot_control_param__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/iot_control_param__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/jpeg_stream__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/jpeg_stream__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/jpeg_stream__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/jpeg_stream__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/live_stream_task__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/live_stream_task__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/live_stream_task__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/live_stream_task__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/new_udp_connect__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/new_udp_connect__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/new_udp_connect__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/new_udp_connect__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/pcm_stream__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/pcm_stream__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/pcm_stream__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/pcm_stream__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/proper_to_app__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/proper_to_app__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/proper_to_app__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/proper_to_app__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/propriety_set__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/propriety_set__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/propriety_set__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/propriety_set__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_action__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_action__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_action__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_action__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_state__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_state__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_state__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/robdog_state__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/sigc_event__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/sigc_event__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/sigc_event__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/sigc_event__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/target_vel_info__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/target_vel_info__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/target_vel_info__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/target_vel_info__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_status__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_status__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_status__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_status__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_step__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_step__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_step__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/task_step__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/trakerimageinfo__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/trakerimageinfo__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/trakerimageinfo__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/trakerimageinfo__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/wakeup__functions.c"
  "rosidl_generator_c/homi_speech_interface/msg/detail/wakeup__functions.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/wakeup__struct.h"
  "rosidl_generator_c/homi_speech_interface/msg/detail/wakeup__type_support.h"
  "rosidl_generator_c/homi_speech_interface/msg/exec_step.h"
  "rosidl_generator_c/homi_speech_interface/msg/follow_image_info.h"
  "rosidl_generator_c/homi_speech_interface/msg/iot_control_param.h"
  "rosidl_generator_c/homi_speech_interface/msg/jpeg_stream.h"
  "rosidl_generator_c/homi_speech_interface/msg/live_stream_task.h"
  "rosidl_generator_c/homi_speech_interface/msg/new_udp_connect.h"
  "rosidl_generator_c/homi_speech_interface/msg/pcm_stream.h"
  "rosidl_generator_c/homi_speech_interface/msg/proper_to_app.h"
  "rosidl_generator_c/homi_speech_interface/msg/propriety_set.h"
  "rosidl_generator_c/homi_speech_interface/msg/robdog_action.h"
  "rosidl_generator_c/homi_speech_interface/msg/robdog_state.h"
  "rosidl_generator_c/homi_speech_interface/msg/sigc_event.h"
  "rosidl_generator_c/homi_speech_interface/msg/target_vel_info.h"
  "rosidl_generator_c/homi_speech_interface/msg/task.h"
  "rosidl_generator_c/homi_speech_interface/msg/task_status.h"
  "rosidl_generator_c/homi_speech_interface/msg/task_step.h"
  "rosidl_generator_c/homi_speech_interface/msg/trakerimageinfo.h"
  "rosidl_generator_c/homi_speech_interface/msg/wakeup.h"
  "rosidl_generator_c/homi_speech_interface/srv/assistant_abort.h"
  "rosidl_generator_c/homi_speech_interface/srv/assistant_ctrl.h"
  "rosidl_generator_c/homi_speech_interface/srv/assistant_quiet.h"
  "rosidl_generator_c/homi_speech_interface/srv/assistant_speech_text.h"
  "rosidl_generator_c/homi_speech_interface/srv/assistant_take_photo.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_abort__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_abort__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_abort__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_abort__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_ctrl__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_ctrl__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_ctrl__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_ctrl__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_quiet__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_quiet__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_quiet__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_quiet__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_speech_text__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_speech_text__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_speech_text__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_speech_text__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_take_photo__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_take_photo__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_take_photo__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/assistant_take_photo__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_pcm_player__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_pcm_player__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_pcm_player__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_pcm_player__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_qt_video__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_qt_video__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_qt_video__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_qt_video__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_video_stream__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_video_stream__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_video_stream__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/end_video_stream__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/force_idr__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/force_idr__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/force_idr__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/force_idr__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_pcm_player__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_pcm_player__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_pcm_player__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_pcm_player__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_player_status__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_player_status__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_player_status__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_player_status__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_qt_video__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_qt_video__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_qt_video__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_qt_video__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_video_stream__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_video_stream__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_video_stream__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/get_video_stream__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/iot_control__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/iot_control__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/iot_control__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/iot_control__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/net_ctrl__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/net_ctrl__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/net_ctrl__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/net_ctrl__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ntrip_account__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ntrip_account__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ntrip_account__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ntrip_account__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_ctrl__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_ctrl__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_ctrl__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_ctrl__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_status__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_status__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_status__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/peripherals_status__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/phone_call__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/phone_call__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/phone_call__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/phone_call__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_file__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_file__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_file__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_file__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_wav__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_wav__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_wav__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/play_wav__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/rtc_emotion_change__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/rtc_emotion_change__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/rtc_emotion_change__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/rtc_emotion_change__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_diy_word__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_diy_word__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_diy_word__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_diy_word__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_nr_mode__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_nr_mode__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_nr_mode__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_nr_mode__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_wake_event__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_wake_event__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_wake_event__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/set_wake_event__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/sigc_data__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/sigc_data__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/sigc_data__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/sigc_data__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ultrasonic_ctrl__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ultrasonic_ctrl__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ultrasonic_ctrl__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/ultrasonic_ctrl__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image_url__functions.c"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image_url__functions.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image_url__struct.h"
  "rosidl_generator_c/homi_speech_interface/srv/detail/upload_image_url__type_support.h"
  "rosidl_generator_c/homi_speech_interface/srv/end_pcm_player.h"
  "rosidl_generator_c/homi_speech_interface/srv/end_qt_video.h"
  "rosidl_generator_c/homi_speech_interface/srv/end_video_stream.h"
  "rosidl_generator_c/homi_speech_interface/srv/force_idr.h"
  "rosidl_generator_c/homi_speech_interface/srv/get_pcm_player.h"
  "rosidl_generator_c/homi_speech_interface/srv/get_player_status.h"
  "rosidl_generator_c/homi_speech_interface/srv/get_qt_video.h"
  "rosidl_generator_c/homi_speech_interface/srv/get_video_stream.h"
  "rosidl_generator_c/homi_speech_interface/srv/iot_control.h"
  "rosidl_generator_c/homi_speech_interface/srv/net_ctrl.h"
  "rosidl_generator_c/homi_speech_interface/srv/ntrip_account.h"
  "rosidl_generator_c/homi_speech_interface/srv/peripherals_ctrl.h"
  "rosidl_generator_c/homi_speech_interface/srv/peripherals_status.h"
  "rosidl_generator_c/homi_speech_interface/srv/phone_call.h"
  "rosidl_generator_c/homi_speech_interface/srv/play_file.h"
  "rosidl_generator_c/homi_speech_interface/srv/play_wav.h"
  "rosidl_generator_c/homi_speech_interface/srv/rtc_emotion_change.h"
  "rosidl_generator_c/homi_speech_interface/srv/set_diy_word.h"
  "rosidl_generator_c/homi_speech_interface/srv/set_nr_mode.h"
  "rosidl_generator_c/homi_speech_interface/srv/set_wake_event.h"
  "rosidl_generator_c/homi_speech_interface/srv/sigc_data.h"
  "rosidl_generator_c/homi_speech_interface/srv/ultrasonic_ctrl.h"
  "rosidl_generator_c/homi_speech_interface/srv/upload_image.h"
  "rosidl_generator_c/homi_speech_interface/srv/upload_image_url.h"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/homi_speech_interface__rosidl_generator_c.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
