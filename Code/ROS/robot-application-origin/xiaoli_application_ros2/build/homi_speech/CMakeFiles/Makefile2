# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/homi_speech

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/speech_core.dir/all
all: CMakeFiles/helper.dir/all
all: CMakeFiles/capture.dir/all
all: CMakeFiles/wakeup.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/uninstall.dir/clean
clean: CMakeFiles/homi_speech_uninstall.dir/clean
clean: CMakeFiles/speech_core.dir/clean
clean: CMakeFiles/helper.dir/clean
clean: CMakeFiles/capture.dir/clean
clean: CMakeFiles/wakeup.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/uninstall.dir

# All Build rule for target.
CMakeFiles/uninstall.dir/all: CMakeFiles/homi_speech_uninstall.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles --progress-num= "Built target uninstall"
.PHONY : CMakeFiles/uninstall.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uninstall.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uninstall.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
.PHONY : CMakeFiles/uninstall.dir/rule

# Convenience name for target.
uninstall: CMakeFiles/uninstall.dir/rule
.PHONY : uninstall

# clean rule for target.
CMakeFiles/uninstall.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/clean
.PHONY : CMakeFiles/uninstall.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/homi_speech_uninstall.dir

# All Build rule for target.
CMakeFiles/homi_speech_uninstall.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_uninstall.dir/build.make CMakeFiles/homi_speech_uninstall.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_uninstall.dir/build.make CMakeFiles/homi_speech_uninstall.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles --progress-num= "Built target homi_speech_uninstall"
.PHONY : CMakeFiles/homi_speech_uninstall.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/homi_speech_uninstall.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/homi_speech_uninstall.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
.PHONY : CMakeFiles/homi_speech_uninstall.dir/rule

# Convenience name for target.
homi_speech_uninstall: CMakeFiles/homi_speech_uninstall.dir/rule
.PHONY : homi_speech_uninstall

# clean rule for target.
CMakeFiles/homi_speech_uninstall.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_uninstall.dir/build.make CMakeFiles/homi_speech_uninstall.dir/clean
.PHONY : CMakeFiles/homi_speech_uninstall.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/speech_core.dir

# All Build rule for target.
CMakeFiles/speech_core.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles --progress-num=5,6,7,8,9 "Built target speech_core"
.PHONY : CMakeFiles/speech_core.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/speech_core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/speech_core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
.PHONY : CMakeFiles/speech_core.dir/rule

# Convenience name for target.
speech_core: CMakeFiles/speech_core.dir/rule
.PHONY : speech_core

# clean rule for target.
CMakeFiles/speech_core.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/clean
.PHONY : CMakeFiles/speech_core.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/helper.dir

# All Build rule for target.
CMakeFiles/helper.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/helper.dir/build.make CMakeFiles/helper.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/helper.dir/build.make CMakeFiles/helper.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles --progress-num=3,4 "Built target helper"
.PHONY : CMakeFiles/helper.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/helper.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/helper.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
.PHONY : CMakeFiles/helper.dir/rule

# Convenience name for target.
helper: CMakeFiles/helper.dir/rule
.PHONY : helper

# clean rule for target.
CMakeFiles/helper.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/helper.dir/build.make CMakeFiles/helper.dir/clean
.PHONY : CMakeFiles/helper.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/capture.dir

# All Build rule for target.
CMakeFiles/capture.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/capture.dir/build.make CMakeFiles/capture.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/capture.dir/build.make CMakeFiles/capture.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles --progress-num=1,2 "Built target capture"
.PHONY : CMakeFiles/capture.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/capture.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/capture.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
.PHONY : CMakeFiles/capture.dir/rule

# Convenience name for target.
capture: CMakeFiles/capture.dir/rule
.PHONY : capture

# clean rule for target.
CMakeFiles/capture.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/capture.dir/build.make CMakeFiles/capture.dir/clean
.PHONY : CMakeFiles/capture.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/wakeup.dir

# All Build rule for target.
CMakeFiles/wakeup.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/wakeup.dir/build.make CMakeFiles/wakeup.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/wakeup.dir/build.make CMakeFiles/wakeup.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles --progress-num=10,11 "Built target wakeup"
.PHONY : CMakeFiles/wakeup.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/wakeup.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/wakeup.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
.PHONY : CMakeFiles/wakeup.dir/rule

# Convenience name for target.
wakeup: CMakeFiles/wakeup.dir/rule
.PHONY : wakeup

# clean rule for target.
CMakeFiles/wakeup.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/wakeup.dir/build.make CMakeFiles/wakeup.dir/clean
.PHONY : CMakeFiles/wakeup.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

