# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/src/homi_speech

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named homi_speech_uninstall

# Build rule for target.
homi_speech_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 homi_speech_uninstall
.PHONY : homi_speech_uninstall

# fast build rule for target.
homi_speech_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/homi_speech_uninstall.dir/build.make CMakeFiles/homi_speech_uninstall.dir/build
.PHONY : homi_speech_uninstall/fast

#=============================================================================
# Target rules for targets named speech_core

# Build rule for target.
speech_core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 speech_core
.PHONY : speech_core

# fast build rule for target.
speech_core/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/build
.PHONY : speech_core/fast

#=============================================================================
# Target rules for targets named helper

# Build rule for target.
helper: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 helper
.PHONY : helper

# fast build rule for target.
helper/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/helper.dir/build.make CMakeFiles/helper.dir/build
.PHONY : helper/fast

#=============================================================================
# Target rules for targets named capture

# Build rule for target.
capture: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 capture
.PHONY : capture

# fast build rule for target.
capture/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/capture.dir/build.make CMakeFiles/capture.dir/build
.PHONY : capture/fast

#=============================================================================
# Target rules for targets named wakeup

# Build rule for target.
wakeup: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 wakeup
.PHONY : wakeup

# fast build rule for target.
wakeup/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/wakeup.dir/build.make CMakeFiles/wakeup.dir/build
.PHONY : wakeup/fast

src/OfflineAsrEngine.o: src/OfflineAsrEngine.cpp.o
.PHONY : src/OfflineAsrEngine.o

# target to build an object file
src/OfflineAsrEngine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/OfflineAsrEngine.cpp.o
.PHONY : src/OfflineAsrEngine.cpp.o

src/OfflineAsrEngine.i: src/OfflineAsrEngine.cpp.i
.PHONY : src/OfflineAsrEngine.i

# target to preprocess a source file
src/OfflineAsrEngine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/OfflineAsrEngine.cpp.i
.PHONY : src/OfflineAsrEngine.cpp.i

src/OfflineAsrEngine.s: src/OfflineAsrEngine.cpp.s
.PHONY : src/OfflineAsrEngine.s

# target to generate assembly for a file
src/OfflineAsrEngine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/OfflineAsrEngine.cpp.s
.PHONY : src/OfflineAsrEngine.cpp.s

src/capture.o: src/capture.cpp.o
.PHONY : src/capture.o

# target to build an object file
src/capture.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/capture.dir/build.make CMakeFiles/capture.dir/src/capture.cpp.o
.PHONY : src/capture.cpp.o

src/capture.i: src/capture.cpp.i
.PHONY : src/capture.i

# target to preprocess a source file
src/capture.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/capture.dir/build.make CMakeFiles/capture.dir/src/capture.cpp.i
.PHONY : src/capture.cpp.i

src/capture.s: src/capture.cpp.s
.PHONY : src/capture.s

# target to generate assembly for a file
src/capture.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/capture.dir/build.make CMakeFiles/capture.dir/src/capture.cpp.s
.PHONY : src/capture.cpp.s

src/helper.o: src/helper.cpp.o
.PHONY : src/helper.o

# target to build an object file
src/helper.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/helper.dir/build.make CMakeFiles/helper.dir/src/helper.cpp.o
.PHONY : src/helper.cpp.o

src/helper.i: src/helper.cpp.i
.PHONY : src/helper.i

# target to preprocess a source file
src/helper.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/helper.dir/build.make CMakeFiles/helper.dir/src/helper.cpp.i
.PHONY : src/helper.cpp.i

src/helper.s: src/helper.cpp.s
.PHONY : src/helper.s

# target to generate assembly for a file
src/helper.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/helper.dir/build.make CMakeFiles/helper.dir/src/helper.cpp.s
.PHONY : src/helper.cpp.s

src/offline_speech_result_parser.o: src/offline_speech_result_parser.cpp.o
.PHONY : src/offline_speech_result_parser.o

# target to build an object file
src/offline_speech_result_parser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/offline_speech_result_parser.cpp.o
.PHONY : src/offline_speech_result_parser.cpp.o

src/offline_speech_result_parser.i: src/offline_speech_result_parser.cpp.i
.PHONY : src/offline_speech_result_parser.i

# target to preprocess a source file
src/offline_speech_result_parser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/offline_speech_result_parser.cpp.i
.PHONY : src/offline_speech_result_parser.cpp.i

src/offline_speech_result_parser.s: src/offline_speech_result_parser.cpp.s
.PHONY : src/offline_speech_result_parser.s

# target to generate assembly for a file
src/offline_speech_result_parser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/offline_speech_result_parser.cpp.s
.PHONY : src/offline_speech_result_parser.cpp.s

src/opusEngine.o: src/opusEngine.cpp.o
.PHONY : src/opusEngine.o

# target to build an object file
src/opusEngine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/opusEngine.cpp.o
.PHONY : src/opusEngine.cpp.o

src/opusEngine.i: src/opusEngine.cpp.i
.PHONY : src/opusEngine.i

# target to preprocess a source file
src/opusEngine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/opusEngine.cpp.i
.PHONY : src/opusEngine.cpp.i

src/opusEngine.s: src/opusEngine.cpp.s
.PHONY : src/opusEngine.s

# target to generate assembly for a file
src/opusEngine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/opusEngine.cpp.s
.PHONY : src/opusEngine.cpp.s

src/speech_core.o: src/speech_core.cpp.o
.PHONY : src/speech_core.o

# target to build an object file
src/speech_core.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/speech_core.cpp.o
.PHONY : src/speech_core.cpp.o

src/speech_core.i: src/speech_core.cpp.i
.PHONY : src/speech_core.i

# target to preprocess a source file
src/speech_core.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/speech_core.cpp.i
.PHONY : src/speech_core.cpp.i

src/speech_core.s: src/speech_core.cpp.s
.PHONY : src/speech_core.s

# target to generate assembly for a file
src/speech_core.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech_core.dir/build.make CMakeFiles/speech_core.dir/src/speech_core.cpp.s
.PHONY : src/speech_core.cpp.s

src/wakeup.o: src/wakeup.cpp.o
.PHONY : src/wakeup.o

# target to build an object file
src/wakeup.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/wakeup.dir/build.make CMakeFiles/wakeup.dir/src/wakeup.cpp.o
.PHONY : src/wakeup.cpp.o

src/wakeup.i: src/wakeup.cpp.i
.PHONY : src/wakeup.i

# target to preprocess a source file
src/wakeup.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/wakeup.dir/build.make CMakeFiles/wakeup.dir/src/wakeup.cpp.i
.PHONY : src/wakeup.cpp.i

src/wakeup.s: src/wakeup.cpp.s
.PHONY : src/wakeup.s

# target to generate assembly for a file
src/wakeup.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/wakeup.dir/build.make CMakeFiles/wakeup.dir/src/wakeup.cpp.s
.PHONY : src/wakeup.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... homi_speech_uninstall"
	@echo "... uninstall"
	@echo "... capture"
	@echo "... helper"
	@echo "... speech_core"
	@echo "... wakeup"
	@echo "... src/OfflineAsrEngine.o"
	@echo "... src/OfflineAsrEngine.i"
	@echo "... src/OfflineAsrEngine.s"
	@echo "... src/capture.o"
	@echo "... src/capture.i"
	@echo "... src/capture.s"
	@echo "... src/helper.o"
	@echo "... src/helper.i"
	@echo "... src/helper.s"
	@echo "... src/offline_speech_result_parser.o"
	@echo "... src/offline_speech_result_parser.i"
	@echo "... src/offline_speech_result_parser.s"
	@echo "... src/opusEngine.o"
	@echo "... src/opusEngine.i"
	@echo "... src/opusEngine.s"
	@echo "... src/speech_core.o"
	@echo "... src/speech_core.i"
	@echo "... src/speech_core.s"
	@echo "... src/wakeup.o"
	@echo "... src/wakeup.i"
	@echo "... src/wakeup.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

