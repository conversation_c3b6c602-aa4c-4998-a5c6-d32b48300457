# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_homi_speech_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED homi_speech_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(homi_speech_FOUND FALSE)
  elseif(NOT homi_speech_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(homi_speech_FOUND FALSE)
  endif()
  return()
endif()
set(_homi_speech_CONFIG_INCLUDED TRUE)

# output package information
if(NOT homi_speech_FIND_QUIETLY)
  message(STATUS "Found homi_speech: 0.0.0 (${homi_speech_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'homi_speech' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${homi_speech_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(homi_speech_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "")
foreach(_extra ${_extras})
  include("${homi_speech_DIR}/${_extra}")
endforeach()
