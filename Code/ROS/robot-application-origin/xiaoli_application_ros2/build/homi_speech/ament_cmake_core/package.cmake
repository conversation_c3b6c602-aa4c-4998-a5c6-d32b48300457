set(_AMENT_PACKAGE_NAME "homi_speech")
set(homi_speech_VERSION "0.0.0")
set(homi_speech_MAINTAINER "zrj <<EMAIL>>")
set(homi_speech_BUILD_DEPENDS "rclcpp" "homi_speech_interface")
set(homi_speech_BUILDTOOL_DEPENDS "ament_cmake")
set(homi_speech_BUILD_EXPORT_DEPENDS "rclcpp" "homi_speech_interface")
set(homi_speech_BUILDTOOL_EXPORT_DEPENDS )
set(homi_speech_EXEC_DEPENDS "rclcpp" "homi_speech_interface")
set(homi_speech_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(homi_speech_GROUP_DEPENDS )
set(homi_speech_MEMBER_OF_GROUPS )
set(homi_speech_DEPRECATED "")
set(homi_speech_EXPORT_TAGS)
list(APPEND homi_speech_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
