AMENT_PREFIX_PATH=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/install/homi_speech_interface:/opt/ros/humble
ANTHROPIC_AUTH_TOKEN=sk-bIrPf7D0AFcc66DlrzOF2T4Rop4TbuQC1WUry1mczY3Iz3YL
ANTHROPIC_BASE_URL=https://anyrouter.top
AUTOJUMP_ERROR_PATH=/home/<USER>/.local/share/autojump/errors.log
AUTOJUMP_SOURCED=1
BUILD_TYPE=Release
CC=gcc
CFLAGS=-O2
CMAKE_BUILD_TYPE=Release
CMAKE_PREFIX_PATH=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/install/homi_speech_interface
COLCON=1
COLCON_JOBS=16
COLORTERM=truecolor
CXX=g++
CXXFLAGS=-O2
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
DISPLAY=:0
FZF_DEFAULT_COMMAND=fd --exclude={.git,.idea,.sass-cache,node_modules,build} --type f
FZF_DEFAULT_OPTS=--height 40% --layout=reverse --preview '(highlight -O ansi {} || cat {}) 2> /dev/null | head -500'
GEMINI_API_KEY=AIzaSyBHGmgSCnj9yEITCK_cpm62hfOf7kqNQik
GIT_ASKPASS=/home/<USER>/.cursor-server/bin/a8e95743c5268be73767c46944a71f4465d05c90/extensions/git/dist/askpass.sh
GOOGLE_CLOUD_PROJECT=dotted-chariot-464922-r5
GPG_TTY=/dev/pts/16
GTK_IM_MODULE=fcitx
HOME=/home/<USER>
HOSTTYPE=x86_64
HTTPS_PROXY=http://127.0.0.1:7890
HTTP_PROXY=http://127.0.0.1:7890
INPUT_METHOD=fcitx
LANG=zh_CN.UTF-8
LD_LIBRARY_PATH=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/install/homi_speech_interface/lib:/home/<USER>/.x-cmd.root/local/data/pkg/sphere/X/l/j/h/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib
LESS=-R
LOGNAME=xuhui
LSCOLORS=Gxfxcxdxbxegedabagacad
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
NAME=Ubuntu
NO_PROXY=192.168.*,172.31.*,172.30.*,172.29.*,172.28.*,172.27.*,172.26.*,172.25.*,172.24.*,172.23.*,172.22.*,172.21.*,172.20.*,172.19.*,172.18.*,172.17.*,172.16.*,10.*,127.*,localhost,<local>
OLDPWD=/mine/note/Code/ROS/robot-application-origin
PAGER=less
PATH=/home/<USER>/.x-cmd.root/local/data/pkg/sphere/X/l/j/h/bin:/home/<USER>/.x-cmd.root/bin:/home/<USER>/.cargo/bin:/home/<USER>/anaconda3/bin:/home/<USER>/.cursor-server/bin/a8e95743c5268be73767c46944a71f4465d05c90/bin/remote-cli:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/usr/lib/wsl/lib:/c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin:/c/Windows/system32:/c/Windows:/c/Windows/System32/Wbem:/c/Windows/System32/WindowsPowerShell/v1.0/:/c/Windows/System32/OpenSSH/:/c/Users/<USER>/AppData/Local/Microsoft/WindowsApps:/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin:/usr/local/go/bin:/home/<USER>/.x-cmd.root/local/data/triarii/bin
PULSE_SERVER=unix:/wslg/PulseServer
PWD=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/build/homi_speech
PYTHONPATH=/mine/note/Code/ROS/robot-application-origin/xiaoli_application_ros2/install/homi_speech_interface/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages
QT_IM_MODULE=fcitx
ROS_ARCH=x86_64
ROS_DISTRO=humble
ROS_LOCALHOST_ONLY=0
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SDL_IM_MODULE=fcitx
SHELL=/usr/bin/zsh
SHLVL=3
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.2.4
USER=xuhui
USER_ZDOTDIR=/home/<USER>
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/home/<USER>/.cursor-server/bin/a8e95743c5268be73767c46944a71f4465d05c90/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/home/<USER>/.cursor-server/bin/a8e95743c5268be73767c46944a71f4465d05c90/node
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-8907fa969f.sock
VSCODE_INJECTION=1
VSCODE_IPC_HOOK_CLI=/run/user/1000/vscode-ipc-495f02c1-ec99-4ea5-b06d-689e6e961fbe.sock
WAYLAND_DISPLAY=wayland-0
WSL2_GUI_APPS_ENABLED=1
WSLENV=
WSL_DISTRO_NAME=Ubuntu-22.04
WSL_INTEROP=/run/WSL/72072_interop
XDG_RUNTIME_DIR=/run/user/1000/
XMODIFIERS=@im=fcitx
ZDOTDIR=/home/<USER>
ZSH=/home/<USER>/.oh-my-zsh
_=/usr/bin/colcon
___X_CMD_LOG_C_DEBUG=\033[2;35m
___X_CMD_LOG_C_ERROR=\033[1;31m
___X_CMD_LOG_C_INFO=\033[32m
___X_CMD_LOG_C_TF=
___X_CMD_LOG_C_WARN=\033[1;33m
http_proxy=http://127.0.0.1:7890
https_proxy=http://127.0.0.1:7890
no_proxy=192.168.*,172.31.*,172.30.*,172.29.*,172.28.*,172.27.*,172.26.*,172.25.*,172.24.*,172.23.*,172.22.*,172.21.*,172.20.*,172.19.*,172.18.*,172.17.*,172.16.*,10.*,127.*,localhost,<local>
