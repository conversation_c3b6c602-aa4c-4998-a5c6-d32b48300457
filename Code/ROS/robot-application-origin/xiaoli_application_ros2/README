1. 编译
    bash build.sh --help
        查看帮助
    bash build.sh
        默认选项编译
            产品编号 1.0, 线上环境(pro), debug 版本, 本地构建, 全量板子打包，全量模块编译
    bash build.sh --no=1.0 --env=dev --build=release
        选项编译
            产品编号 1.0, 测试环境(pro), release 版本, 本地构建, 全量板子打包，全量模块编译
    bash build.sh --no=1.0 --env=dev --build=release --board=app
        选项编译
            产品编号 1.0, 测试环境(pro), release 版本, 本地构建, 只打包交互板(app), 交互版(app)全量模块编译及打包
    bash build.sh --no=1.0 --env=dev --build=release --board=app --select=robdog_control,live_stream
        选项编译
            产品编号 1.0, 测试环境(pro), release 版本, 本地构建, 只打包交互板(app), 但交互版(app)只编译打包 robdog_control + live_stream
    bash build.sh --no=1.0 --env=dev --build=release --board=app,motion --select=robdog_control,live_stream
        选项编译
            产品编号 1.0, 测试环境(pro), release 版本, 本地构建, 全量模块板子打包, 但只编译打包 robdog_control + live_stream

    安装包存放位置
        pack_xxx(时间戳)

2. 部署
    app_xxx.bin(交互版)
    motion_xxx.bin(运控板)
    cognition_xxx.bin(感知板)

    ./app_xxx.bin --help
        查看帮助
    ./app_xxx.bin --hanlde=stop/extract/start
        stop: 停止服务
        extract: 只解压升级包
        start: 开启服务
    ./app_xxx.bin --base(低配版) --test(测试环境) --install=/xxx(安装路径) --pwd="xxx"(密码) --forall(同时升级其他板子)
        以上参数均可选, 根据需要选择入参即可

    安装路径查看
        /usr/bin/cmcc_robot/install
            version.list: 安装包信息
            install.list: 安装信息