#!/bin/bash

g_work_dir=$(pwd)
g_root_of_script=$(dirname $(readlink -f "$0"))

function func_usage()
{
    echo "usage:"
    echo "    --help/--h"
    echo "    [--board=] motion,app,cognition"
    echo "    [--select=] robdog_control,..."
    echo "    [--skip=] robdog_control,..."
    echo "    [--build] debug/release"
    echo "    [--no=] 0.0/1.0/1.1/2.0/..."
    echo "    --online"
    echo "    [--base]"
    exit
}

g_temp_param=""
g_build_type="debug"
g_product_no="1.0"
g_select_boards=""
g_select_packages=""
g_skip_packages=""
g_is_online="n"
g_path_base=""
while [ $# -gt 0 ]; do
    g_temp_param=$1
    case ${g_temp_param} in
        --help | --h)
            func_usage
            ;;
        --board=*)
            g_select_boards=${g_temp_param#*=}
            ;;
        --select=*)
            g_select_packages=${g_temp_param#*=}
            ;;
        --skip=*)
            g_skip_packages=${g_temp_param#*=}
            ;;
        --build=*)
            g_build_type=${g_temp_param#*=}
            ;;
        --no=*)
            g_product_no=${g_temp_param#*=}
            ;;
        --online)
            g_is_online="y"
            ;;
        --base=*)
            g_path_base=${g_temp_param#*=}
            ;;
        *)
            func_usage
            ;;
    esac
    shift
done

g_install_status="success"
g_pack_time=$(date +%Y%m%d%H%M%S)
function func_check_param()
{
    echo "[$LINENO][NOTE ]----- check param"
    echo "[$LINENO][NOTE ]time:    ${g_pack_time}"
    echo "[$LINENO][NOTE ]boards:  ${g_select_boards}"
    echo "[$LINENO][NOTE ]select:  ${g_select_packages}"
    echo "[$LINENO][NOTE ]skip:    ${g_skip_packages}"
    echo "[$LINENO][NOTE ]build:   ${g_build_type}"
    echo "[$LINENO][NOTE ]product: ${g_product_no}"
    echo "[$LINENO][NOTE ]online:  ${g_is_online}"
    echo "[$LINENO][NOTE ]base:    ${g_path_base}"
    if [ "${g_select_packages}" != "" -a "${g_skip_packages}" != "" ]; then
        echo "[$LINENO][ERROR][--select] conflict with [--skip]"
        g_install_status="func_check_param"
        return
    fi
}

g_project_info_path=${g_root_of_script}/project.yaml
g_root_path=${g_root_of_script}
function func_git_env_check()
{
    echo "[$LINENO][NOTE ]----- check git env"
    git -C ${g_root_path} status
    if [ "$?" == "0" ]; then
        echo "[$LINENO][NOTE ]git env valid"
        return
    fi
    if [ -d /mine/robot-application-02 ]; then
        echo "[$LINENO][NOTE ]set git env"
        git config --global --add safe.directory /mine/robot-application-02
    fi
}

function func_check_env()
{
    echo "[$LINENO][NOTE ]----- check env"
    echo "[$LINENO][NOTE ]project info: ${g_project_info_path}"
    if [ ! -f ${g_project_info_path} ]; then
        echo "[$LINENO][ERROR][${g_project_info_path}]lost"
        g_install_status="func_check_env"
        return
    fi
    if command -v yq &> /dev/null; then
        echo "[$LINENO][NOTE ]yq is ready"
    else
        echo "[$LINENO][ERROR]please [pip install yq]"
        g_install_status="func_check_env"
        return
    fi
    if command -v md5sum &> /dev/null; then
        echo "[$LINENO][NOTE ]md5sum is ready"
    else
        echo "[$LINENO][ERROR]please [sudo apt install md5sum]"
        g_install_status="func_check_env"
        return
    fi
    if [ "${g_is_online}" == "y" ]; then
        func_git_env_check
    fi
}

g_robot_version="1.1.0"
g_product_no_temp=${g_product_no/./_}
g_product_no_temp="dev_${g_product_no_temp}"
g_oem=""
g_product_name=""
g_target_arch=""
g_app_user=""
g_app_pwd=""
g_app_ip=""
g_motion_user=""
g_motion_pwd=""
g_motion_ip=""
g_cognition_user=""
g_cognition_pwd=""
g_cognition_ip=""
function func_get_board_info()
{
    local arg_board="$1"
    local arg_user=""
    local arg_pwd=""
    local arg_ip=""
    echo "[$LINENO][NOTE ]----- get board info of [${arg_board}]"
    while true
    do
        arg_user=$(yq -r '.product.'${g_product_no_temp}'.'${arg_board}'.user' ${g_project_info_path})
        if [ "${arg_user}" == "null" ] || [ -z "${arg_user}" ]; then
            echo "[$LINENO][ERROR] arg_user is invalid"
            g_install_status="func_get_board_info"
            break
        fi

        arg_pwd=$(yq -r '.product.'${g_product_no_temp}'.'${arg_board}'.pwd' ${g_project_info_path})
        if [ "${arg_pwd}" == "null" ] || [ -z "${arg_pwd}" ]; then
            echo "[$LINENO][ERROR] arg_pwd is invalid"
            g_install_status="func_get_board_info"
            break
        fi

        arg_ip=$(yq -r '.product.'${g_product_no_temp}'.'${arg_board}'.ip' ${g_project_info_path})
        if [ "${arg_ip}" == "null" ] || [ -z "${arg_ip}" ]; then
            echo "[$LINENO][ERROR] arg_ip is invalid"
            g_install_status="func_get_board_info"
            break
        fi

        break
    done
    if [ "${arg_board}" == "app" ]; then
        g_app_user="${arg_user}"
        g_app_pwd="${arg_pwd}"
        g_app_ip="${arg_ip}"
    elif [ "${arg_board}" == "motion" ]; then
        g_motion_user="${arg_user}"
        g_motion_pwd="${arg_pwd}"
        g_motion_ip="${arg_ip}"
    elif [ "${arg_board}" == "cognition" ]; then
        g_cognition_user="${arg_user}"
        g_cognition_pwd="${arg_pwd}"
        g_cognition_ip="${arg_ip}"
    fi
    echo "[$LINENO][NOTE ]board: ${arg_board}"
    echo "[$LINENO][NOTE ]usr:   ${arg_user}"
    echo "[$LINENO][NOTE ]pwd:   ${arg_pwd}"
    echo "[$LINENO][NOTE ]ip:    ${arg_ip}"
}

function func_get_robot_info()
{
    echo "[$LINENO][NOTE ]----- get robot info"
    local arg_board_list=""
    local arg_board_array=""
    while true
    do
        g_robot_version=$(yq -r '.module.robot_body' ${g_project_info_path})
        if [ "${g_robot_version}" == "null" -o "${g_robot_version}" == "" ]; then
            echo "[$LINENO][ERROR]g_robot_version invalid"
            g_install_status="func_get_robot_info"
            break
        fi
        g_oem=$(yq -r '.product.'${g_product_no_temp}'.oem' ${g_project_info_path})
        if [ "${g_oem}" == "null" -o "${g_oem}" == "" ]; then
            echo "[$LINENO][ERROR]g_oem invalid"
            g_install_status="func_get_robot_info"
            break
        fi
        g_product_name=$(yq -r '.product.'${g_product_no_temp}'.name' ${g_project_info_path})
        if [ "${g_product_name}" == "null" -o "${g_product_name}" == "" ]; then
            echo "[$LINENO][ERROR]g_product_name invalid"
            g_install_status="func_get_robot_info"
            break
        fi
        g_target_arch=$(yq -r '.product.'${g_product_no_temp}'.arch' ${g_project_info_path})
        if [ "${g_target_arch}" == "null" -o "${g_target_arch}" == "" ]; then
            echo "[$LINENO][ERROR]g_target_arch invalid"
            g_install_status="func_get_robot_info"
            break
        fi
        arg_board_list=$(yq -r '.product.'${g_product_no_temp}'.board' ${g_project_info_path})
        if [ "${arg_board_list}" == "null" -o "${arg_board_list}" == "" ]; then
            echo "[$LINENO][ERROR]arg_board_list invalid"
            g_install_status="func_get_robot_info"
            break
        fi
        if [ "${g_product_no}" != "0.0" ]; then
            arg_board_array=(${arg_board_list//,/ })
            for arg_board in ${arg_board_array[@]}
            do
                func_get_board_info "${arg_board}"
            done
        fi
        break
    done
    echo "[$LINENO][NOTE ]version: ${g_robot_version}"
    echo "[$LINENO][NOTE ]product: ${g_product_no_temp}"
    echo "[$LINENO][NOTE ]oem:     ${g_oem}"
    echo "[$LINENO][NOTE ]name:    ${g_product_name}"
    echo "[$LINENO][NOTE ]arch:    ${g_target_arch}"
    echo "[$LINENO][NOTE ]board:   ${arg_board_list}"
}

function func_current_branch()
{
    local folder="$(pwd)"
    [ -n "$1" ] && folder="$1"
    git -C "$folder" rev-parse --abbrev-ref HEAD | grep -v HEAD || \
    git -C "$folder" describe --tags HEAD || \
    git -C "$folder" rev-parse HEAD
}

function func_commit_id()
{
    local folder="$(pwd)"
    [ -n "$1" ] && folder="$1"
    git -C "$folder" rev-parse HEAD
}

g_install_bin_prefix=""
g_install_short_prefix=""
g_install_src_path=${g_root_of_script}/install
g_pack_path=""
g_git_branch_tag=""
g_git_commit=""
g_pack_bin_dir=cmcc_robot_bin
g_pack_bin_path=""
g_pack_bin_info_path=""
function func_pack_prepare()
{
    echo "[$LINENO][NOTE ]----- pack prepare"
    local arg_base_path_first_c=""
    local arg_base_path_last_c=""
    if [ "${g_path_base}" == "" -o "${g_path_base}" == " " ]; then
        g_path_base=${g_root_of_script}/build_${g_product_no}/${g_build_type}
    else
        arg_base_path_first_c=${g_path_base:0:1}
        if [ "${arg_base_path_first_c}" != "/" ]; then
            echo "[$LINENO][ERROR]not absolute path"
            g_install_status="func_pack_prepare"
            return
        fi
        arg_base_path_last_c=${g_path_base: -1}
        if [ "${arg_base_path_last_c}" == "/" ]; then
            echo "[$LINENO][NOTE ]del last /"
            g_path_base=${g_path_base%/*}
        fi
    fi
    if [ ! -d ${g_path_base} ]; then
        echo "[$LINENO][ERROR][${g_path_base}]lost"
        g_install_status="func_pack_prepare"
        return
    fi
    g_install_src_path=${g_path_base}/install
    if [ "${g_is_online}" == "y" ]; then
        g_pack_path=${g_root_of_script}/pack
    else
        g_pack_path=${g_root_of_script}/pack_${g_product_no}_${g_build_type}_${g_pack_time}
    fi
    if [ -d ${g_pack_path} ]; then
        echo "[$LINENO][NOTE ][${g_pack_path}]del"
        rm -rf ${g_pack_path}
    fi
    mkdir -p ${g_pack_path}
    local arg_pack_info=pack_${g_pack_time}.info
    g_pack_bin_path=${g_pack_path}/${g_pack_bin_dir}
    g_pack_bin_info_path=${g_pack_bin_path}/${arg_pack_info}
    g_git_branch_tag=$(func_current_branch ${g_root_path})
    if [ "${g_git_branch_tag}" == "" -o "${g_git_branch_tag}" == " " ]; then
        g_git_branch_tag="unknown"
    fi
    g_git_commit=$(func_commit_id ${g_root_path})
    if [ "${g_git_commit}" != "" -a "${g_git_commit}" != " " ]; then
        g_git_commit=${g_git_commit:0:8}
    else
        g_git_commit="unknown"
    fi
    g_install_bin_prefix=${g_product_no}_v${g_robot_version}_${g_pack_time}_${g_build_type}_${g_git_branch_tag}_${g_git_commit}_install
    g_install_short_prefix=${g_pack_time}_${g_build_type}
    echo "[$LINENO][NOTE ]src:     ${g_install_src_path}"
    echo "[$LINENO][NOTE ]pack:    ${g_pack_path}"
    echo "[$LINENO][NOTE ]bin_dir: ${g_pack_bin_dir}"
    echo "[$LINENO][NOTE ]info:    ${arg_pack_info}"
    echo "[$LINENO][NOTE ]branch:  ${g_git_branch_tag}"
    echo "[$LINENO][NOTE ]commit:  ${g_git_commit}"
}

function func_check_module()
{
    echo "[$LINENO][NOTE ]----- check module"
    local arg_support_list="${1}"
    local arg_suport_array=""
    local arg_select_array=""
    arg_suport_array=(${arg_support_list//,/ })
    arg_select_array=(${g_select_packages//,/ })
    for arg_select_package in ${arg_select_array[@]}
    do
        if [[ "${arg_suport_array[@]}" =~ "${arg_select_package}" ]]; then
            continue
        else
            g_install_status="func_check_module"
            echo "[$LINENO][ERROR]not support package[${arg_select_package}]"
            return
        fi
    done
}

function func_board_modules_pack()
{
    local arg_board=$1
    local arg_board_pack_path=$2
    local arg_package_list=""
    echo "[$LINENO][NOTE ]----- pack module of ${arg_board}"
    arg_package_list=$(yq -r '.product.'${g_product_no_temp}'.'${arg_board}'.module' ${g_project_info_path})
    if [ "${arg_package_list}" == "null" -o "${arg_package_list}" == "" ]; then
        echo "[$LINENO][ERROR]arg_package_list invalid"
        g_install_status="func_board_modules_pack"
        return
    fi
    echo "PKG:robot_body:${g_robot_version}:${g_pack_time}:${g_git_branch_tag}:${g_git_commit}" >> ${arg_board_pack_path}/version.list
    local arg_package_array=""
    local arg_version=""
    local arg_select_array=""
    local arg_skip_array=""
    local arg_need_pack="y"
    if [ "${g_select_packages}" != "" -a "${g_select_packages}" != " " ]; then
        func_check_module "${arg_package_list}"
        if [ "${g_install_status}" != "success" ]; then
            return
        fi
        arg_select_array=(${g_select_packages//,/ })
    fi
    if [ "${g_skip_packages}" != "" -a "${g_skip_packages}" != " " ]; then
        arg_skip_array=(${g_skip_packages//,/ })
    fi
    arg_package_array=(${arg_package_list//,/ })
    for arg_package in ${arg_package_array[@]}
    do
        arg_version=$(yq -r '.module.'${arg_package}'' ${g_project_info_path})
        if [ "${arg_version}" == "null" -o "${arg_version}" == "" ]; then
            echo "[$LINENO][ERROR][${arg_package}]version invalid"
            g_install_status="func_board_modules_pack"
            return
        fi
        arg_need_pack="y"
        if [ "${g_skip_packages}" != "" -a "${g_skip_packages}" != " " ]; then
            if [[ "${arg_skip_array[@]}" =~ "${arg_package}" ]]; then
                echo "[$LINENO][WARN ][${arg_package}]skip"
                arg_need_pack="n"
                continue
            fi
        fi
        if [ "${g_select_packages}" != "" -a "${g_select_packages}" != " " ]; then
            if [[ "${arg_select_array[@]}" =~ "${arg_package}" ]]; then
                echo "[$LINENO][NOTE ][${arg_package}]select"
            else
                arg_need_pack="n"
            fi
        fi
        if [ "${arg_need_pack}" == "y" ]; then
            if [ ! -d "${g_install_src_path}/${arg_package}" ]; then
                echo "[$LINENO][ERROR][${arg_package}]not exist"
                g_install_status="func_board_modules_pack"
                return
            fi
            echo "[$LINENO][NOTE ][${arg_package}]pack"
            # copy [a2dp/andlink/...] directory
            cp -rf ${g_install_src_path}/${arg_package} ${arg_board_pack_path}
            # record version.list
            echo "PKG:${arg_package}:${arg_version}:${g_pack_time}:${g_git_branch_tag}:${g_git_commit}" >> ${arg_board_pack_path}/version.list
        fi
    done
    if [ "${g_select_packages}" == "" -o "${g_select_packages}" == " " ]; then
        cp -rf ${g_install_src_path}/*.py                   ${arg_board_pack_path}
        cp -rf ${g_install_src_path}/.colcon_install_layout ${arg_board_pack_path}
        cp -rf ${g_install_src_path}/COLCON_IGNORE          ${arg_board_pack_path}
        cp -rf ${g_install_src_path}/*.bash                 ${arg_board_pack_path}
        cp -rf ${g_install_src_path}/*.ps1                  ${arg_board_pack_path}
        cp -rf ${g_install_src_path}/*.sh                   ${arg_board_pack_path}
        cp -rf ${g_install_src_path}/*.zsh                  ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/_local_setup_util_ps1.py ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/_local_setup_util_sh.py ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/.colcon_install_layout ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/COLCON_IGNORE ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/local_setup.bash ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/local_setup.ps1 ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/local_setup.sh ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/local_setup.zsh ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/setup.bash ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/setup.ps1 ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/setup.sh ${arg_board_pack_path}
        # cp -rf ${g_install_src_path}/setup.zsh ${arg_board_pack_path}
    fi
}

function func_board_dependencies_pack()
{
    local arg_board=$1
    local arg_board_env_path=$2/env
    echo "[$LINENO][NOTE ]----- pack dependency of ${arg_board}"
    if [ ! -d ${arg_board_env_path} ]; then
        mkdir -p ${arg_board_env_path}
    fi
    local arg_package_list=""
    arg_package_list=$(yq -r '.product.'${g_product_no_temp}'.'${arg_board}'.dependency' ${g_project_info_path})
    if [ "${arg_package_list}" == "null" -o "${arg_package_list}" == "" ]; then
        echo "[$LINENO][NOTE ]no deps"
        return
    fi
    local arg_temp_product_no=""
    local arg_package_array=""
    arg_package_array=(${arg_package_list//,/ })
    for arg_package in ${arg_package_array[@]}
    do
        echo "[$LINENO][NOTE ][${arg_package}]pack"
        if [ "${arg_package}" == "ObuVoice" ]; then
            if [ ! -d ${g_root_of_script}/../3rdParty/ObuVoice ]; then
                echo "[$LINENO][ERROR][${arg_package}]not exist"
                g_install_status="func_board_dependencies_pack"
                return
            fi
            cp -rf ${g_root_of_script}/../3rdParty/ObuVoice ${arg_board_env_path}
        elif [ "${arg_package}" == "lib" ]; then
            if [ ! -d ${g_path_base}/env/lib ]; then
                echo "[$LINENO][ERROR][${arg_package}]not exist"
                g_install_status="func_board_dependencies_pack"
                return
            fi
            cp -rf ${g_path_base}/env/lib ${arg_board_env_path}
        elif [ "${arg_package}" == "uwb" ]; then
            if [ ! -d ${g_root_of_script}/../deeprob_ws_ctrl/uwb_ws_server/install/uwb_ws_server ]; then
                echo "[$LINENO][ERROR][${arg_package}]not exist"
                g_install_status="func_board_dependencies_pack"
                return
            fi
            cp -rf ${g_root_of_script}/../deeprob_ws_ctrl/uwb_ws_server/install/uwb_ws_server ${arg_board_env_path}/uwb
        elif [ "${arg_package}" == "SensorHub" ]; then
            if [ ! -d ${g_root_of_script}/../3rdParty/SensorHub/install/SensorHub ]; then
                echo "[$LINENO][ERROR][${arg_package}]not exist"
                g_install_status="func_board_dependencies_pack"
                return
            fi
            cp -rf ${g_root_of_script}/../3rdParty/SensorHub/install/SensorHub ${arg_board_env_path}
        elif [ "${arg_package}" == "service" ]; then
            arg_temp_product_no=${g_product_no}
            if [ "${g_product_no}" == "0.0" -o "${g_product_no}" == "0.1" ]; then
                arg_temp_product_no="1.1"
            fi
            if [ ! -d ${g_root_of_script}/${arg_package}/${arg_temp_product_no} ]; then
                echo "[$LINENO][ERROR][${arg_package}/${arg_temp_product_no}]not exist"
                g_install_status="func_board_dependencies_pack"
                return
            fi
            cp -rf ${g_root_of_script}/${arg_package}/${arg_temp_product_no} ${arg_board_env_path}/service
        elif [ "${arg_package}" == "factory" ]; then
            if [ ! -d ${g_root_of_script}/../3rdParty/factory/install/factory ]; then
                echo "[$LINENO][ERROR][${arg_package}]not exist"
                g_install_status="func_board_dependencies_pack"
                return
            fi
            cp -rf ${g_root_of_script}/../3rdParty/factory/install/factory ${arg_board_env_path}
        else
            echo "[$LINENO][ERROR][${arg_package}]not support"
            g_install_status="func_board_dependencies_pack"
            return
        fi
    done
}

function func_board_zip_make()
{
    local arg_board=$1
    local arg_board_zip_name=${arg_board}_${g_install_short_prefix}.tar.gz
    local arg_board_zip_path=${g_pack_path}/${arg_board_zip_name}
    local arg_board_bin_name=${arg_board}_${g_install_short_prefix}.bin
    local arg_board_bin_path=${g_pack_path}/${arg_board_bin_name}
    local arg_board_install_path=${g_pack_path}/${arg_board}_install.sh
    local arg_md5_str=""
    echo "[$LINENO][NOTE ]----- make zip of ${arg_board}"
    cd ${g_pack_path}
    tar -zcf ${arg_board_zip_name} ${arg_board}
    arg_md5_str=($(md5sum ${arg_board_zip_name}))
    cd ${g_root_of_script}
    cp -rf ${g_root_of_script}/install.sh ${arg_board_install_path}
    sed -i "s|^g_target_no=.*|g_target_no=\"${g_product_no}\"|"              ${arg_board_install_path}
    sed -i "s|^g_target_md5=.*|g_target_md5=\"${arg_md5_str}\"|"             ${arg_board_install_path}
    sed -i "s|^g_target_board=.*|g_target_board=\"${arg_board}\"|"           ${arg_board_install_path}
    sed -i "s|^g_target_version=.*|g_target_version=\"${g_robot_version}\"|" ${arg_board_install_path}
    sed -i "s|^g_target_oem=.*|g_target_oem=\"${g_oem}\"|"                   ${arg_board_install_path}
    sed -i "s|^g_target_product=.*|g_target_product=\"${g_product_name}\"|"  ${arg_board_install_path}
    sed -i "s|^g_target_arch=.*|g_target_arch=\"${g_target_arch}\"|"         ${arg_board_install_path}
    sed -i "s|^g_pack_time=.*|g_pack_time=\"${g_pack_time}\"|"               ${arg_board_install_path}
    sed -i "s|^g_build_type=.*|g_build_type=\"${g_build_type}\"|"            ${arg_board_install_path}
    sed -i "s|^g_git_branch=.*|g_git_branch=\"${g_git_branch_tag}\"|"        ${arg_board_install_path}
    sed -i "s|^g_git_commit=.*|g_git_commit=\"${g_git_commit}\"|"            ${arg_board_install_path}
    if [ "${g_product_no}" == "1.0" -o "${g_product_no}" == "1.1" ]; then
        sed -i "s|^g_sync_from_user=.*|g_sync_from_user=\"${g_motion_user}\"|" ${arg_board_install_path}
        sed -i "s|^g_sync_from_pwd=.*|g_sync_from_pwd=\"${g_motion_pwd}\"|"    ${arg_board_install_path}
        sed -i "s|^g_sync_from_ip_no=.*|g_sync_from_ip_no=\"${g_motion_ip}\"|" ${arg_board_install_path}
    fi
    if [ "${arg_board}" == "app" ]; then
        sed -i "s|^g_target_user=.*|g_target_user=\"${g_app_user}\"|" ${arg_board_install_path}
        sed -i "s|^g_target_pwd=.*|g_target_pwd=\"${g_app_pwd}\"|"    ${arg_board_install_path}
        sed -i "s|^g_target_ip=.*|g_target_ip=\"${g_app_ip}\"|"       ${arg_board_install_path}
    elif [ "${arg_board}" == "motion" ]; then
        sed -i "s|^g_target_user=.*|g_target_user=\"${g_motion_user}\"|" ${arg_board_install_path}
        sed -i "s|^g_target_pwd=.*|g_target_pwd=\"${g_motion_pwd}\"|"    ${arg_board_install_path}
        sed -i "s|^g_target_ip=.*|g_target_ip=\"${g_motion_ip}\"|"       ${arg_board_install_path}
    elif [ "${arg_board}" == "cognition" ]; then
        sed -i "s|^g_target_user=.*|g_target_user=\"${g_cognition_user}\"|" ${arg_board_install_path}
        sed -i "s|^g_target_pwd=.*|g_target_pwd=\"${g_cognition_pwd}\"|"    ${arg_board_install_path}
        sed -i "s|^g_target_ip=.*|g_target_ip=\"${g_cognition_ip}\"|"       ${arg_board_install_path}
    fi
    cat ${arg_board_install_path} ${arg_board_zip_path} > ${arg_board_bin_path}
    rm -rf ${arg_board_install_path}
    rm -rf ${arg_board_zip_path}
    if [ -f ${arg_board_bin_path} ]; then
        chmod 0777 ${arg_board_bin_path}
        echo "[$LINENO][NOTE ]make [${arg_board_bin_name}] success"
    else
        echo "[$LINENO][ERROR]make [${arg_board_bin_name}] failed"
        g_install_status="func_board_zip_make"
    fi
}

function func_dev_boards_pack()
{
    local arg_board=$1
    local arg_board_pack_path=${g_pack_path}/${arg_board}
    echo "[$LINENO][NOTE ]----- pack board ${arg_board}"
    if [ ! -d ${arg_board_pack_path} ]; then
        mkdir -p ${arg_board_pack_path}
    fi
    func_board_modules_pack "${arg_board}" "${arg_board_pack_path}"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    func_board_dependencies_pack "${arg_board}" "${arg_board_pack_path}"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    func_board_zip_make "${arg_board}"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    rm -rf ${arg_board_pack_path}
}

function func_dev_bin_ota_make()
{
    local arg_update_type=$1
    local arg_dev_zip_name=${arg_update_type}_${g_install_bin_prefix}.tar.gz
    local arg_dev_zip_path=${g_pack_path}/${arg_dev_zip_name}
    echo "[$LINENO][NOTE ]----- make [${arg_update_type}]"
    cd ${g_pack_path}
    tar -zcf ${arg_dev_zip_name} ${g_pack_bin_dir}
    cd ${g_root_of_script}
    if [ -f ${arg_dev_zip_path} ]; then
        echo "[$LINENO][NOTE ]make [${arg_dev_zip_name}] success"
    else
        echo "[$LINENO][ERROR]make [${arg_dev_zip_name}] failed"
        g_install_status="func_dev_bin_ota_make"
    fi
}

function func_dev_pack_jenkins()
{
    echo "[$LINENO][NOTE ]----- make jenkins"
    local arg_jenkins_pack_info=${g_pack_path}/pack.info
    echo "bin_${g_install_bin_prefix}" > ${arg_jenkins_pack_info}
}

function func_check_board()
{
    echo "[$LINENO][NOTE ]----- check board"
    local arg_support_list="${1}"
    local arg_suport_array=""
    local arg_select_array=""
    arg_suport_array=(${arg_support_list//,/ })
    arg_select_array=(${g_select_boards//,/ })
    for arg_select_board in ${arg_select_array[@]}
    do
        if [[ "${arg_suport_array[@]}" =~ "${arg_select_board}" ]]; then
            continue
        else
            g_install_status="func_check_board"
            echo "[$LINENO][ERROR]not support board[${arg_select_board}]"
            return
        fi
    done
}

function func_md5_generate()
{
    local arg_board="$1"
    local arg_file="$2"
    local arg_info_list="$3"
    local arg_md5_str=""
    cd ${g_pack_path}
    arg_md5_str=($(md5sum ${arg_file}))
    if [ "${arg_board}" == "app" ]; then
        arg_md5_str="PACK:app:${g_app_user}:${g_app_pwd}:${g_app_ip}:${arg_file}:${arg_md5_str}:END"
    elif [ "${arg_board}" == "motion" ]; then
        arg_md5_str="PACK:motion:${g_motion_user}:${g_motion_pwd}:${g_motion_ip}:${arg_file}:${arg_md5_str}:END"
    elif [ "${arg_board}" == "cognition" ]; then
        arg_md5_str="PACK:cognition:${g_cognition_user}:${g_cognition_pwd}:${g_cognition_ip}:${arg_file}:${arg_md5_str}:END"
    fi
    echo "${arg_md5_str}" >> ${arg_info_list}
    cd ${g_root_of_script}
}

function func_dev_pack()
{
    echo "[$LINENO][NOTE ]----- start pack"
    local arg_select_board=""
    local arg_board_list=""
    arg_board_list=$(yq -r '.product.'${g_product_no_temp}'.board' ${g_project_info_path})
    if [ "${arg_board_list}" == "null" -o "${arg_board_list}" == "" ]; then
        g_install_status="func_dev_pack"
        echo "[$LINENO][ERROR]arg_board_list invalid"
        return
    fi
    echo "[$LINENO][NOTE ]support board [${arg_board_list}]"
    local arg_board_array=""
    if [ "${g_select_boards}" != "" -a "${g_select_boards}" != " " ]; then
        func_check_board "${arg_board_list}"
        if [ "${g_install_status}" != "success" ]; then
            return
        fi
        arg_board_array=(${g_select_boards//,/ })
    else
        arg_board_array=(${arg_board_list//,/ })
    fi
    if [ -d ${g_pack_bin_path} ]; then
        echo "[$LINENO][NOTE ][${g_pack_bin_path}]del"
        rm -rf ${g_pack_bin_path}
    fi
    mkdir -p ${g_pack_bin_path}
    echo "CFG_VER:0:END" > ${g_pack_bin_info_path}
    echo "INFO:${g_robot_version}:${g_product_no}:${g_pack_time}:${g_git_branch_tag}:${g_git_commit}:${g_build_type}:${g_oem}:${g_product_name}:${g_target_arch}:END" >> ${g_pack_bin_info_path}
    for arg_board in ${arg_board_array[@]}
    do
        func_dev_boards_pack "${arg_board}"
        if [ "${g_install_status}" != "success" ]; then
            echo "[$LINENO][ERROR]handle [${arg_board}] failed"
            return
        fi
        cp -rf ${g_pack_path}/${arg_board}_${g_install_short_prefix}.bin ${g_pack_bin_path}
        func_md5_generate "${arg_board}" "${arg_board}_${g_install_short_prefix}.bin" "${g_pack_bin_info_path}"
    done
    func_dev_bin_ota_make "bin"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    rm -rf ${g_pack_bin_path}
    if [ "${g_is_online}" == "y" ]; then
        func_dev_pack_jenkins
    fi
}

while true
do
    g_install_status="success"
    func_check_param
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_check_env
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_get_robot_info
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_pack_prepare
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_dev_pack
    break
done

if [ "${g_install_status}" != "success" ]; then
    echo "[$LINENO][ERROR][${g_install_status}]pack failed, del [${g_pack_path}]"
    if [ -d ${g_pack_path} ]; then
        rm -rf ${g_pack_path}
    fi
fi
sync
