# ROS2 项目运行指南

## 概述

`run.sh` 脚本是配合 `build.sh` 使用的 ROS2 项目运行工具，用于启动已构建的 ROS2 应用程序。

## 快速开始

### 1. 构建项目
```bash
# 构建 debug 版本
./build.sh --no=1.0 --build=debug

# 构建 release 版本
./build.sh --no=1.0 --build=release

# 构建指定板卡
./build.sh --no=1.0 --board=app
```

### 2. 运行项目（智能自动检测）
```bash
# 自动检测上次构建参数并运行
./run.sh

# 检查上次构建状态
./run.sh --check

# 手动指定参数（覆盖自动检测）
./run.sh --no=1.0 --build=debug

# 使用自动检测的版本，但指定板卡
./run.sh --board=app
```

### 3. 智能参数检测
`run.sh` 会自动读取 `build.sh` 生成的构建日志，无需手动指定版本号和构建类型：
- 自动检测最新的构建版本号（--no）
- 自动检测最新的构建类型（--build）
- 自动检测最新的板卡配置（--board）
- 支持手动参数覆盖自动检测

## 详细用法

### 参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `--no=` | 产品版本号 (默认自动检测) | `--no=1.0`, `--no=2.0` |
| `--build=` | 构建类型 (默认自动检测) | `--build=debug`, `--build=release` |
| `--board=` | 板卡类型 (默认自动检测) | `--board=app`, `--board=motion`, `--board=cognition` |
| `--launch=` | 指定启动文件 | `--launch=test.py`, `--launch=speech_ctrl_robdog.py` |
| `--node=` | 运行单个节点 | `--node=expression_node` |
| `--test` | 测试模式 | 启用测试相关配置 |
| `--env` | 只设置环境变量 | 不启动服务，只配置环境 |
| `--check` | 检查构建状态 | 验证构建是否完成 |

**注意**: 如果不指定 `--no`、`--build`、`--board` 参数，脚本会自动从最新的构建日志中检测这些参数。

### 常用场景

#### 1. 开发调试
```bash
# 构建并运行 debug 版本
./build.sh --no=1.0 --build=debug
./run.sh --no=1.0 --build=debug --test

# 运行单个节点进行调试
./run.sh --node=expression_node
```

#### 2. 生产环境
```bash
# 构建并运行 release 版本
./build.sh --no=1.0 --build=release
./run.sh --no=1.0 --build=release --board=app
```

#### 3. 分板卡部署
```bash
# App 板卡 (主控制)
./run.sh --board=app

# Motion 板卡 (运动控制)
./run.sh --board=motion

# Cognition 板卡 (视觉处理)
./run.sh --board=cognition
```

#### 4. 自定义启动
```bash
# 运行指定 launch 文件
./run.sh --launch=test.py
./run.sh --launch=speech_ctrl_robdog.py
./run.sh --launch=nvidia_ctrl_robdog.py

# 运行特定节点
./run.sh --node=robdog_control_node
./run.sh --node=andlink:andlink_node
```

## 环境配置

### 自动配置
脚本会自动配置以下环境：
- ROS2 Foxy 环境
- 项目构建环境
- 库路径 (LD_LIBRARY_PATH)
- RMW 实现 (CycloneDX)
- GStreamer 插件路径

### 手动配置
```bash
# 只设置环境变量，不启动服务
./run.sh --env

# 然后可以手动运行 ROS2 命令
ros2 node list
ros2 topic list
ros2 launch launch_package test.py
```

## 故障排除

### 常见问题

1. **构建目录不存在**
   ```
   错误: 安装目录不存在
   ```
   解决：先运行 `./build.sh` 构建项目

2. **包缺失错误**
   ```
   not found: "audio_recorder/share/audio_recorder/local_setup.bash"
   ```
   解决：
   ```bash
   # 方法1: 使用诊断工具
   ./run.sh --diagnose

   # 方法2: 使用修复脚本
   ./fix_missing_packages.sh

   # 方法3: 手动重新构建包含更多包的版本
   ./build.sh --no=0.1 --build=debug  # 包含 audio_recorder
   ./build.sh --no=1.0 --build=debug  # 包含更多包
   ```

3. **ROS2 环境未找到**
   ```
   错误: 找不到 ROS2 Foxy 安装
   ```
   解决：确保已安装 ROS2 Foxy

4. **权限问题**
   ```
   Permission denied
   ```
   解决：确保脚本有执行权限 `chmod +x run.sh`

### 调试技巧

1. **检查构建状态**
   ```bash
   ./run.sh --check
   ```

2. **详细诊断**
   ```bash
   ./run.sh --diagnose  # 检查缺失的包和配置
   ```

3. **查看运行状态**
   ```bash
   # 在另一个终端中
   ros2 node list
   ros2 topic list
   ```

4. **查看日志**
   ```bash
   # ROS2 日志通常在
   ~/.ros/log/
   ```

5. **包缺失问题快速修复**
   ```bash
   ./fix_missing_packages.sh  # 交互式修复工具
   ```

## 与 build.sh 的配合使用

### 完整工作流程
```bash
# 1. 构建项目
./build.sh --no=1.0 --build=debug --board=app

# 2. 检查构建
./run.sh --check

# 3. 运行项目
./run.sh --no=1.0 --build=debug --board=app

# 4. 开发调试
./run.sh --node=expression_node
```

### 参数对应关系
| build.sh 参数 | run.sh 参数 | 说明 |
|---------------|-------------|------|
| `--no=1.0` | `--no=1.0` | 产品版本号必须一致 |
| `--build=debug` | `--build=debug` | 构建类型必须一致 |
| `--board=app` | `--board=app` | 板卡类型对应启动配置 |

## 扩展功能

脚本支持以下扩展：
- 自动启动依赖服务 (UWB, SensorHub, 语音服务)
- 智能推断启动文件
- 信号处理和清理
- 状态监控和显示

更多详细信息请参考脚本源码中的注释。
