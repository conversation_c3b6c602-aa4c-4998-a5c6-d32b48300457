#!/bin/bash

# =============================================================================
# ROS2 编译脚本 - 支持多架构、多版本、调试模式
# 作者: AI Assistant
# 版本: 1.0.0
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本信息
SCRIPT_NAME="build.sh"
SCRIPT_VERSION="1.0.0"
SCRIPT_DESCRIPTION="ROS2 多架构编译脚本"

# 默认配置
DEFAULT_ROS_DISTRO="humble"
DEFAULT_ARCH="x86_64"
DEFAULT_BUILD_TYPE="Release"
DEFAULT_JOBS=$(nproc)
DEFAULT_CLEAN=false
DEFAULT_VERBOSE=false
DEFAULT_CLANGD=false
DEFAULT_DEBUG=false
DEFAULT_PACKAGES=""
DEFAULT_INSTALL=false
DEFAULT_RUN=false
DEFAULT_RUN_ARGS=""

# 全局变量
ROS_DISTRO=$DEFAULT_ROS_DISTRO
ARCH=$DEFAULT_ARCH
BUILD_TYPE=$DEFAULT_BUILD_TYPE
JOBS=$DEFAULT_JOBS
CLEAN=$DEFAULT_CLEAN
VERBOSE=$DEFAULT_VERBOSE
CLANGD=$DEFAULT_CLANGD
DEBUG=$DEFAULT_DEBUG
PACKAGES=$DEFAULT_PACKAGES
INSTALL=$DEFAULT_INSTALL
RUN=$DEFAULT_RUN
RUN_ARGS=$DEFAULT_RUN_ARGS

# 支持的ROS2版本
SUPPORTED_ROS_VERSIONS=("humble" "iron" "rolling" "galactic" "foxy")
# 支持的架构
SUPPORTED_ARCHS=("x86_64" "aarch64" "arm64" "armv7l" "armv8l")
# 支持的构建类型
SUPPORTED_BUILD_TYPES=("Release" "Debug" "RelWithDebInfo" "MinSizeRel")

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "$DEBUG" = true ]; then
        echo -e "${CYAN}[DEBUG]${NC} $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
${SCRIPT_NAME} v${SCRIPT_VERSION} - ${SCRIPT_DESCRIPTION}

用法: $0 [选项] [包名...]

选项:
    -h, --help              显示此帮助信息
    -v, --version           显示版本信息
    -d, --distro ROS_DISTRO 指定ROS2版本 (默认: ${DEFAULT_ROS_DISTRO})
    -a, --arch ARCH         指定目标架构 (默认: ${DEFAULT_ARCH})
    -t, --type BUILD_TYPE   指定构建类型 (默认: ${DEFAULT_BUILD_TYPE})
    -j, --jobs JOBS         指定并行编译任务数 (默认: ${DEFAULT_JOBS})
    -c, --clean             清理构建目录
    -V, --verbose           详细输出
    -C, --clangd            启用clangd支持
    -D, --debug             启用调试模式
    -i, --install           安装到系统
    -r, --run               编译后运行程序
    --run-args ARGS         运行参数
    --test                  运行测试
    --docs                  生成文档

支持的ROS2版本: ${SUPPORTED_ROS_VERSIONS[*]}
支持的架构: ${SUPPORTED_ARCHS[*]}
支持的构建类型: ${SUPPORTED_BUILD_TYPES[*]}

示例:
    $0                                    # 使用默认配置编译所有包
    $0 -d humble -a x86_64 network       # 编译network包
    $0 -t Debug -C -D homi_speech_interface network  # 调试模式编译指定包
    $0 -c -V                              # 清理并详细编译
    $0 --test                             # 编译并运行测试
    $0 -i --docs                          # 编译、安装并生成文档
    $0 -r network                        # 编译并运行network包
    $0 -r --run-args "--test-mode" network  # 编译并运行network包（带参数）

环境变量:
    ROS_DISTRO              ROS2版本
    ROS_ARCH               目标架构
    BUILD_TYPE             构建类型
    COLCON_JOBS            并行任务数
    CMAKE_BUILD_TYPE       CMake构建类型
    CC                      C编译器
    CXX                     C++编译器
EOF
}

# 显示版本信息
show_version() {
    echo "${SCRIPT_NAME} v${SCRIPT_VERSION}"
    echo "ROS2 多架构编译脚本"
    echo "支持版本: ${SUPPORTED_ROS_VERSIONS[*]}"
    echo "支持架构: ${SUPPORTED_ARCHS[*]}"
}

# 检查参数是否在支持列表中
check_supported() {
    local value=$1
    local list_name=$2
    shift 2
    local supported_list=("$@")
    
    for item in "${supported_list[@]}"; do
        if [ "$value" = "$item" ]; then
            return 0
        fi
    done
    
    log_error "不支持的${list_name}: $value"
    log_error "支持的${list_name}: ${supported_list[*]}"
    return 1
}

# 检测系统信息
detect_system() {
    log_info "🔍 检测系统信息..."
    
    # 检测操作系统
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS_NAME=$NAME
        OS_VERSION=$VERSION_ID
    else
        OS_NAME=$(uname -s)
        OS_VERSION=$(uname -r)
    fi
    
    # 检测架构
    SYSTEM_ARCH=$(uname -m)
    
    # 检测CPU核心数
    CPU_CORES=$(nproc)
    
    # 检测可用内存
    MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    MEMORY_GB=$((MEMORY_KB / 1024 / 1024))
    
    log_debug "操作系统: $OS_NAME $OS_VERSION"
    log_debug "系统架构: $SYSTEM_ARCH"
    log_debug "CPU核心数: $CPU_CORES"
    log_debug "可用内存: ${MEMORY_GB}GB"
    
    # 自动设置架构
    if [ "$ARCH" = "$DEFAULT_ARCH" ]; then
        case $SYSTEM_ARCH in
            x86_64) ARCH="x86_64" ;;
            aarch64|arm64) ARCH="aarch64" ;;
            armv7l) ARCH="armv7l" ;;
            armv8l) ARCH="armv8l" ;;
            *) log_warn "未知架构: $SYSTEM_ARCH, 使用默认架构: $ARCH" ;;
        esac
    fi
}

# 检查ROS2环境
check_ros2_environment() {
    log_info "🔍 检查ROS2环境..."
    
    # 检查ROS2是否安装
    if ! command -v ros2 &> /dev/null; then
        log_error "ROS2未安装或未在PATH中"
        log_info "请先安装ROS2 $ROS_DISTRO"
        return 1
    fi
    
    # 检查当前ROS2版本
    CURRENT_ROS_VERSION=$(ros2 --version 2>/dev/null | head -n1 | grep -o '[0-9]\+\.[0-9]\+' || echo "unknown")
    log_debug "当前ROS2版本: $CURRENT_ROS_VERSION"
    
    # 检查ROS_DISTRO环境变量
    if [ -n "$ROS_DISTRO" ]; then
        log_debug "ROS_DISTRO: $ROS_DISTRO"
    fi
    
    # 检查工作空间
    if [ -f "package.xml" ] || [ -f "src" ]; then
        log_debug "检测到ROS2工作空间"
    else
        log_warn "未检测到ROS2工作空间结构"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "🔍 检查编译依赖..."
    
    local missing_deps=()
    
    # 检查基本工具
    local basic_tools=("colcon" "cmake" "make" "python3")
    for tool in "${basic_tools[@]}"; do
        if ! command -v $tool &> /dev/null; then
            missing_deps+=("$tool")
        fi
    done
    
    # 检查Python包
    local python_packages=("setuptools" "wheel")
    for pkg in "${python_packages[@]}"; do
        if ! python3 -c "import $pkg" &> /dev/null; then
            missing_deps+=("python3-$pkg")
        fi
    done
    
    # 检查编译器
    if [ "$CLANGD" = true ]; then
        if ! command -v clang &> /dev/null; then
            missing_deps+=("clang")
        fi
        if ! command -v clangd &> /dev/null; then
            missing_deps+=("clangd")
        fi
    else
        if ! command -v gcc &> /dev/null; then
            missing_deps+=("gcc")
        fi
        if ! command -v g++ &> /dev/null; then
            missing_deps+=("g++")
        fi
    fi
    
    # 报告缺失的依赖
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺失依赖: ${missing_deps[*]}"
        log_info "请安装缺失的依赖包"
        return 1
    fi
    
    log_info "✅ 所有依赖检查通过"
}

# 设置编译环境
setup_build_environment() {
    log_info "🔧 设置编译环境..."
    
    # 设置环境变量
    export ROS_DISTRO=$ROS_DISTRO
    export ROS_ARCH=$ARCH
    export BUILD_TYPE=$BUILD_TYPE
    export COLCON_JOBS=$JOBS
    export CMAKE_BUILD_TYPE=$BUILD_TYPE
    
    # 设置编译器
    if [ "$CLANGD" = true ]; then
        export CC=clang
        export CXX=clang++
        log_debug "使用Clang编译器"
    else
        export CC=gcc
        export CXX=g++
        log_debug "使用GCC编译器"
    fi
    
    # 设置编译标志
    local cflags="-O2"
    local cxxflags="-O2"
    
    if [ "$BUILD_TYPE" = "Debug" ]; then
        cflags="-g -O0 -DDEBUG"
        cxxflags="-g -O0 -DDEBUG"
    elif [ "$BUILD_TYPE" = "RelWithDebInfo" ]; then
        cflags="-g -O2"
        cxxflags="-g -O2"
    fi
    
    export CFLAGS="$cflags"
    export CXXFLAGS="$cxxflags"
    
    # 设置colcon参数
    COLCON_ARGS=(
        "--cmake-args"
        "-DCMAKE_BUILD_TYPE=$BUILD_TYPE"
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=1"
    )
    
    if [ "$VERBOSE" = true ]; then
        COLCON_ARGS+=("--event-handlers" "console_direct+")
    fi
    
    if [ "$JOBS" -gt 1 ]; then
        COLCON_ARGS+=("--parallel-workers" "$JOBS")
    fi
    
    log_debug "编译环境设置完成"
    log_debug "ROS_DISTRO: $ROS_DISTRO"
    log_debug "ARCH: $ARCH"
    log_debug "BUILD_TYPE: $BUILD_TYPE"
    log_debug "JOBS: $JOBS"
    log_debug "CLANGD: $CLANGD"
}

# 清理构建目录
clean_build() {
    if [ "$CLEAN" = true ]; then
        log_info "🧹 清理构建目录..."
        rm -rf build/ install/ log/
        log_info "✅ 清理完成"
    fi
}

# 检查包是否存在
check_packages() {
    if [ -n "$PACKAGES" ]; then
        log_info "🔍 检查指定包..."
        local missing_packages=()
        
        for pkg in $PACKAGES; do
            if [ ! -d "src/$pkg" ] && [ ! -d "$pkg" ]; then
                missing_packages+=("$pkg")
            fi
        done
        
        if [ ${#missing_packages[@]} -gt 0 ]; then
            log_error "找不到包: ${missing_packages[*]}"
            return 1
        fi
        
        log_info "✅ 所有指定包存在"
    fi
}

# 编译包
build_packages() {
    log_info "🔨 开始编译..."
    
    local build_cmd="colcon build"
    
    # 添加包选择
    if [ -n "$PACKAGES" ]; then
        build_cmd="$build_cmd --packages-select $PACKAGES"
    fi
    
    # 添加colcon参数
    for arg in "${COLCON_ARGS[@]}"; do
        build_cmd="$build_cmd $arg"
    done
    
    log_debug "编译命令: $build_cmd"
    
    # 执行编译
    if eval "$build_cmd"; then
        log_info "✅ 编译成功"
        return 0
    else
        log_error "❌ 编译失败"
        return 1
    fi
}

# 安装包
install_packages() {
    if [ "$INSTALL" = true ]; then
        log_info "📦 安装包..."
        
        if colcon build --packages-select $PACKAGES --cmake-args -DCMAKE_INSTALL_PREFIX=/usr/local; then
            log_info "✅ 安装成功"
        else
            log_error "❌ 安装失败"
            return 1
        fi
    fi
}

# 运行测试
run_tests() {
    if [ "$1" = "--test" ]; then
        log_info "🧪 运行测试..."
        
        if colcon test --packages-select $PACKAGES; then
            log_info "✅ 测试通过"
            
            # 生成测试报告
            colcon test-result --verbose
        else
            log_error "❌ 测试失败"
            return 1
        fi
    fi
}

# 运行程序
run_program() {
    if [ "$RUN" = true ]; then
        log_info "🚀 运行程序..."
        
        # 设置环境
        source install/setup.bash
        
        # 检查是否有指定包
        if [ -n "$PACKAGES" ]; then
            for pkg in $PACKAGES; do
                log_info "运行包: $pkg"
                
                # 查找可执行文件
                local executable=""
                
                # 检查常见的可执行文件位置
                if [ -f "install/$pkg/lib/$pkg/${pkg}_node" ]; then
                    executable="install/$pkg/lib/$pkg/${pkg}_node"
                elif [ -f "install/$pkg/bin/${pkg}_node" ]; then
                    executable="install/$pkg/bin/${pkg}_node"
                elif [ -f "install/$pkg/lib/$pkg/${pkg}" ]; then
                    executable="install/$pkg/lib/$pkg/${pkg}"
                elif [ -f "install/$pkg/bin/$pkg" ]; then
                    executable="install/$pkg/bin/$pkg"
                elif [ -f "src/$pkg/$pkg/${pkg}_node.py" ]; then
                    executable="python3 src/$pkg/$pkg/${pkg}_node.py"
                elif [ -f "src/$pkg/$pkg/${pkg}.py" ]; then
                    executable="python3 src/$pkg/$pkg/${pkg}.py"
                else
                    log_warn "未找到 $pkg 的可执行文件，尝试使用ros2 run"
                    executable="ros2 run $pkg ${pkg}_node"
                fi
                
                if [ -n "$executable" ]; then
                    log_info "执行: $executable $RUN_ARGS"
                    
                    # 运行程序
                    if eval "$executable $RUN_ARGS"; then
                        log_info "✅ $pkg 运行成功"
                    else
                        log_error "❌ $pkg 运行失败"
                        return 1
                    fi
                else
                    log_error "❌ 未找到 $pkg 的可执行文件"
                    return 1
                fi
            done
        else
            log_info "运行所有包的主程序..."
            
            # 尝试运行常见的启动文件
            if [ -f "src/launch_package/launch/robot_launch.py" ]; then
                log_info "启动机器人程序..."
                ros2 launch launch_package robot_launch.py $RUN_ARGS
            elif [ -f "src/network/network/network_node.py" ]; then
                log_info "启动网络节点..."
                python3 src/network/network/network_node.py $RUN_ARGS
            else
                log_warn "未找到主程序，请指定具体的包名"
                return 1
            fi
        fi
    fi
}

# 生成文档
generate_docs() {
    if [ "$1" = "--docs" ]; then
        log_info "📚 生成文档..."
        
        # 检查doxygen
        if command -v doxygen &> /dev/null; then
            for pkg in $PACKAGES; do
                if [ -f "src/$pkg/Doxyfile" ]; then
                    log_info "为 $pkg 生成文档..."
                    (cd "src/$pkg" && doxygen Doxyfile)
                fi
            done
            log_info "✅ 文档生成完成"
        else
            log_warn "Doxygen未安装，跳过文档生成"
        fi
    fi
}

# 设置clangd支持
setup_clangd() {
    if [ "$CLANGD" = true ]; then
        log_info "🔧 设置clangd支持..."
        
        # 创建compile_commands.json的符号链接
        if [ -f "build/compile_commands.json" ]; then
            ln -sf "$(pwd)/build/compile_commands.json" .
            log_info "✅ compile_commands.json链接已创建"
        else
            log_warn "未找到compile_commands.json"
        fi
        
        # 创建.clangd配置文件
        cat > .clangd << EOF
CompileFlags:
  Add: [-std=c++17, -I/opt/ros/$ROS_DISTRO/include]
  Remove: [-W*]
Diagnostics:
  ClangTidy:
    Add: [modernize*, performance*, readability*]
Index:
  Background: Build
EOF
        log_info "✅ .clangd配置文件已创建"
    fi
}

# 显示编译信息
show_build_info() {
    log_info "📊 编译信息汇总:"
    echo "  ROS2版本: $ROS_DISTRO"
    echo "  目标架构: $ARCH"
    echo "  构建类型: $BUILD_TYPE"
    echo "  并行任务: $JOBS"
    echo "  编译器: $($CC --version | head -n1)"
    echo "  调试模式: $DEBUG"
    echo "  Clangd支持: $CLANGD"
    echo "  详细输出: $VERBOSE"
    echo "  清理构建: $CLEAN"
    echo "  安装模式: $INSTALL"
    if [ -n "$PACKAGES" ]; then
        echo "  指定包: $PACKAGES"
    else
        echo "  编译所有包"
    fi
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                show_version
                exit 0
                ;;
            -d|--distro)
                ROS_DISTRO="$2"
                shift 2
                ;;
            -a|--arch)
                ARCH="$2"
                shift 2
                ;;
            -t|--type)
                BUILD_TYPE="$2"
                shift 2
                ;;
            -j|--jobs)
                JOBS="$2"
                shift 2
                ;;
            -c|--clean)
                CLEAN=true
                shift
                ;;
            -V|--verbose)
                VERBOSE=true
                shift
                ;;
            -C|--clangd)
                CLANGD=true
                shift
                ;;
            -D|--debug)
                DEBUG=true
                shift
                ;;
            -i|--install)
                INSTALL=true
                shift
                ;;
            -r|--run)
                RUN=true
                shift
                ;;
            --run-args)
                RUN_ARGS="$2"
                shift 2
                ;;
            --test)
                TEST_MODE=true
                shift
                ;;
            --docs)
                DOCS_MODE=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$PACKAGES" ]; then
                    PACKAGES="$1"
                else
                    PACKAGES="$PACKAGES $1"
                fi
                shift
                ;;
        esac
    done
    
    # 验证参数
    check_supported "$ROS_DISTRO" "ROS2版本" "${SUPPORTED_ROS_VERSIONS[@]}" || exit 1
    check_supported "$ARCH" "架构" "${SUPPORTED_ARCHS[@]}" || exit 1
    check_supported "$BUILD_TYPE" "构建类型" "${SUPPORTED_BUILD_TYPES[@]}" || exit 1
    
    # 显示开始信息
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  ROS2 多架构编译脚本${NC}"
    echo -e "${BLUE}================================${NC}"
    
    # 执行编译流程
    detect_system
    check_ros2_environment || exit 1
    check_dependencies || exit 1
    setup_build_environment
    show_build_info
    clean_build
    check_packages || exit 1
    build_packages || exit 1
    setup_clangd
    install_packages
    run_tests "$TEST_MODE"
    generate_docs "$DOCS_MODE"
    run_program
    
    # 显示完成信息
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}  编译完成！${NC}"
    echo -e "${GREEN}================================${NC}"
    
    if [ "$INSTALL" = true ]; then
        log_info "包已安装到系统"
    fi
    
    if [ "$CLANGD" = true ]; then
        log_info "clangd支持已启用，可在IDE中使用"
    fi
    
    log_info "使用以下命令设置环境:"
    echo "  source install/setup.bash"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
