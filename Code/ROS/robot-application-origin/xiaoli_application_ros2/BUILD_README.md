# ROS2 编译脚本使用指南

## 📋 概述

本项目提供了两个功能完善的ROS2编译脚本，支持多架构、多版本、调试模式等功能：

- `build.sh` - 基础编译脚本
- `build_advanced.sh` - 高级编译脚本（支持配置文件）

## 🚀 快速开始

### 基础编译脚本

```bash
# 进入项目目录
cd xiaoli_application_ros2

# 使用默认配置编译所有包
./build.sh

# 编译指定包
./build.sh network homi_speech_interface

# 调试模式编译
./build.sh -t Debug -C -D network
```

### 高级编译脚本

```bash
# 使用配置文件编译
./build_advanced.sh -c build_config.yaml

# 编译指定包
./build_advanced.sh network homi_speech_interface

# 完整功能编译
./build_advanced.sh -t Debug -C -D --test --docs network
```

## 📖 详细使用说明

### 基础编译脚本 (build.sh)

#### 基本语法
```bash
./build.sh [选项] [包名...]
```

#### 主要选项
- `-h, --help` - 显示帮助信息
- `-v, --version` - 显示版本信息
- `-d, --distro ROS_DISTRO` - 指定ROS2版本
- `-a, --arch ARCH` - 指定目标架构
- `-t, --type BUILD_TYPE` - 指定构建类型
- `-j, --jobs JOBS` - 指定并行编译任务数
- `-c, --clean` - 清理构建目录
- `-V, --verbose` - 详细输出
- `-C, --clangd` - 启用clangd支持
- `-D, --debug` - 启用调试模式
- `-i, --install` - 安装到系统
- `-r, --run` - 编译后运行程序
- `--run-args ARGS` - 运行参数
- `--test` - 运行测试
- `--docs` - 生成文档

#### 使用示例

```bash
# 1. 基本编译
./build.sh

# 2. 编译指定包
./build.sh network

# 3. 调试模式编译
./build.sh -t Debug network

# 4. 启用clangd支持
./build.sh -C network

# 5. 清理并重新编译
./build.sh -c network

# 6. 详细输出编译
./build.sh -V network

# 7. 编译并运行测试
./build.sh --test network

# 8. 编译并生成文档
./build.sh --docs network

# 9. 安装到系统
./build.sh -i network

# 10. 多包编译
./build.sh network homi_speech_interface launch_package

# 11. 编译并运行程序
./build.sh -r network

# 12. 编译并运行程序（带参数）
./build.sh -r --run-args "--test-mode" network
```

### 高级编译脚本 (build_advanced.sh)

#### 基本语法
```bash
./build_advanced.sh [选项] [包名...]
```

#### 主要选项
- `-c, --config FILE` - 指定配置文件
- `-d, --distro ROS_DISTRO` - 指定ROS2版本
- `-a, --arch ARCH` - 指定目标架构
- `-t, --type BUILD_TYPE` - 指定构建类型
- `-j, --jobs JOBS` - 指定并行编译任务数
- `--clean` - 清理构建目录
- `-V, --verbose` - 详细输出
- `-C, --clangd` - 启用clangd支持
- `-D, --debug` - 启用调试模式
- `-i, --install` - 安装到系统
- `-r, --run` - 编译后运行程序
- `--run-args ARGS` - 运行参数
- `--test` - 运行测试
- `--docs` - 生成文档
- `--profile` - 性能分析
- `--sanitize` - 启用sanitizer
- `--coverage` - 代码覆盖率
- `--cross-compile` - 交叉编译
- `--docker` - 使用Docker编译
- `--dry-run` - 试运行模式
- `--cache` - 启用编译缓存
- `--no-cache` - 禁用编译缓存

#### 使用示例

```bash
# 1. 使用配置文件编译
./build_advanced.sh -c build_config.yaml

# 2. 调试模式编译
./build_advanced.sh -t Debug -C -D network

# 3. 启用sanitizer编译
./build_advanced.sh --sanitize network

# 4. 启用覆盖率编译
./build_advanced.sh --coverage network

# 5. 试运行模式
./build_advanced.sh --dry-run network

# 6. 启用缓存编译
./build_advanced.sh --cache network

# 7. 完整功能编译
./build_advanced.sh -t Debug -C -D --test --docs --coverage network

# 8. 性能分析编译
./build_advanced.sh --profile network
```

## ⚙️ 配置文件说明

### build_config.yaml

高级编译脚本支持YAML配置文件，可以定义：

- 支持的ROS2版本
- 支持的架构
- 构建类型配置
- 包特定配置
- 编译器配置
- 测试配置
- 文档配置
- 环境变量配置

#### 配置文件示例

```yaml
# 全局配置
global:
  default_ros_distro: "humble"
  default_arch: "x86_64"
  default_build_type: "Release"
  default_jobs: 4

# 支持的ROS2版本
ros_distros:
  humble:
    version: "2.0.0"
    supported_archs: ["x86_64", "aarch64", "arm64"]
    cmake_args: ["-DCMAKE_BUILD_TYPE=Release"]

# 包特定配置
packages:
  network:
    dependencies: ["homi_speech_interface"]
    build_type: "Release"
    cmake_args: ["-DNETWORK_DEBUG=OFF"]
    test_enabled: true
```

## 🏗️ 支持的配置

### ROS2版本
- `humble` (推荐)
- `iron`
- `rolling`
- `galactic`
- `foxy`

### 目标架构
- `x86_64` (Intel/AMD 64位)
- `aarch64` (ARM 64位)
- `arm64` (ARM 64位)
- `armv7l` (ARM 32位)
- `armv8l` (ARM 32位)

### 构建类型
- `Release` - 发布版本（优化）
- `Debug` - 调试版本
- `RelWithDebInfo` - 带调试信息的发布版本
- `MinSizeRel` - 最小体积版本

### 编译器
- `gcc/g++` - GNU编译器（默认）
- `clang/clang++` - Clang编译器
- `clangd` - Clang语言服务器

## 🔧 环境要求

### 必需依赖
- ROS2 (humble/iron/rolling/galactic/foxy)
- colcon
- cmake
- make
- python3
- gcc/g++ 或 clang/clang++

### 可选依赖
- clangd (用于IDE支持)
- doxygen (用于文档生成)
- lcov (用于代码覆盖率)
- valgrind (用于内存检查)

### 安装命令

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install build-essential cmake python3-pip
pip3 install colcon-common-extensions

# 安装clangd (可选)
sudo apt install clangd

# 安装doxygen (可选)
sudo apt install doxygen

# 安装lcov (可选)
sudo apt install lcov
```

## 📊 编译选项详解

### 调试模式 (-D, --debug)
启用详细的调试输出，包括：
- 系统信息检测
- 依赖检查过程
- 编译环境设置
- 详细的编译命令

### Clangd支持 (-C, --clangd)
启用clangd语言服务器支持：
- 自动创建compile_commands.json链接
- 生成.clangd配置文件
- 支持IDE智能提示和代码补全

### 测试模式 (--test)
编译后自动运行测试：
- 执行colcon test
- 生成测试报告
- 显示测试结果

### 文档生成 (--docs)
自动生成文档：
- 检查Doxyfile配置
- 为每个包生成文档
- 支持HTML和PDF格式

### Sanitizer (--sanitize)
启用内存和未定义行为检查：
- AddressSanitizer (ASan)
- UndefinedBehaviorSanitizer (UBSan)
- 帮助发现内存错误和未定义行为

### 代码覆盖率 (--coverage)
启用代码覆盖率分析：
- 生成覆盖率报告
- 支持HTML和XML格式
- 帮助识别未测试的代码

### 试运行模式 (--dry-run)
不执行实际编译，只显示将要执行的命令：
- 验证配置正确性
- 检查依赖关系
- 预览编译过程

### 运行模式 (-r, --run)
编译后自动运行程序：
- 自动查找可执行文件
- 支持多种可执行文件格式
- 支持运行参数传递
- 自动设置ROS2环境

## 🐛 常见问题解决

### 1. 编译错误

```bash
# 清理并重新编译
./build.sh -c network

# 检查依赖
./build.sh -D network

# 详细输出查看错误
./build.sh -V network
```

### 2. 导入错误

```bash
# 确保环境正确设置
source install/setup.bash

# 检查Python路径
export PYTHONPATH="$PWD/src:$PYTHONPATH"
```

### 3. 权限问题

```bash
# 给脚本执行权限
chmod +x build.sh build_advanced.sh

# 使用sudo安装（如果需要）
sudo ./build.sh -i network
```

### 4. 内存不足

```bash
# 减少并行任务数
./build.sh -j 2 network

# 清理构建目录
./build.sh -c network
```

### 5. 磁盘空间不足

```bash
# 清理构建目录
./build.sh -c

# 使用最小构建类型
./build.sh -t MinSizeRel network
```

## 📈 性能优化

### 1. 并行编译
```bash
# 使用所有CPU核心
./build.sh -j $(nproc) network

# 使用指定核心数
./build.sh -j 8 network
```

### 2. 编译缓存
```bash
# 启用缓存（高级脚本）
./build_advanced.sh --cache network

# 禁用缓存
./build_advanced.sh --no-cache network
```

### 3. 增量编译
```bash
# 只编译修改的包
./build.sh network

# 强制重新编译
./build.sh -c network
```

## 🔍 调试技巧

### 1. 查看编译信息
```bash
# 详细输出
./build.sh -V network

# 调试模式
./build.sh -D network

# 查看编译命令
./build_advanced.sh --dry-run network
```

### 2. 检查依赖
```bash
# 检查系统依赖
./build.sh -D

# 检查包依赖
colcon graph --packages-select network
```

### 3. 性能分析
```bash
# 启用性能分析
./build_advanced.sh --profile network

# 查看编译时间
time ./build.sh network
```

## 📝 日志和输出

### 日志文件
- 构建日志: `build_YYYYMMDD_HHMMSS.log`
- 测试日志: `log/latest_test/`
- 编译日志: `log/latest_build/`

### 输出目录
- 构建目录: `build/`
- 安装目录: `install/`
- 日志目录: `log/`
- 文档目录: `docs/`

## 🎯 最佳实践

### 1. 开发环境
```bash
# 开发时使用调试模式
./build.sh -t Debug -C -D network

# 启用测试
./build.sh --test network
```

### 2. 生产环境
```bash
# 生产环境使用发布模式
./build.sh -t Release network

# 启用优化
./build.sh -j $(nproc) network
```

### 3. CI/CD环境
```bash
# 自动化构建
./build.sh --test --coverage network

# 生成文档
./build.sh --docs network
```

## 📚 相关文档

- [ROS2官方文档](https://docs.ros.org/)
- [colcon文档](https://colcon.readthedocs.io/)
- [CMake文档](https://cmake.org/documentation/)
- [Clangd文档](https://clangd.llvm.org/)

## 🤝 贡献指南

欢迎提交问题和改进建议：

1. 检查现有问题
2. 创建新问题或拉取请求
3. 遵循代码风格指南
4. 添加适当的测试和文档

---

**现在你可以开始使用这些强大的编译脚本了！** 🎉 