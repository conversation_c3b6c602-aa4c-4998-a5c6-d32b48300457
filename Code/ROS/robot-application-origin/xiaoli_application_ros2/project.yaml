cfg_ver: 1
module:
  robot_body: "1.1.0"
  a2dp: "1.1.0"
  andlink: "1.1.0"
  ble: "1.1.0"
  audio_player: "1.1.0"
  audio_recorder: "1.1.0"
  cmcc_rtc: "1.1.0"
  expression: "1.1.0"
  follow_me_bringup: "1.1.0"
  follow_msgs: "1.1.0"
  follow_rcs: "1.1.0"
  follow_strategy: "1.1.0"
  handlernetwork: "1.1.0"
  homi_audio_player: "1.1.0"
  homi_speech: "1.1.0"
  homi_speech_interface: "1.1.0"
  launch_package: "1.1.0"
  live_stream: "1.1.0"
  nvidia_control: "1.1.0"
  robdog_control: "1.1.0"
  robdog_qt_tools: "1.1.0"
  rtsp_image_capture: "1.1.0"
  video_gst: "1.1.0"
  video_gst_py: "1.1.0"
  pixel2world_pose: "1.1.0"
  video_gst_nv: "1.1.0"
  rcutils: "1.1.0"
  network: "1.1.0"
  video_gst_neck: "1.1.0"
  pwm_touch: "1.1.0"
  peripherals: "1.1.0"
  uwb_package: "1.1.0"
  uploadlog: "1.1.0"
  ultrasonic: "1.1.0"
  point_process: "1.1.0"
dependency:
  alg:
    version: "1.1.0"
  ObuVoice:
    version: "1.1.0"
  service:
    version: "1.1.0"
  lib:
    version: "1.1.0"
  uwb:
    version: "1.1.0"
  SensorHub:
    version: "1.1.0"
  factory:
    version: "1.1.0"
product:
  dev_0_0:
    id: 0.0
    desc: "for test"
    oem: cmcc
    name: xiaoli
    type: homibot
    os: ubuntu2004
    arch: x86_64
    skip: mock,live_stream,robdog_qt_tools,rtsp_image_capture
    board: app
    app:
      module: "launch_package,homi_speech_interface,expression,
              robdog_control,homi_speech"
      dependency: service,lib
      user: ""
      pwd: ""
      ip: ""
  dev_0_1:
    id: 0.1
    desc: "lubancat"
    oem: cmcc
    name: xiaoli
    type: homibot
    os: ubuntu2004
    arch: aarch64
    skip: mock,robdog_qt_tools,rtsp_image_capture
    board: app
    app:
      module: "rcutils,launch_package,homi_speech_interface,audio_recorder,expression,
              robdog_control,audio_player,video_gst,cmcc_rtc,homi_speech,live_stream,
              homi_audio_player,uploadlog"
      dependency: ObuVoice,service,lib
      user: "cat"
      pwd: "temppwd"
      ip: "130"
  dev_1_0:
    id: 1.0
    desc: "old hardware"
    oem: deepRobot
    name: xiaoli
    type: homibot
    os: ubuntu2004
    arch: aarch64
    skip: mock,robdog_qt_tools,rtsp_image_capture,video_gst_neck
    board: motion,app,cognition
    app:
      module: "rcutils,launch_package,homi_speech_interface,audio_recorder,expression,
              robdog_control,audio_player,video_gst,cmcc_rtc,homi_speech,live_stream,
              homi_audio_player"
      dependency: ObuVoice,service,lib
      user: "cat"
      pwd: "temppwd"
      ip: "130"
    motion:
      module: rcutils,launch_package,homi_speech_interface,andlink,handlernetwork,ble,a2dp
      dependency: service
      user: "ysc"
      pwd: "'"
      ip: "120"
    cognition:
      module: "rcutils,launch_package,homi_speech_interface,cmcc_rtc,nvidia_control,
              follow_me_bringup,follow_msgs,follow_rcs,follow_strategy,pixel2world_pose,
              video_gst_nv"
      dependency: service,lib
      user: "nvidia"
      pwd: "nvidia"
      ip: "110"
  dev_1_1:
    id: 1.1
    desc: "new hardware"
    oem: deepRobot
    name: xiaoli
    type: homibot
    os: ubuntu2004
    arch: aarch64
    skip: mock,robdog_qt_tools,rtsp_image_capture,video_gst_neck
    board: motion,app,cognition
    app:
      module: "rcutils,launch_package,homi_speech_interface,audio_recorder,expression,
              robdog_control,audio_player,video_gst,cmcc_rtc,homi_speech,live_stream,
              homi_audio_player"
      dependency: ObuVoice,service,lib,uwb,SensorHub
      user: "ysc"
      pwd: "'"
      ip: "130"
    motion:
      module: rcutils,launch_package,homi_speech_interface,andlink,network,ble,a2dp
      dependency: service
      user: "ysc"
      pwd: "'"
      ip: "120"
    cognition:
      module: "rcutils,launch_package,homi_speech_interface,cmcc_rtc,nvidia_control,
              follow_me_bringup,follow_msgs,follow_rcs,follow_strategy,pixel2world_pose,
              video_gst_nv"
      dependency: service,lib
      user: "nvidia"
      pwd: "nvidia"
      ip: "110"
  dev_2_0:
    id: 2.0
    desc: "hardware"
    oem: unitree
    name: xiaoli
    type: homibot
    os: ubuntu2004
    arch: aarch64
    skip: mock,robdog_qt_tools,rtsp_image_capture,video_gst_nv
    board: app
    app:
      module: "rcutils,launch_package,homi_speech_interface,andlink,network,ble,a2dp,
              pwm_touch,peripherals,audio_recorder,expression,robdog_control,
              audio_player,video_gst,cmcc_rtc,homi_speech,live_stream,homi_audio_player,
              nvidia_control,follow_me_bringup,follow_msgs,follow_rcs,follow_strategy,
              pixel2world_pose,video_gst_neck,uwb_package,uploadlog,ultrasonic,point_process"
      dependency: ObuVoice,service,lib,factory
      user: "root"
      pwd: "123"
      ip: "99"
