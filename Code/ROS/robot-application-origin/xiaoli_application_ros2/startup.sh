#!/bin/bash

g_work_dir=$(pwd)
g_root_of_script=$(dirname $(readlink -f "$0"))

function func_usage()
{
    echo "usage:"
    echo "    --help/--h"
    echo "    [--no=]"
    echo "    [--build=] debug/release"
    exit
}

g_temp_param=""
g_product_no="1.0"
g_build_type="release"
while [ $# -gt 0 ]; do
    g_temp_param=$1
    case ${g_temp_param} in
        --help | --h)
            func_usage
            ;;
        --no=*)
            g_product_no=${g_temp_param#*=}
            ;;
        --build=*)
            g_build_type=${g_temp_param#*=}
            ;;
        *)
            func_usage
            ;;
    esac
    shift
done

echo "[NOTE ]product: ${g_product_no}"
echo "[NOTE ]build:   ${g_build_type}"

g_run_path=/mine/robot-application-02/xiaoli_application_ros2

docker exec -it 3267e2a76b71120ed2b93977d7e2984efc63cb2c53edb02925aaf4ca51f11a04 /bin/bash -c "cd ${g_run_path} && bash ./build.sh --no=${g_product_no} --build=${g_build_type} --online"

if [ -f /root/install.tar.gz ]; then
    echo "[NOTE ][/root/install.tar.gz]del"
    rm -rf /root/install.tar.gz
fi
sync
