#!/bin/bash

g_work_dir=$(pwd)
g_root_of_script=$(dirname $(readlink -f "$0"))

g_install_status="success"
g_current_arch=$(uname -m)
g_target_no="1.0"
g_target_md5=""
g_target_board="app"
g_target_version="1.1.0"
g_target_oem="cmcc"
g_target_product="xiaoli"
g_target_arch="aarch64"
g_pack_time=""
g_build_type=""
g_git_branch=""
g_git_commit=""
g_target_user=""
g_target_pwd=""
g_target_ip=""
g_sync_from_user=""
g_sync_from_pwd=""
g_sync_from_ip_no=""
function func_check_default_info()
{
    echo "[$LINENO][NOTE ]----- check default info"
    echo "[$LINENO][NOTE ]no:      ${g_target_no}"
    echo "[$LINENO][NOTE ]md5:     ${g_target_md5}"
    echo "[$LINENO][NOTE ]board:   ${g_target_board}"
    echo "[$LINENO][NOTE ]version: ${g_target_version}"
    echo "[$LINENO][NOTE ]oem:     ${g_target_oem}"
    echo "[$LINENO][NOTE ]product: ${g_target_product}"
    echo "[$LINENO][NOTE ]arch:    ${g_target_arch}"
    echo "[$LINENO][NOTE ]pack:    ${g_pack_time}"
    echo "[$LINENO][NOTE ]build:   ${g_build_type}"
    echo "[$LINENO][NOTE ]branch:  ${g_git_branch}"
    echo "[$LINENO][NOTE ]commit:  ${g_git_commit}"
    echo "[$LINENO][NOTE ]usr:     ${g_target_user}"
    # echo "[$LINENO][NOTE ]pwd:     ${g_target_pwd}"
    echo "[$LINENO][NOTE ]ip no:   ${g_target_ip}"
    echo "[$LINENO][NOTE ]r usr:   ${g_sync_from_user}"
    # echo "[$LINENO][NOTE ]r pwd:   ${g_sync_from_pwd}"
    echo "[$LINENO][NOTE ]r ip no: ${g_sync_from_ip_no}"
    if [ "${g_target_arch}" != "${g_current_arch}" ]; then
        echo "[$LINENO][ERROR][${g_current_arch}:${g_target_arch}]dismatch"
        g_install_status="func_check_default_info"
        return
    fi
}

function func_usage()
{
    echo "usage:"
    echo "    --help/--h"
    echo "    [--select=] robdog_control,..."
    echo "    [--install=]"
    echo "    [--handle=] extract/stop/start/revert(with --backup)"
    echo "    [--usr=]"
    echo "    [--pwd=]"
    echo "    --base"
    echo "    --test"
    echo "    --forall"
    echo "    --backup"
    echo "    --ota"
    func_check_default_info
    exit
}

g_temp_param=""
g_select_packages=""
g_install_path=""
g_handle_type=""
g_input_user=""
g_input_pwd=""
g_robot_is_pro="y"
g_is_test_dev="n"
g_is_update_all="n"
g_is_ota="n"
g_is_backup="n"
while [ $# -gt 0 ]; do
    g_temp_param=$1
    case ${g_temp_param} in
        --help | --h)
            func_usage
            ;;
        --select=*)
            g_select_packages=${g_temp_param#*=}
            ;;
        --install=*)
            g_install_path=${g_temp_param#*=}
            ;;
        --handle=*)
            g_handle_type=${g_temp_param#*=}
            ;;
        --usr=*)
            g_input_user=${g_temp_param#*=}
            ;;
        --pwd=*)
            g_input_pwd=${g_temp_param#*=}
            ;;
        --base)
            g_robot_is_pro="n"
            ;;
        --test)
            g_is_test_dev="y"
            ;;
        --forall)
            g_is_update_all="y"
            ;;
        --backup)
            g_is_backup="y"
            ;;
        --ota)
            g_is_ota="y"
            g_is_update_all="y"
            g_is_backup="y"
            ;;
        *)
            func_usage
            ;;
    esac
    shift
done

g_install_time=$(date +%Y%m%d%H%M%S)
g_cur_user_name=$(whoami)
function func_check_param()
{
    echo "[$LINENO][NOTE ]----- check param"
    echo "[$LINENO][NOTE ]time:    ${g_install_time}"
    echo "[$LINENO][NOTE ]user:    ${g_cur_user_name}"
    echo "[$LINENO][NOTE ]select:  ${g_select_packages}"
    echo "[$LINENO][NOTE ]install: ${g_install_path}"
    echo "[$LINENO][NOTE ]handle:  ${g_handle_type}"
    echo "[$LINENO][NOTE ]usr:     ${g_input_user}"
    echo "[$LINENO][NOTE ]pwd:     ${g_input_pwd}"
    echo "[$LINENO][NOTE ]is_pro:  ${g_robot_is_pro}"
    echo "[$LINENO][NOTE ]is_test: ${g_is_test_dev}"
    echo "[$LINENO][NOTE ]for all: ${g_is_update_all}"
    echo "[$LINENO][NOTE ]backup:  ${g_is_backup}"
    echo "[$LINENO][NOTE ]is ota:  ${g_is_ota}"
    if [ "${g_target_no}" == "2.0" ]; then
        echo "[$LINENO][NOTE ]unitree, only one board"
        g_is_update_all="n"
    fi
}

function func_check_env()
{
    echo "[$LINENO][NOTE ]----- check env"
    if [ "${g_is_update_all}" == "y" ]; then
        if command -v sshpass &> /dev/null; then
            echo "[$LINENO][NOTE ]sshpass is ready"
        else
            echo "[$LINENO][ERROR]please [sudo apt install sshpass]"
            g_install_status="func_check_env"
            return
        fi
        # if command -v scp &> /dev/null; then
        #     echo "[$LINENO][NOTE ]scp is ready"
        # else
        #     echo "[$LINENO][ERROR]please [sudo apt install scp]"
        #     g_install_status="func_check_env"
        #     return
        # fi
        if command -v rsync &> /dev/null; then
            echo "[$LINENO][NOTE ]rsync is ready"
        else
            echo "[$LINENO][ERROR]please [sudo apt install rsync]"
            g_install_status="func_check_env"
            return
        fi
    fi
    if command -v md5sum &> /dev/null; then
        echo "[$LINENO][NOTE ]md5sum is ready"
    else
        echo "[$LINENO][ERROR]please [sudo apt install md5sum]"
        g_install_status="func_check_env"
        return
    fi
}

g_dev_cfg_root=/etc/cmcc_robot
g_cmcc_sl_root=/usr/bin/cmcc_robot
g_cmcc_sl_install=${g_cmcc_sl_root}/install
g_install_dst_path=""
g_install_quick_path=${g_root_of_script}/quick_${g_install_time}
g_install_zip_name=${g_target_board}_${g_install_time}.tar.gz
g_install_zip_path=${g_install_quick_path}/${g_install_zip_name}
g_install_sh_name=${g_target_board}_${g_install_time}.sh
g_install_sh_path=${g_install_quick_path}/${g_install_sh_name}
g_install_src_path=${g_install_quick_path}/${g_target_board}
g_install_src_res_path=${g_install_src_path}/env
g_version_list_path=${g_install_src_path}/version.list
g_install_dst_env_path=""
g_install_dst_srv_path=""
g_install_list_path=""
g_sys_srv_path="/etc/systemd/system"
g_cmd_exe_status="success"
g_user_name=""
g_user_pwd=""
g_user_ip=""
function func_run_sudo_command()
{
    local arg_command="$1"
    # echo "[$LINENO][NOTE ]pwd: ${g_user_pwd}, cmd: ${arg_command}"
    echo "${g_user_pwd}" | sudo -S bash -c "${arg_command}"
    if [ $? -ne 0 ]; then
        echo "[$LINENO][ERROR][${arg_command}]failed"
        g_cmd_exe_status="func_run_sudo_command"
        return
    fi
}

function func_del_dir_or_file_safe()
{
    local arg_to_del="$1"
    local arg_is_dir="$2"
    local arg_is_sudo="$3"
    local arg_timeout=1
    while true
    do
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR][${arg_to_del}]del timeout, ${arg_timeout}"
            g_install_status="func_del_dir_or_file_safe"
            break
        fi
        if [ "${arg_is_dir}" == "y" ]; then
            if [ ! -d ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]not exist"
                break
            fi
        else
            if [ ! -f ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]not exist"
                break
            fi
        fi
        if [ "${arg_is_sudo}" == "y" ]; then
            func_run_sudo_command "rm -rf ${arg_to_del}"
        else
            rm -rf ${arg_to_del}
        fi
        if [ "${arg_is_dir}" == "y" ]; then
            if [ ! -d ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]del ok"
                break
            fi
        else
            if [ ! -f ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]del ok"
                break
            fi
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 5
    done
}

function func_check_pwd()
{
    echo "[$LINENO][NOTE ]----- check pwd"
    if [ "${g_user_pwd}" == "" -o "${g_user_pwd}" == " " ]; then
        echo "[$LINENO][ERROR]pwd invalid"
        g_install_status="func_check_pwd"
        return
    fi
    func_run_sudo_command "whoami"
    if [ "${g_cmd_exe_status}" != "success" ]; then
        echo "[$LINENO][ERROR][${g_user_pwd}]invalid"
        g_install_status="func_check_pwd"
        return
    fi
}

g_network_cfg_path=${g_dev_cfg_root}/network.ini
g_ip_prefix="192.168.1"
g_board_ip=""
g_sync_from_ip=""
function func_check_network_ini()
{
    echo "[$LINENO][NOTE ]----- check network ini"
    if [ "${g_target_no}" == "2.0" ]; then
        g_ip_prefix="192.168.123"
    fi
    if [ ! -f ${g_network_cfg_path} ]; then
        echo "[$LINENO][NOTE ][${g_network_cfg_path}]lost, default"
        return
    fi
    # lanNetwork=***********/24
    local arg_result=""
    arg_result=$(grep "lanNetwork=" ${g_network_cfg_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][WARN ][${g_network_cfg_path}]ip lost, default"
        return
    fi
    local arg_right=""
    local arg_ip_prefix=""
    arg_right=${arg_result#*=} # skip lanNetwork=
    arg_ip_prefix=${arg_right%.*}
    if [ "${arg_ip_prefix}" != "${g_ip_prefix}" ]; then
        g_ip_prefix="${arg_ip_prefix}"
    fi
}

g_pack_info_path=${g_root_of_script}/pack_${g_pack_time}.info
function func_check_account_info()
{
    echo "[$LINENO][NOTE ]----- check account info"
    func_check_network_ini
    g_board_ip="${g_ip_prefix}.${g_target_ip}"
    if [ "${g_target_no}" == "1.0" -o "${g_target_no}" == "1.1" ]; then
        g_sync_from_ip="${g_ip_prefix}.${g_sync_from_ip_no}"
    fi
    echo "[$LINENO][NOTE ]ip:   ${g_board_ip}"
    echo "[$LINENO][NOTE ]r ip: ${g_sync_from_ip}"
    if [ "${g_input_user}" != "" -a "${g_input_user}" != " " ]; then
        echo "[$LINENO][NOTE ]update usr to input[${g_input_user}]"
        g_user_name="${g_input_user}"
        if [ "${g_input_pwd}" == "" -o "${g_input_pwd}" == " " ]; then
            echo "[$LINENO][ERROR][${g_target_board}]input pwd empty"
            g_install_status="func_check_account_info"
            return
        fi
        echo "[$LINENO][NOTE ]update pwd to input[${g_input_pwd}]"
        g_user_pwd="${g_input_pwd}"
        g_user_ip="${g_board_ip}"
        func_check_pwd
        return
    fi
    echo "[$LINENO][NOTE ]update account to default[${g_target_user}]"
    g_user_name="${g_target_user}"
    g_user_pwd="${g_target_pwd}"
    g_user_ip="${g_board_ip}"
    func_check_pwd
    if [ "${g_install_status}" == "success" ]; then
        return
    fi
    echo "[$LINENO][NOTE ]check pack info: ${g_pack_info_path}"
    if [ ! -f ${g_pack_info_path} ]; then
        echo "[$LINENO][ERROR][${g_pack_info_path}]lost"
        g_install_status="func_check_account_info"
        return
    fi
    local arg_result=""
    local arg_right=""
    local arg_user=""
    local arg_pwd=""
    local arg_ip_no=""
    local arg_ip=""
    arg_result=$(grep "PACK:${g_target_board}:" ${g_pack_info_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][ERROR][${arg_package}]version lost"
        g_install_status="func_check_account_info"
        return
    fi
    arg_result=${arg_result// /}
    arg_right=${arg_result//'\n'/}
    arg_right=${arg_right#*:} # skip PACK
    arg_right=${arg_right#*:} # skip board
    arg_user=${arg_right%%:*}
    arg_right=${arg_right#*:} # skip user
    arg_pwd=${arg_right%%:*}
    arg_right=${arg_right#*:} # skip pwd
    arg_ip_no=${arg_right%%:*}
    arg_ip="${g_ip_prefix}.${arg_ip_no}"
    g_user_name="${arg_user}"
    g_user_pwd="${arg_pwd}"
    g_user_ip="${arg_ip}"
    echo "[$LINENO][NOTE ]user: ${g_user_name}"
    echo "[$LINENO][NOTE ]pwd:  ${g_user_pwd}"
    echo "[$LINENO][NOTE ]ip:   ${g_user_ip}"
    func_check_pwd
}

function func_check_is_board_match()
{
    echo "[$LINENO][NOTE ]----- check board is match"
    if [ "${g_input_user}" != "" -a "${g_input_user}" != " " ]; then
        echo "[$LINENO][NOTE ]input account, ignore"
        return
    fi
    local arg_ip_list=$(ip addr | grep -w 'inet' | awk '{print $2}')
    for arg_temp_ip in ${arg_ip_list}
    do
        echo "[$LINENO][NOTE ][${arg_temp_ip}]try"
        if [[ ${arg_temp_ip} =~ ${g_user_ip} ]]; then
            echo "[$LINENO][NOTE ][${arg_temp_ip}]match"
            return
        fi
    done
    echo "[$LINENO][ERROR][${g_user_ip}]not find"
    g_install_status="func_check_is_board_match"
}

function func_prepare_cfg_path()
{
    echo "[$LINENO][NOTE ]----- prepare cfg path"
    if [ ! -d ${g_dev_cfg_root} ]; then
        echo "[$LINENO][NOTE ][${g_dev_cfg_root}]create"
        func_run_sudo_command "mkdir -p ${g_dev_cfg_root}"
    fi
    # 获取文件夹权限
    local permissions=$(ls -ld "$g_dev_cfg_root" | awk '{print $1}')
    # 判断权限是否为777
    if [ "$permissions" == "drwxrwxrwx" ]; then
        echo "[$LINENO][NOTE] Folder permissions are 777"
    else
        echo "[$LINENO][NOTE] Folder permissions are not 777, current permissions: $permissions"
        func_run_sudo_command "chmod 0777 ${g_dev_cfg_root}"
    fi
}

g_cur_user_path=""
g_log_path=""
function func_prepare_default_path()
{
    echo "[$LINENO][NOTE ]----- prepare default path"
    if [ "${g_user_name}" == "root" ]; then
        g_cur_user_path=/root
    else
        g_cur_user_path=/home/<USER>
    fi
    g_log_path=${g_cur_user_path}/log
    echo "[$LINENO][NOTE ]log: ${g_log_path}"
    if [ ! -d ${g_cur_user_path} ]; then
        echo "[$LINENO][ERROR][${g_cur_user_path}]lost"
        g_install_status="func_prepare_default_path"
        return
    fi
    if [ ! -d ${g_log_path} ]; then
        echo "[$LINENO][NOTE][${g_log_path}]lost"
        mkdir -p ${g_log_path}
    fi
}

function func_check_cur_install_path()
{
    echo "[$LINENO][NOTE ]----- check cur install path"
    if [ -L "${g_cmcc_sl_install}" ]; then
        g_install_dst_path=$(realpath "${g_cmcc_sl_install}")
        if [ "${g_install_dst_path}" == "" -o "${g_install_dst_path}" == " " ]; then
            echo "[$LINENO][WARN ]cur install path invalid"
            g_install_dst_path=${g_cur_user_path}/cmcc_robot
            return
        fi
        echo "[$LINENO][NOTE ]cur install path[${g_install_dst_path}]"
    else
        g_install_dst_path=${g_cur_user_path}/cmcc_robot
        echo "[$LINENO][WARN ][${g_install_dst_path}]default"
    fi
}

g_cur_link_flag="is_cur_link"
function func_check_switch_to_backup()
{
    echo "[$LINENO][NOTE ]----- switch to backup"
    if [ ! -d ${g_install_dst_path} ]; then
        echo "[$LINENO][WARN ][${g_install_dst_path}]lost, first install, ignore"
        return
    fi
    local arg_path=""
    local arg_dir=""
    local arg_bak="_bak"
    local arg_to_dir=""
    local arg_cur_link_flag_path=""
    arg_cur_link_flag_path=${g_install_dst_path}/${g_cur_link_flag}
    echo "[$LINENO][NOTE ]update link flag to[${arg_cur_link_flag_path}]"
    echo "BEGIN:${g_install_dst_path}:END" > ${arg_cur_link_flag_path}
    arg_path=${g_install_dst_path%/*}
    arg_dir=${g_install_dst_path##*/}
    if [[ ${arg_dir} =~ ${arg_bak} ]]; then
        echo "[$LINENO][NOTE ]with bak"
        arg_to_dir=${arg_dir%_bak*}
    else
        echo "[$LINENO][NOTE ]without bak"
        arg_to_dir="${arg_dir}_bak"
    fi
    g_install_dst_path=${arg_path}/${arg_to_dir}
    echo "[$LINENO][NOTE ]switch to[${g_install_dst_path}]"
}

function func_install_prepare()
{
    echo "[$LINENO][NOTE ]----- prepare install"
    func_prepare_cfg_path
    func_prepare_default_path
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    if [ ! -d ${g_cmcc_sl_root} ]; then
        echo "[$LINENO][WARN ][${g_cmcc_sl_root}]lost, create"
        func_run_sudo_command "mkdir -p ${g_cmcc_sl_root}"
    fi
    local arg_install_path_first_c=""
    local arg_install_path_last_c=""
    if [ "${g_install_path}" == "" -o "${g_install_path}" == " " ]; then
        func_check_cur_install_path
        if [ "${g_install_status}" != "success" ]; then
            g_install_status="func_install_prepare"
            return
        fi
        if [ "${g_is_backup}" == "y" ]; then
            func_check_switch_to_backup
        fi
    else
        arg_install_path_first_c=${g_install_path:0:1}
        if [ "${arg_install_path_first_c}" != "/" ]; then
            echo "[$LINENO][ERROR]not absolute path"
            g_install_status="func_install_prepare"
            return
        fi
        arg_install_path_last_c=${g_install_path: -1}
        if [ "${arg_install_path_last_c}" == "/" ]; then
            echo "[$LINENO][NOTE ]del last /"
            g_install_path=${g_install_path%/*}
        fi
        g_install_dst_path=${g_install_path}
    fi
    echo "[$LINENO][NOTE ]dst: ${g_install_dst_path}"
    if [ ! -d ${g_install_dst_path} ]; then
        echo "[$LINENO][WARN ][${g_install_dst_path}]lost, create"
        mkdir -p ${g_install_dst_path}
    fi
    if [ "${g_is_backup}" == "y" ]; then
        if [ -f ${g_install_dst_path}/${g_cur_link_flag} ]; then
            echo "[$LINENO][NOTE ][${g_install_dst_path}/${g_cur_link_flag}]clear it"
            func_del_dir_or_file_safe "${g_install_dst_path}/${g_cur_link_flag}" "n" "y"
        fi
    fi
    g_install_dst_env_path=${g_install_dst_path}/env
    g_install_dst_srv_path=${g_install_dst_env_path}/service
    g_install_list_path=${g_install_dst_path}/install.list
}

g_cmcc_sl_env_path=${g_cmcc_sl_install}/env
g_cmcc_sl_srv_path=${g_cmcc_sl_env_path}/service
g_cmcc_sl_handle="new"
function func_cmcc_symbolic_link_check()
{
    echo "[$LINENO][NOTE ]----- check symbolic link"
    local arg_cur_sl_to=""
    if [ -L "${g_cmcc_sl_install}" ]; then
        arg_cur_sl_to=$(realpath "${g_cmcc_sl_install}")
        echo "[$LINENO][NOTE ]cur link to [${arg_cur_sl_to}]"
        if [ "${arg_cur_sl_to}" != "${g_install_dst_path}" ]; then
            echo "[$LINENO][WARN ]changed, will link to [${g_install_dst_path}]"
            g_cmcc_sl_handle="update"
        else
            g_cmcc_sl_handle="skip"
        fi
    else
        echo "[$LINENO][NOTE ][${g_cmcc_sl_install}]invalid, new"
        g_cmcc_sl_handle="new"
    fi
}

function func_cmcc_symbolic_link_handle()
{
    echo "[$LINENO][NOTE ]----- handle symbolic link"
    if [ "${g_cmcc_sl_handle}" == "new" ]; then
        echo "[$LINENO][NOTE ]new link"
        func_run_sudo_command "ln -s ${g_install_dst_path} ${g_cmcc_sl_install}"
    elif [ "${g_cmcc_sl_handle}" == "update" ]; then
        echo "[$LINENO][NOTE ]update link"
        func_run_sudo_command "ln -snf ${g_install_dst_path} ${g_cmcc_sl_install}"
    fi
}

function func_check_ping()
{
    local arg_ip="$1"
    if ping -c 2 "${arg_ip}" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

g_dev_cfg_path=${g_dev_cfg_root}/cmcc_dev.ini
g_dev_cfg_bak_path=${g_dev_cfg_root}/cmcc_dev_bak.ini
g_dev_tmp_cfg_path=/tmp/cmcc_dev.ini
function func_sync_ini()
{
    echo "[$LINENO][NOTE ]----- sync ini"
    local arg_is_get="n"
    local arg_timeout=1
    while true
    do
        echo "[$LINENO][NOTE ]try, ${arg_timeout}"
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR]timeout, ${arg_timeout}"
            break
        fi
        sshpass -p "${g_sync_from_pwd}" rsync -av "${g_sync_from_user}"@"${g_sync_from_ip}":${g_dev_cfg_path} /tmp
        if [ -f ${g_dev_tmp_cfg_path} ]; then
            echo "[$LINENO][NOTE ]get ini ok"
            arg_is_get="y"
            break
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 3
    done
    if [ "${arg_is_get}" != "y" ]; then
        g_install_status="func_sync_ini"
        return
    fi
    if [ ! -f ${g_dev_cfg_path} ]; then
        echo "[$LINENO][WARN ][${g_dev_cfg_path}]lost"
        func_run_sudo_command "cp -rf ${g_dev_tmp_cfg_path} ${g_dev_cfg_root}"
        return
    fi
    local arg_result1=""
    local arg_result2=""
    arg_result1=$(grep "devSn=" ${g_dev_cfg_path})
    arg_result2=$(grep "devSn=" ${g_dev_tmp_cfg_path})
    if [ "${arg_result1}" != "${arg_result2}" ]; then
        echo "[$LINENO][ERROR][${arg_result1}] != [${arg_result2}]"
        func_run_sudo_command "cp -rf ${g_dev_tmp_cfg_path} ${g_dev_cfg_root}"
    else
        echo "[$LINENO][NOTE ]not need update"
    fi
}

function func_sync_date()
{
    echo "[$LINENO][NOTE ]----- sync date"
    local arg_date=""
    local arg_date_sub=""
    local arg_is_get="n"
    local arg_timeout=1
    while true
    do
        echo "[$LINENO][NOTE ]try, ${arg_timeout}"
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR]timeout, ${arg_timeout}"
            break
        fi
        arg_date=$(sshpass -p "${g_sync_from_pwd}" ssh -tt "${g_sync_from_user}"@"${g_sync_from_ip}" "date '+%Y-%m-%d %H:%M:%S'")
        arg_date_sub=${arg_date:0:2}
        if [ "${arg_date_sub}" == "20" ]; then
            echo "[$LINENO][NOTE ]date: ${arg_date}"
            arg_is_get="y"
            break
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 3
    done
    if [ "${arg_is_get}" != "y" ]; then
        g_install_status="func_sync_date"
        return
    fi
    func_run_sudo_command "date -s \"${arg_date}\""
}

function func_check_ssh()
{
    echo "[$LINENO][NOTE ]----- check ssh"
    local arg_user="$1"
    local arg_pwd="$2"
    local arg_ip="$3"
    local arg_result=""
    local arg_timeout=1
    while true
    do
        echo "[$LINENO][NOTE ]try, ${arg_timeout}"
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR]timeout, ${arg_timeout}"
            g_install_status="func_check_ssh"
            break
        fi
        arg_result=$(sshpass -p "${arg_pwd}" ssh -tt "${arg_user}"@"${arg_ip}" "echo connect_success")
        if echo "${arg_result}" | grep -q "connect_success"; then
            echo "[$LINENO][NOTE ][${arg_ip}]ssh success"
            break
        fi
        echo "[$LINENO][ERROR][${arg_ip}]ssh failed"
        ssh-keygen -R "${arg_ip}"
        arg_result=$(sshpass -p "${arg_pwd}" ssh -tt -o StrictHostKeyChecking=no "${arg_user}"@"${arg_ip}" "echo connect_success")
        if echo "${arg_result}" | grep -q "connect_success"; then
            echo "[$LINENO][NOTE ][${arg_ip}]ssh success"
            break
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 3
    done
}

function func_sync_data()
{
    echo "[$LINENO][NOTE ]----- sync data"
    if [ "${g_is_ota}" == "y" ]; then
        echo "[$LINENO][NOTE ]ota, not sync"
        return
    fi
    if [ "${g_sync_from_ip}" == "" -o "${g_sync_from_ip}" == " " ]; then
        echo "[$LINENO][NOTE ]ignore"
        return
    fi
    if [ "${g_target_board}" == "motion" ]; then
        echo "[$LINENO][NOTE ][${g_target_board}]skip self"
        return
    fi
    local arg_is_reachable="n"
    local arg_timeout=1
    while true
    do
        echo "[$LINENO][NOTE ]try, ${arg_timeout}"
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR]timeout, ${arg_timeout}"
            break
        fi
        if func_check_ping "${g_sync_from_ip}"; then
            echo "[$LINENO][NOTE ][${g_sync_from_ip}]reachable"
            arg_is_reachable="y"
            break
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 4
    done
    if [ "${arg_is_reachable}" != "y" ]; then
        echo "[$LINENO][WARN ][${g_sync_from_ip}]unreachable"
        return
    fi
    func_check_ssh "${g_sync_from_user}" "${g_sync_from_pwd}" "${g_sync_from_ip}"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    # func_sync_date
    func_sync_ini
    g_install_status="success"
}

g_robot_no=""
g_robot_type=""
g_robot_info_sn=""
g_robot_info_cmei=""
g_robot_info_mac=""
g_robot_ovd_pwd=""
g_robot_ovd_enc_pwd=""
g_robot_mmvee_sn=""
g_robot_app_key=""
g_robot_app_secret=""
function func_get_sub_robot_info()
{
    local arg_item=$1
    local arg_result=""
    arg_result=$(grep "${arg_item}=" ${g_dev_cfg_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][ERROR][${arg_item}]lost"
        g_install_status="func_get_sub_robot_info"
        return
    fi
    arg_result=${arg_result#*=}
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][ERROR][${arg_item}]invalid"
        g_install_status="func_get_sub_robot_info"
        return
    fi
    if [ "${arg_item}" == "devNO" ]; then
        g_robot_no=${arg_result}
        return
    fi
    if [ "${arg_item}" == "devType" ]; then
        g_robot_type=${arg_result}
        return
    fi
    if [ "${arg_item}" == "devSn" ]; then
        g_robot_info_sn=${arg_result}
        return
    fi
    if [ "${arg_item}" == "devCmei" ]; then
        g_robot_info_cmei=${arg_result}
        return
    fi
    if [ "${arg_item}" == "macAddr" ]; then
        g_robot_info_mac=${arg_result}
        return
    fi
    if [ "${arg_item}" == "OVDLoginPassword" ]; then
        g_robot_ovd_pwd=${arg_result}
        return
    fi
    if [ "${arg_item}" == "OVDMediaEncPassword" ]; then
        g_robot_ovd_enc_pwd=${arg_result}
        return
    fi
    if [ "${arg_item}" == "mmveeSn" ]; then
        g_robot_mmvee_sn=${arg_result}
        return
    fi
    if [ "${arg_item}" == "APPKey" ]; then
        g_robot_app_key=${arg_result}
        return
    fi
    if [ "${arg_item}" == "APPSecret" ]; then
        g_robot_app_secret=${arg_result}
        return
    fi
}

function func_get_robot_info()
{
    echo "[$LINENO][NOTE ]----- get robot info"
    if [ "${g_target_board}" == "cognition" ]; then
        echo "[$LINENO][NOTE ][${g_target_board}]not need factory info"
        return
    fi
    if [ ! -f ${g_dev_cfg_path} ]; then
        echo "[$LINENO][ERROR][${g_dev_cfg_path}]lost"
        if [ ! -f ${g_dev_cfg_bak_path} ]; then
            echo "[$LINENO][ERROR][${g_dev_cfg_bak_path}]lost too"
            g_install_status="func_get_robot_info"
            return
        fi
        echo "[$LINENO][WARN ]switch to[${g_dev_cfg_bak_path}]"
        g_dev_cfg_path=${g_dev_cfg_bak_path}
    fi
    while true
    do
        g_install_status="success"
        func_get_sub_robot_info "devNO"

        g_install_status="success"
        func_get_sub_robot_info "devType"
        if [ "${g_install_status}" != "success" ]; then
            if [ "${g_target_no}" == "2.0" ]; then
                g_robot_type="591884"
            else
                g_robot_type="2320647"
            fi
        fi
        g_install_status="success"
        func_get_sub_robot_info "devSn"
        if [ "${g_install_status}" != "success" ]; then
            break
        fi
        g_install_status="success"
        func_get_sub_robot_info "devCmei"
        if [ "${g_install_status}" != "success" ]; then
            break
        fi
        g_install_status="success"
        func_get_sub_robot_info "macAddr"
        if [ "${g_install_status}" != "success" ]; then
            break
        fi
        g_install_status="success"
        func_get_sub_robot_info "OVDLoginPassword"
        if [ "${g_install_status}" != "success" ]; then
            break
        fi
        g_install_status="success"
        func_get_sub_robot_info "OVDMediaEncPassword"
        if [ "${g_install_status}" != "success" ]; then
            g_robot_ovd_enc_pwd=${g_robot_ovd_pwd}
        fi

        g_install_status="success"
        func_get_sub_robot_info "mmveeSn"

        g_install_status="success"
        func_get_sub_robot_info "APPKey"

        g_install_status="success"
        func_get_sub_robot_info "APPSecret"

        g_install_status="success"
        break
    done

    echo "[$LINENO][NOTE ]no:         ${g_robot_no}"
    echo "[$LINENO][NOTE ]type:       ${g_robot_type}"
    echo "[$LINENO][NOTE ]sn:         ${g_robot_info_sn}"
    echo "[$LINENO][NOTE ]cemi:       ${g_robot_info_cmei}"
    echo "[$LINENO][NOTE ]mac:        ${g_robot_info_mac}"
    echo "[$LINENO][NOTE ]ovd_pwd:    ${g_robot_ovd_pwd}"
    echo "[$LINENO][NOTE ]enc_pwd:    ${g_robot_ovd_enc_pwd}"
    echo "[$LINENO][NOTE ]mmvee_sn:   ${g_robot_mmvee_sn}"
    echo "[$LINENO][NOTE ]app_key:    ${g_robot_app_key}"
    echo "[$LINENO][NOTE ]app_secret: ${g_robot_app_secret}"
}

function func_kill_process()
{
    local arg_process=$1
    local arg_pid=""
    arg_pid=`ps -ef|grep "${arg_process}" |grep -v "grep"|grep -v " Z " | wc -l`
    if [ "${arg_pid}" == "0" ]; then
        echo "[$LINENO][NOTE ][${arg_process}]inactive"
    else
        arg_pid=`ps -ef | grep "${arg_process}" |grep -v "grep" | awk '{print $2}'`
        func_run_sudo_command "kill -9 ${arg_pid}"
        echo "[$LINENO][NOTE ][${arg_pid}:${arg_process}]stop"
    fi
    sleep 1
}

function func_ObuVoice_stop()
{
    echo "[$LINENO][NOTE ]----- stop ObuVoice"
    func_kill_process "ifly-check.sh"
    func_kill_process "message_obu_robot"
    func_kill_process "vtn_obu_node"
}

function func_stop_service()
{
    local arg_service_name="$1"
    local arg_status=""
    local arg_timeout=1
    while true
    do
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR][${arg_service_name}]stop timeout, ${arg_timeout}"
            g_install_status="func_stop_service"
            break
        fi
        arg_status=$(systemctl is-active "${arg_service_name}")
        if [ "${arg_status}" == "active" ]; then
            echo "[$LINENO][NOTE ][${arg_service_name}]stop ..."
            func_run_sudo_command "systemctl stop "${arg_service_name}""
            # func_run_sudo_command "systemctl disable "${arg_service_name}""
            echo "[$LINENO][NOTE ][${arg_service_name}]stop end"
        else
            echo "[$LINENO][NOTE ][${arg_service_name}]off"
            break
        fi
        arg_status=$(systemctl is-active "${arg_service_name}")
        if [ "${arg_status}" == "active" ]; then
            echo "[$LINENO][NOTE ][${arg_service_name}]stop failed"
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 5
    done
}

function func_app_service_stop()
{
    echo "[$LINENO][NOTE ]----- stop app service"
    func_stop_service "run_xiaoli_server.service"
}

function func_motion_service_stop()
{
    echo "[$LINENO][NOTE ]----- stop motion service"
    if [ "${g_target_no}" == "1.0" ]; then
        func_stop_service "dr_wifi.service"
        if [ "${g_install_status}" != "success" ]; then
            return
        fi
    fi
    func_stop_service "dr_5g.service"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    # func_stop_service "dr_ap.service"
    # if [ "${g_install_status}" != "success" ]; then
    #     return
    # fi
    func_stop_service "run_andlink_ros2.service"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    func_stop_service "run_network_ros2.service"
}

function func_cognition_service_stop()
{
    echo "[$LINENO][NOTE ]----- stop cognition service"
    func_stop_service "run_nvidia_rob_ros2.service"
}

function func_all_services_stop()
{
    echo "[$LINENO][NOTE ]----- stop all services"
    if [ "${g_target_board}" == "app" ]; then
        func_app_service_stop
        return
    fi
    if [ "${g_target_board}" == "motion" ]; then
        func_motion_service_stop
        return
    fi
    if [ "${g_target_board}" == "cognition" ]; then
        func_cognition_service_stop
        return
    fi
}

function func_start_service()
{
    local arg_service_name="$1"
    local arg_status=""
    local arg_timeout=1
    while true
    do
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR][${arg_service_name}]start timeout, ${arg_timeout}"
            g_install_status="func_start_service"
            break
        fi
        arg_status=$(systemctl is-active "${arg_service_name}")
        if [ "${arg_status}" == "active" ]; then
            echo "${arg_service_name} on"
            break
        fi
        echo "[$LINENO][NOTE ][${arg_service_name}]start ..."
        func_run_sudo_command "systemctl enable "${arg_service_name}""
        func_run_sudo_command "systemctl start "${arg_service_name}""
        echo "[$LINENO][NOTE ][${arg_service_name}]start end"
        arg_status=$(systemctl is-active "${arg_service_name}")
        if [ "${arg_status}" == "active" ]; then
            echo "[$LINENO][NOTE ][${arg_service_name}]start success"
            break
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 5
    done
}

function func_app_service_start()
{
    echo "[$LINENO][NOTE ]----- start app service"
    func_run_sudo_command "systemctl daemon-reload"
    func_start_service "run_xiaoli_server.service"
}

function func_motion_service_start()
{
    echo "[$LINENO][NOTE ]----- start motion service"
    func_run_sudo_command "systemctl daemon-reload"
    if [ "${g_target_no}" == "1.0" ]; then
        func_start_service "dr_wifi.service"
    fi
    func_start_service "dr_5g.service"
    # func_start_service "dr_ap.service"
    func_start_service "run_andlink_ros2.service"
    func_start_service "run_network_ros2.service"
}

function func_cognition_service_start()
{
    echo "[$LINENO][NOTE ]----- start cognition service"
    func_run_sudo_command "systemctl daemon-reload"
    func_start_service "run_nvidia_rob_ros2.service"
}

function func_all_services_start()
{
    echo "[$LINENO][NOTE ]----- start all services"
    if [ "${g_target_board}" == "app" ]; then
        func_app_service_start
        return
    fi
    if [ "${g_target_board}" == "motion" ]; then
        func_motion_service_start
        return
    fi
    if [ "${g_target_board}" == "cognition" ]; then
        func_cognition_service_start
        return
    fi
}

function func_extract_zip()
{
    local arg_is_handle="$1"
    echo "[$LINENO][NOTE ]----- extract zip ${arg_is_handle}"
    func_del_dir_or_file_safe "${g_install_quick_path}" "y" "y"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    mkdir -p ${g_install_quick_path}
    if [ ! -d ${g_install_dst_path} ]; then
        mkdir -p ${g_install_dst_path}
    fi
    sed -n -e '1,/^exit 0$/!p' $0 > "${g_install_zip_path}" 2>/dev/null
    if [ "${arg_is_handle}" == "y" ]; then
        sed '/^exit 0$/q' $0 > "${g_install_sh_path}" 2>/dev/null
    fi
    # just for test install.sh
    # cp -rf ./install.tar.gz ${g_install_quick_path}
    if [ -f ${g_install_zip_path} ]; then
        echo "[$LINENO][NOTE ]extract [${g_install_zip_path}] success"
    else
        echo "[$LINENO][ERROR]extract [${g_install_zip_path}] failed"
        g_install_status="func_extract_zip"
        return
    fi
    local arg_md5_str=""
    cd ${g_install_quick_path}
    arg_md5_str=($(md5sum ${g_install_zip_name}))
    if [ "${arg_md5_str}" != "${g_target_md5}" ]; then
        echo "[$LINENO][ERROR]extract [${arg_md5_str}]mismatch"
        g_install_status="func_extract_zip"
        return
    fi
    tar -xf ${g_install_zip_name}
    cd ${g_root_of_script}
}

function func_update_factory()
{
    echo "[$LINENO][NOTE ]----- update factory"
    if [ -f ${g_install_src_res_path}/factory/factory_update ]; then
        echo "[$LINENO][NOTE ]start update ..."
        func_run_sudo_command "${g_install_src_res_path}/factory/factory_update"
    fi
}

function func_install_cmcc_rtc()
{
    local arg_package=$1
    sed -i "s|^  log_path:.*|  log_path: \"${g_log_path}/call.txt\"|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/config/param.yaml
    if [ "${g_is_test_dev}" != "y" ]; then
        return
    fi
    # dev: false
    sed -i "s|^dev: false.*|dev: true|"              ${g_install_dst_path}/${arg_package}/share/${arg_package}/config/param.yaml
}

function func_install_homi_speech()
{
    local arg_package=$1
    if [ "${g_is_test_dev}" != "y" ]; then
        return
    fi
    local arg_robot_new_url="http://************:10000/robot/business/api/device/client/connect/url"
    sed -i "s|^                    \"url\":.*|                    \"url\":\"${arg_robot_new_url}\",|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/launch/config/param.yaml
}

function func_install_live_stream()
{
    if [ "${g_is_test_dev}" != "y" ]; then
        return
    fi
    local arg_robot_new_url="http://36.138.107.136:10000/ovp/getServerAddr"
    sed -i "s|^servicescheduleurl:.*|servicescheduleurl:${arg_robot_new_url}|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/launch/DeviceConf.ini
}

function func_install_video_gst()
{
    local arg_package=$1
    if [ "${g_target_no}" == "1.1" ]; then
        sed -i "s|^      camera_name:.*|      camera_name: \"1080P USB Camera-A: 1080P USB C\"|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/config/param.yaml
    elif [ "${g_target_no}" == "2.0" ]; then
        sed -i "s|^      camera_name:.*|      camera_name: \"1080P USB Camera-A: 1080P USB C\"|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/config/param.yaml
    fi
    local arg_file_path=/lib/udev/uvcdynctrl
    if [ ! -f ${arg_file_path} ]; then
        echo "[$LINENO][WARN ][${arg_file_path}]lost"
        return
    fi
    local arg_result=""
    arg_result=$(grep "debug=" ${arg_file_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        return
    fi
    arg_result=${arg_result#*=}
    if [ "${arg_result}" != "0" ]; then
        sed -i "/debug=/cdebug=0" ${arg_file_path}
    fi
}

function func_install_video_gst_nv()
{
    local arg_package=$1
    if [ "${g_target_no}" == "1.1" ]; then
        sed -i "s|^      camera_name:.*|      camera_name: \"1080P USB Camera-A: 1080P USB C\"|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/config/param.yaml
    fi
    local arg_file_path=/lib/udev/uvcdynctrl
    if [ ! -f ${arg_file_path} ]; then
        echo "[$LINENO][WARN ][${arg_file_path}]lost"
        return
    fi
    local arg_result=""
    arg_result=$(grep "debug=" ${arg_file_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        return
    fi
    arg_result=${arg_result#*=}
    if [ "${arg_result}" != "0" ]; then
        sed -i "/debug=/cdebug=0" ${arg_file_path}
    fi
}

function func_install_video_gst_neck()
{
    local arg_package=$1
    if [ "${g_target_no}" == "1.1" ]; then
        sed -i "s|^      camera_name:.*|      camera_name: \"1080P USB Camera-B: 1080P USB C\"|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/config/param.yaml
    elif [ "${g_target_no}" == "2.0" ]; then
        sed -i "s|^      camera_name:.*|      camera_name: \"1080P USB Camera-B: 1080P USB C\"|" ${g_install_dst_path}/${arg_package}/share/${arg_package}/config/param.yaml
    fi
    local arg_file_path=/lib/udev/uvcdynctrl
    if [ ! -f ${arg_file_path} ]; then
        echo "[$LINENO][WARN ][${arg_file_path}]lost"
        return
    fi
    local arg_result=""
    arg_result=$(grep "debug=" ${arg_file_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        return
    fi
    arg_result=${arg_result#*=}
    if [ "${arg_result}" != "0" ]; then
        sed -i "/debug=/cdebug=0" ${arg_file_path}
    fi
}

function func_install_per_package()
{
    local arg_package=$1
    # cmcc_rtc
    if [ "${arg_package}" == "cmcc_rtc" ]; then
        echo "[$LINENO][NOTE ][cmcc_rtc]handle"
        func_install_cmcc_rtc "${arg_package}"
    fi
    # homi_speech
    if [ "${arg_package}" == "homi_speech" ]; then
        echo "[$LINENO][NOTE ][homi_speech]handle"
        func_install_homi_speech "${arg_package}"
    fi
    # live_stream
    if [ "${arg_package}" == "live_stream" ]; then
        echo "[$LINENO][NOTE ][live_stream]handle"
        func_install_live_stream "${arg_package}"
    fi
    # video_gst
    if [ "${arg_package}" == "video_gst" ]; then
        echo "[$LINENO][NOTE ][video_gst]handle"
        func_install_video_gst "${arg_package}"
    fi
    # video_gst_nv
    if [ "${arg_package}" == "video_gst_nv" ]; then
        echo "[$LINENO][NOTE ][video_gst_nv]handle"
        func_install_video_gst_nv "${arg_package}"
    fi
    # video_gst_neck
    if [ "${arg_package}" == "video_gst_neck" ]; then
        echo "[$LINENO][NOTE ][video_gst_neck]handle"
        func_install_video_gst_neck "${arg_package}"
    fi
}

function func_install_list_update()
{
    local arg_package=${1}
    local arg_install_info=${2}
    local arg_result=""
    if [ ! -f ${g_install_list_path} ]; then
        # new
        echo "${arg_install_info}" >> ${g_install_list_path}
    else
        arg_result=$(grep "PKG:${arg_package}:" ${g_install_list_path})
        if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
            # new
            echo "${arg_install_info}" >> ${g_install_list_path}
        else
            # update
            sed -i "/PKG:${arg_package}:/c${arg_install_info}" ${g_install_list_path}
        fi
    fi
}

g_install_packages_list=""
function func_install_all_check()
{
    echo "[$LINENO][NOTE ]----- check all packages"
    local arg_result=""
    local arg_package=""
    local arg_version=""
    local arg_right=""
    while read arg_line
    do
        # echo "arg_line: ${arg_line}"
        arg_result=${arg_line// /}
        arg_right=${arg_result//'\n'/}
        # echo "arg_result: ${arg_result}"
        arg_right=${arg_right#*:}
        arg_package=${arg_right%%:*}
        if [ "${arg_package}" == "" -o "${arg_package}" == " " ]; then
            echo "[$LINENO][ERROR]pkg invalid"
            g_install_status="func_install_all_check"
            return
        fi
        if [ "${arg_package}" == "robot_body" ]; then
            echo "[$LINENO][WARN ][${arg_package}]skip"
            continue
        fi
        if [ ! -d ${g_install_src_path}/${arg_package} ]; then
            echo "[$LINENO][WARN ][${arg_package}]lost"
            g_install_status="func_install_all_check"
            return
        fi
        arg_right=${arg_right#*:}
        arg_version=${arg_right%%:*}
        if [ "${arg_version}" == "" -o "${arg_version}" == " " ]; then
            echo "[$LINENO][ERROR][${arg_package}]version empty"
            g_install_status="func_install_all_check"
            return
        fi
        func_del_dir_or_file_safe "${g_install_dst_path}/${arg_package}" "y" "y"
        if [ "${g_install_status}" != "success" ]; then
            return
        fi
        if [ "${g_install_packages_list}" == "" -o "${g_install_packages_list}" == " " ]; then
            g_install_packages_list="${arg_package}"
        else
            g_install_packages_list="${g_install_packages_list},${arg_package}"
        fi
    done < ${g_version_list_path}
}

function func_robot_deploy()
{
    echo "[$LINENO][NOTE ]----- deploy"
    local arg_result=""
    local arg_package=""
    local arg_package_array=""
    arg_package_array=(${g_install_packages_list//,/ })
    for arg_package in ${arg_package_array[@]}
    do
        arg_result=$(grep "PKG:${arg_package}:" ${g_version_list_path})
        if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
            echo "[$LINENO][ERROR][${arg_package}]version lost"
            g_install_status="func_robot_deploy"
            return
        fi
        echo "[$LINENO][NOTE ][${arg_package}]deploy"
        # custom install, config/.wav/...
        func_install_per_package "${arg_package}"
        # record install.list
        func_install_list_update "${arg_package}" "${arg_result}:${g_install_time}"
    done
}

function func_install_package()
{
    echo "[$LINENO][NOTE ]----- install package"
    local arg_version=""
    local arg_right=""
    local arg_package_array=""
    local arg_package=""
    local arg_result=""
    # version.list
    if [ ! -f ${g_version_list_path} ]; then
        echo "[$LINENO][ERROR][${g_version_list_path}]lost"
        g_install_status="func_install_package"
        return
    fi
    if [ "${g_select_packages}" != "" ]; then
        g_install_packages_list=${g_select_packages}
        arg_package_array=(${g_install_packages_list//,/ })
        for arg_package in ${arg_package_array[@]}
        do
            if [ ! -d "${g_install_src_path}/${arg_package}" ]; then
                echo "[$LINENO][ERROR][${g_install_src_path}/${arg_package}]lost"
                g_install_status="func_install_package"
                return
            fi
            arg_result=$(grep "PKG:${arg_package}:" ${g_version_list_path})
            if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
                echo "[$LINENO][ERROR][${arg_package}]version lost"
                g_install_status="func_install_package"
                return
            fi
            arg_result=${arg_result// /}
            arg_right=${arg_result//'\n'/}
            arg_right=${arg_right#*:}
            arg_right=${arg_right#*:}
            arg_version=${arg_right%%:*}
            if [ "${arg_version}" == "" -o "${arg_version}" == " " ]; then
                echo "[$LINENO][ERROR][${arg_package}]version empty"
                g_install_status="func_install_package"
                return
            fi
            if [ -d ${g_install_dst_path}/${arg_package} ]; then
                func_del_dir_or_file_safe "${g_install_dst_path}/${arg_package}" "y" "y"
                if [ "${g_install_status}" != "success" ]; then
                    return
                fi
            fi
            echo "[$LINENO][NOTE ][${arg_package}]copy"
            # copy [a2dp/andlink/...] directory
            cp -rf ${g_install_src_path}/${arg_package} ${g_install_dst_path}
        done
    else
        func_install_all_check
        cp -rf ${g_install_src_path}/* ${g_install_dst_path}
    fi
}

function func_install_env()
{
    echo "[$LINENO][NOTE ]----- install env"
    if [ ! -d ${g_install_dst_env_path} ]; then
        mkdir -p ${g_install_dst_env_path}
    fi
    local arg_package_array=""
    local arg_package=""
    local arg_res_list="ObuVoice,service,lib,uwb,SensorHub,factory"
    arg_package_array=(${arg_res_list//,/ })
    for arg_package in ${arg_package_array[@]}
    do
        func_del_dir_or_file_safe "${g_install_dst_env_path}/${arg_package}" "y" "y"
        if [ "${g_install_status}" != "success" ]; then
            return
        fi
        if [ -d ${g_install_src_res_path}/${arg_package} ]; then
            cp -rf ${g_install_src_res_path}/${arg_package} ${g_install_dst_env_path}
            if [ "${arg_package}" == "ObuVoice" ]; then
                # sed -i "s|^            \"sn\":.*|            \"sn\":\"${g_robot_mmvee_sn}\"|" ${g_install_dst_env_path}/${arg_package}/pkt/Vtn_obu/mmvee.cfg
                # sed -i "s|^        \"sn\":.*|        \"sn\": \"${g_robot_mmvee_sn}\",|" ${g_install_dst_env_path}/${arg_package}/pkt/Vtn_obu/mmvee.cfg
                sed -i "s|^        \"sn\":.*|        \"sn\": \"${g_robot_info_sn}\",|" ${g_install_dst_env_path}/${arg_package}/pkt/Vtn_obu/mmvee.cfg
                func_run_sudo_command "chmod 0777 ${g_install_dst_env_path}/${arg_package}/pkt/*.sh"
                func_run_sudo_command "chmod 0777 ${g_install_dst_env_path}/${arg_package}/pkt/Vtn_obu/bin/*obu*"
            fi
        else
            echo "[$LINENO][WARN ][${arg_package}]ignore"
        fi
    done
}

g_need_update_service="n"
function func_check_is_update_service()
{
    local arg_src_srv="$1"
    local arg_dst_srv="$2"
    echo "[$LINENO][NOTE ]----- check service [${arg_dst_srv}]"
    if [ ! -f ${arg_dst_srv} ]; then
        echo "[$LINENO][NOTE ]service lost"
        g_need_update_service="y"
        return
    fi
    local arg_src_result=""
    local arg_dst_result=""
    arg_src_result=$(grep "CMCC_ROBOT_SERVICE_VER=" ${arg_src_srv})
    arg_dst_result=$(grep "CMCC_ROBOT_SERVICE_VER=" ${arg_dst_srv})
    if [ "${arg_src_result}" != "${arg_dst_result}" ]; then
        echo "[$LINENO][NOTE ][${arg_src_result} != ${arg_dst_result}]"
        g_need_update_service="y"
        return
    fi
    g_need_update_service="n"
}

function func_app_service_update()
{
    echo "[$LINENO][NOTE ]----- update app service"
    local arg_xiaoli_srv="run_xiaoli_server.service"
    local arg_xiaoli_sh="run_xiaoli_server.sh"
    local arg_service_path=${g_install_dst_srv_path}/cat
    local arg_cmcc_sl_service_path=${g_cmcc_sl_srv_path}/cat
    local arg_hostname=""
    local arg_sn_suffix_four=""
    local arg_hostname_path=/etc/hostname
    local arg_hosts_path=/etc/hosts
    local arg_alsa_conf="asound.conf"
    if [ "${g_target_no}" == "2.0" ]; then
        arg_service_path=${g_install_dst_srv_path}/app
        arg_cmcc_sl_service_path=${g_cmcc_sl_srv_path}/app
        arg_sn_suffix_four=${g_robot_info_sn:0-4:4}
        arg_hostname=CMB${g_robot_type}-${arg_sn_suffix_four}
        echo "[$LINENO][NOTE ]hostname: ${arg_hostname}"
        if cat ${arg_hostname_path} | grep -q "${arg_hostname}"; then
            echo "[$LINENO][NOTE ]not update hostname"
        else
            echo "[$LINENO][NOTE ]need update hostname"
            func_run_sudo_command "echo ${arg_hostname} > ${arg_hostname_path}"
        fi
        if cat ${arg_hosts_path} | grep -q "${arg_hostname}"; then
            echo "[$LINENO][NOTE ]not update hosts"
        else
            echo "[$LINENO][NOTE ]need update hosts"
            func_run_sudo_command "echo 127.0.0.1 ${arg_hostname} >> ${arg_hosts_path}"
        fi
        func_run_sudo_command "cp -rf "${arg_service_path}/${arg_alsa_conf}" "/etc""
    else
        sed -i "s|^User=.*|User=${g_user_name}|"   ${arg_service_path}/${arg_xiaoli_srv}
        sed -i "s|^Group=.*|Group=${g_user_name}|" ${arg_service_path}/${arg_xiaoli_srv}
    fi
    sed -i "s|^ExecStart=.*|ExecStart=bash ${arg_cmcc_sl_service_path}/${arg_xiaoli_sh}|" ${arg_service_path}/${arg_xiaoli_srv}
    sed -i "s|^g_target_user=.*|g_target_user=\"${g_user_name}\"|"                        ${arg_service_path}/${arg_xiaoli_sh}
    sed -i "s|^g_target_pwd=.*|g_target_pwd=\"${g_user_pwd}\"|"                           ${arg_service_path}/${arg_xiaoli_sh}
    sed -i "s|^g_target_is_test=.*|g_target_is_test=\"${g_is_test_dev}\"|"                ${arg_service_path}/${arg_xiaoli_sh}
    if [ "${g_target_no}" == "1.0" -o "${g_target_no}" == "1.1" ]; then
        sed -i "s|^g_target_is_pro=.*|g_target_is_pro=\"${g_robot_is_pro}\"|" ${arg_service_path}/${arg_xiaoli_sh}
    fi
    if [ "${g_target_no}" == "1.0" -o "${g_target_no}" == "1.1" ]; then
        sed -i "s|^g_sync_from_user=.*|g_sync_from_user=\"${g_sync_from_user}\"|" ${arg_service_path}/${arg_xiaoli_sh}
        sed -i "s|^g_sync_from_pwd=.*|g_sync_from_pwd=\"${g_sync_from_pwd}\"|"    ${arg_service_path}/${arg_xiaoli_sh}
        sed -i "s|^g_sync_from_ip=.*|g_sync_from_ip=\"${g_sync_from_ip}\"|"       ${arg_service_path}/${arg_xiaoli_sh}
    fi
    g_need_update_service="n"
    func_check_is_update_service "${arg_service_path}/${arg_xiaoli_srv}" "${g_sys_srv_path}/${arg_xiaoli_srv}"
    if [ "${g_need_update_service}" != "y" ]; then
        echo "[$LINENO][NOTE ][${arg_xiaoli_srv}]ignore"
    else
        echo "[$LINENO][NOTE ][${arg_xiaoli_srv}]update"
        func_run_sudo_command "cp -rf "${arg_service_path}/${arg_xiaoli_srv}" "${g_sys_srv_path}""
    fi
}

function func_motion_service_update()
{
    echo "[$LINENO][NOTE ]----- update motion service"
    local arg_service_path=${g_install_dst_srv_path}/andlink
    local arg_board_res_path=${arg_service_path}/board_resources
    local arg_andlink_srv="run_andlink_ros2.service"
    local arg_network_srv="run_network_ros2.service"
    local arg_andlink_sh="run_andlink_ros2.sh"
    local arg_andlink_stop_sh="stop_andlink_ros2.sh"
    local arg_network_sh="run_network_ros2.sh"
    local arg_network_stop_sh="stop_network_ros2.sh"
    local arg_dr_wifi_srv="dr_wifi.service"
    local arg_dr_5g_srv="dr_5g.service"
    # local arg_dr_ap_srv="dr_ap.service"
    local arg_hostname=""
    local arg_sn_suffix_four=""
    local arg_hostname_path=/etc/hostname
    local arg_hosts_path=/etc/hosts
    arg_sn_suffix_four=${g_robot_info_sn:0-4:4}
    arg_hostname=CMB${g_robot_type}-${arg_sn_suffix_four}
    echo "[$LINENO][NOTE ]hostname: ${arg_hostname}"
    if cat ${arg_hostname_path} | grep -q "${arg_hostname}"; then
        echo "[$LINENO][NOTE ]not update hostname"
    else
        echo "[$LINENO][NOTE ]need update hostname"
        func_run_sudo_command "echo ${arg_hostname} > ${arg_hostname_path}"
    fi
    if cat ${arg_hosts_path} | grep -q "${arg_hostname}"; then
        echo "[$LINENO][NOTE ]not update hosts"
    else
        echo "[$LINENO][NOTE ]need update hosts"
        func_run_sudo_command "echo 127.0.0.1 ${arg_hostname} >> ${arg_hosts_path}"
    fi

    # update robot no
    if [ "${g_robot_no}" != "" -a "${g_robot_no}" != " " ]; then
        echo "XiaoLi${g_robot_no}" > ${arg_board_res_path}/wifi/host_name
    else
        echo "XiaoLi${arg_sn_suffix_four}" > ${arg_board_res_path}/wifi/host_name
    fi

    local arg_cmcc_sl_srv_path=${g_cmcc_sl_srv_path}/andlink
    local arg_cmcc_sl_board_res_path=${arg_cmcc_sl_srv_path}/board_resources
    if [ "${g_target_no}" == "1.0" ]; then
        sed -i "s|^ExecStart=.*|ExecStart=bash ${arg_cmcc_sl_board_res_path}/internet_start.sh|" ${arg_board_res_path}/${arg_dr_wifi_srv}
    fi
    sed -i "s|^ExecStart=.*|ExecStart=bash ${arg_cmcc_sl_board_res_path}/5G/5G_ctl.sh enable|"  ${arg_board_res_path}/${arg_dr_5g_srv}
    # sed -i "s|^ExecStart=.*|ExecStart=bash ${arg_cmcc_sl_board_res_path}/ap_start.sh|"         ${arg_board_res_path}/${arg_dr_ap_srv}
    sed -i "s|^WorkingDirectory=.*|WorkingDirectory=${g_cmcc_sl_install}|"                      ${arg_service_path}/${arg_andlink_srv}
    sed -i "s|^ExecStart=.*|ExecStart=bash ${arg_cmcc_sl_srv_path}/${arg_andlink_sh}|"          ${arg_service_path}/${arg_andlink_srv}
    sed -i "s|^ExecStop=.*|ExecStop=bash ${arg_cmcc_sl_srv_path}/${arg_andlink_stop_sh}|"       ${arg_service_path}/${arg_andlink_srv}
    sed -i "s|^WorkingDirectory=.*|WorkingDirectory=${g_cmcc_sl_srv_path}|"                     ${arg_service_path}/${arg_network_srv}
    sed -i "s|^ExecStart=.*|ExecStart=bash ${arg_cmcc_sl_srv_path}/${arg_network_sh}|"          ${arg_service_path}/${arg_network_srv}
    sed -i "s|^ExecStop=.*|ExecStop=bash ${arg_cmcc_sl_srv_path}/${arg_network_stop_sh}|"       ${arg_service_path}/${arg_network_srv}

    if [ "${g_target_no}" == "1.0" ]; then
        g_need_update_service="n"
        func_check_is_update_service "${arg_board_res_path}/${arg_dr_wifi_srv}" "${g_sys_srv_path}/${arg_dr_wifi_srv}"
        if [ "${g_need_update_service}" != "y" ]; then
            echo "[$LINENO][NOTE ][${arg_dr_wifi_srv}]ignore"
        else
            echo "[$LINENO][NOTE ][${arg_dr_wifi_srv}]update"
            func_run_sudo_command "cp -rf "${arg_board_res_path}/${arg_dr_wifi_srv}" "${g_sys_srv_path}""
        fi
    fi

    g_need_update_service="n"
    func_check_is_update_service "${arg_board_res_path}/${arg_dr_5g_srv}" "${g_sys_srv_path}/${arg_dr_5g_srv}"
    if [ "${g_need_update_service}" != "y" ]; then
        echo "[$LINENO][NOTE ][${arg_dr_5g_srv}]ignore"
    else
        echo "[$LINENO][NOTE ][${arg_dr_5g_srv}]update"
        func_run_sudo_command "cp -rf "${arg_board_res_path}/${arg_dr_5g_srv}" "${g_sys_srv_path}""
    fi

    # g_need_update_service="n"
    # func_check_is_update_service "${arg_board_res_path}/${arg_dr_ap_srv}" "${g_sys_srv_path}/${arg_dr_ap_srv}"
    # if [ "${g_need_update_service}" != "y" ]; then
    #     echo "[$LINENO][NOTE ][${arg_dr_ap_srv}]ignore"
    # else
    #     echo "[$LINENO][NOTE ][${arg_dr_ap_srv}]update"
    #     func_run_sudo_command "cp -rf "${arg_board_res_path}/${arg_dr_ap_srv}" "${g_sys_srv_path}""
    # fi

    g_need_update_service="n"
    func_check_is_update_service "${arg_service_path}/${arg_andlink_srv}" "${g_sys_srv_path}/${arg_andlink_srv}"
    if [ "${g_need_update_service}" != "y" ]; then
        echo "[$LINENO][NOTE ][${arg_andlink_srv}]ignore"
    else
        echo "[$LINENO][NOTE ][${arg_andlink_srv}]update"
        func_run_sudo_command "cp -rf "${arg_service_path}/${arg_andlink_srv}" "${g_sys_srv_path}""
    fi

    g_need_update_service="n"
    func_check_is_update_service "${arg_service_path}/${arg_network_srv}" "${g_sys_srv_path}/${arg_network_srv}"
    if [ "${g_need_update_service}" != "y" ]; then
        echo "[$LINENO][NOTE ][${arg_network_srv}]ignore"
    else
        echo "[$LINENO][NOTE ][${arg_network_srv}]update"
        func_run_sudo_command "cp -rf "${arg_service_path}/${arg_network_srv}" "${g_sys_srv_path}""
    fi
}

function func_cognition_service_update()
{
    echo "[$LINENO][NOTE ]----- update cognition service"
    local arg_nvidia_srv="run_nvidia_rob_ros2.service"
    local arg_nvidia_sh="run_nvidia_rob_ros2.sh"
    local arg_service_path=${g_install_dst_srv_path}/nvidia
    local arg_cmcc_sl_service_path=${g_cmcc_sl_srv_path}/nvidia
    sed -i "s|^User=.*|User=${g_user_name}|"                                              ${arg_service_path}/${arg_nvidia_srv}
    sed -i "s|^Group=.*|Group=${g_user_name}|"                                            ${arg_service_path}/${arg_nvidia_srv}
    sed -i "s|^ExecStart=.*|ExecStart=bash ${arg_cmcc_sl_service_path}/${arg_nvidia_sh}|" ${arg_service_path}/${arg_nvidia_srv}
    sed -i "s|^g_target_user=.*|g_target_user=\"${g_user_name}\"|"                        ${arg_service_path}/${arg_nvidia_sh}
    sed -i "s|^g_target_pwd=.*|g_target_pwd=\"${g_user_pwd}\"|"                           ${arg_service_path}/${arg_nvidia_sh}
    sed -i "s|^g_target_is_test=.*|g_target_is_test=\"${g_is_test_dev}\"|"                ${arg_service_path}/${arg_nvidia_sh}
    sed -i "s|^g_target_is_pro=.*|g_target_is_pro=\"${g_robot_is_pro}\"|"                 ${arg_service_path}/${arg_nvidia_sh}
    sed -i "s|^g_sync_from_user=.*|g_sync_from_user=\"${g_sync_from_user}\"|"             ${arg_service_path}/${arg_nvidia_sh}
    sed -i "s|^g_sync_from_pwd=.*|g_sync_from_pwd=\"${g_sync_from_pwd}\"|"                ${arg_service_path}/${arg_nvidia_sh}
    sed -i "s|^g_sync_from_ip=.*|g_sync_from_ip=\"${g_sync_from_ip}\"|"                   ${arg_service_path}/${arg_nvidia_sh}
    g_need_update_service="n"
    func_check_is_update_service "${arg_service_path}/${arg_nvidia_srv}" "${g_sys_srv_path}/${arg_nvidia_srv}"
    if [ "${g_need_update_service}" != "y" ]; then
        echo "[$LINENO][NOTE ][${arg_nvidia_srv}]ignore"
    else
        echo "[$LINENO][NOTE ][${arg_nvidia_srv}]update"
        func_run_sudo_command "cp -rf "${arg_service_path}/${arg_nvidia_srv}" "${g_sys_srv_path}""
    fi
}

function func_all_services_update()
{
    echo "[$LINENO][NOTE ]----- update all services"
    if [ "${g_target_board}" == "app" ]; then
        func_app_service_update
        return
    fi
    if [ "${g_target_board}" == "motion" ]; then
        func_motion_service_update
        return
    fi
    if [ "${g_target_board}" == "cognition" ]; then
        func_cognition_service_update
        return
    fi
}

function func_update_revert()
{
    echo "[$LINENO][NOTE ]----- update revert"
    if [ ! -L "${g_cmcc_sl_install}" ]; then
        echo "[$LINENO][ERROR]symbolic link invalid"
        g_install_status="func_update_revert"
        return
    fi
    local arg_cur_install_path=""
    arg_cur_install_path=$(realpath "${g_cmcc_sl_install}")
    if [ "${arg_cur_install_path}" == "" -o "${arg_cur_install_path}" == " " ]; then
        echo "[$LINENO][ERROR]cur link path invalid"
        g_install_status="func_update_revert"
        return
    fi
    echo "[$LINENO][NOTE ]cur link path[${arg_cur_install_path}]"
    local arg_path=""
    local arg_dir=""
    local arg_bak="_bak"
    local arg_to_dir=""
    local arg_cur_link_flag_path=""
    arg_cur_link_flag_path=${arg_cur_install_path}/${g_cur_link_flag}
    echo "[$LINENO][NOTE ]firstly, check flag[${arg_cur_link_flag_path}]"
    if [ -f ${arg_cur_link_flag_path} ]; then
        echo "[$LINENO][NOTE ]flag exist, match to link, not revert"
        return
    fi
    arg_path=${arg_cur_install_path%/*}
    arg_dir=${arg_cur_install_path##*/}
    if [[ ${arg_dir} =~ ${arg_bak} ]]; then
        echo "[$LINENO][NOTE ]with bak"
        arg_to_dir=${arg_dir%_bak*}
    else
        echo "[$LINENO][NOTE ]without bak"
        arg_to_dir="${arg_dir}_bak"
    fi
    arg_cur_install_path=${arg_path}/${arg_to_dir}
    arg_cur_link_flag_path=${arg_cur_install_path}/${g_cur_link_flag}
    echo "[$LINENO][NOTE ]secondly, check flag[${arg_cur_link_flag_path}]"
    if [ -f ${arg_cur_link_flag_path} ]; then
        echo "[$LINENO][NOTE ]flag exist, dismatch to link, revert"
        func_all_services_stop
        if [ "${g_install_status}" != "success" ]; then
            return
        fi
        func_run_sudo_command "ln -snf ${arg_cur_install_path} ${g_cmcc_sl_install}"
        func_all_services_start
        return
    fi
    echo "[$LINENO][NOTE ]flag lost, ignore"
    g_install_status="func_update_revert"
    return
}

g_start_install="n"
g_keep_zip="n"
g_install_display="install"
function func_handle_type_check()
{
    echo "[$LINENO][NOTE ]----- check handle type: ${g_handle_type}"
    if [ "${g_handle_type}" == "" -o "${g_handle_type}" == " " ]; then
        echo "[$LINENO][NOTE ]regular install"
        g_start_install="y"
        return
    fi
    if [ "${g_handle_type}" == "start" ]; then
        func_all_services_start
        return
    fi
    if [ "${g_handle_type}" == "stop" ]; then
        func_all_services_stop
        return
    fi
    if [ "${g_handle_type}" == "extract" ]; then
        func_extract_zip "y"
        if [ "${g_install_status}" == "success" ]; then
            g_keep_zip="y"
        fi
        return
    fi
    if [ "${g_handle_type}" == "revert" ]; then
        if [ "${g_is_backup}" == "y" ]; then
            g_install_display="revert"
            func_update_revert
        else
            echo "[$LINENO][ERROR]ota off"
            g_install_status="func_handle_type_check"
        fi
        return
    fi
    echo "[$LINENO][ERROR]not support [${g_handle_type}]"
    g_install_status="func_handle_type_check"
}

g_update_finish_record=/tmp/cmcc_robot_ota_finish
g_update_status_record=/tmp/cmcc_robot_ota_status.ini
function func_clear_install_flag()
{
    echo "[$LINENO][NOTE ]----- clear install flag"
    func_del_dir_or_file_safe "${g_update_finish_record}" "n" "y"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    func_del_dir_or_file_safe "${g_update_status_record}" "n" "y"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
}

function func_update_finish()
{
    echo "[$LINENO][NOTE ]----- update finish"
    echo "finish" > ${g_update_finish_record}
}

function func_update_all()
{
    local arg_update_type="$1"
    echo "[$LINENO][NOTE ]----- update all [${arg_update_type}]"
    if [ ! -f ${g_pack_info_path} ]; then
        echo "[$LINENO][WARN ][${g_pack_info_path}]lost"
        return
    fi
    local arg_result=""
    local arg_right=""
    local arg_board=""
    local arg_user=""
    local arg_pwd=""
    local arg_file=""
    local arg_ip_no=""
    local arg_ip=""
    local arg_md5=""
    local arg_md5_temp=""
    local arg_cmd_args=""
    local arg_update_result=""
    local arg_update_log=""
    local arg_file_path=""
    arg_result=$(grep 'PACK:' ${g_pack_info_path})
    for arg_line in ${arg_result}
    do
        arg_result=${arg_line// /}
        arg_right=${arg_result//'\n'/}
        arg_right=${arg_right#*:} # skip PACK
        arg_board=${arg_right%%:*}
        if [ "${arg_board}" == "${g_target_board}" ]; then
            echo "[$LINENO][NOTE ][${arg_board}]skip self"
            continue
        fi
        arg_right=${arg_right#*:} # skip board
        arg_user=${arg_right%%:*}
        if [ "${arg_user}" == "" -o "${arg_user}" == " " ]; then
            echo "[$LINENO][ERROR]${arg_board}user empty"
            g_install_status="func_update_all"
            return
        fi
        arg_right=${arg_right#*:} # skip user
        arg_pwd=${arg_right%%:*}
        arg_right=${arg_right#*:} # skip pwd
        arg_ip_no=${arg_right%%:*}
        arg_ip="${g_ip_prefix}.${arg_ip_no}"
        arg_right=${arg_right#*:} # skip ip
        arg_file=${arg_right%%:*}
        arg_file_path=${g_root_of_script}/${arg_file}
        if [ ! -f ${arg_file_path} ]; then
            echo "[$LINENO][WARN ][${arg_file_path}]lost"
            continue
        fi
        arg_right=${arg_right#*:} # skip file
        arg_md5=${arg_right%%:*}
        arg_md5_temp=($(md5sum ${arg_file_path}))
        if [ "${arg_md5}" != "${arg_md5_temp}" ]; then
            echo "[$LINENO][ERROR][${arg_board}][md5, ${arg_md5} != ${arg_md5_temp}]"
            g_install_status="func_update_all"
            return
        fi
        arg_cmd_args=""
        if [ "${g_robot_is_pro}" == "n" ]; then
            if [ "${arg_board}" == "cognition" ]; then
                echo "[$LINENO][NOTE ]skip cognition for base"
                continue
            fi
            arg_cmd_args="${arg_cmd_args} --base"
        fi
        if [ "${g_is_test_dev}" == "y" ]; then
            arg_cmd_args="${arg_cmd_args} --test"
        fi
        if [ "${g_is_ota}" == "y" ]; then
            arg_cmd_args="${arg_cmd_args} --backup"
        fi
        if [ "${arg_update_type}" == "revert" ]; then
            arg_update_log=/tmp/revert_${arg_board}_${g_install_time}.log
        else
            arg_update_log=/tmp/install_${arg_board}_${g_install_time}.log
        fi
        echo "[$LINENO][NOTE ]usr:  ${arg_user}"
        echo "[$LINENO][NOTE ]pwd:  ${arg_pwd}"
        echo "[$LINENO][NOTE ]ip:   ${arg_ip}"
        echo "[$LINENO][NOTE ]file: ${arg_file_path}"
        echo "[$LINENO][NOTE ]md5:  ${arg_md5}"
        echo "[$LINENO][NOTE ]args: ${arg_cmd_args}"
        echo "[$LINENO][NOTE ]log:  ${arg_update_log}"
        if [ "${g_target_no}" == "1.0" -o "${g_target_no}" == "1.1" ]; then
            func_check_ssh "${arg_user}" "${arg_pwd}" "${arg_ip}"
            if [ "${g_install_status}" != "success" ]; then
                return
            fi
        fi
        # sshpass -p "${arg_pwd}" scp ${g_pack_info_path} "${arg_user}"@"${arg_ip}":/tmp
        # sshpass -p "${arg_pwd}" scp ${arg_file_path} "${arg_user}"@"${arg_ip}":/tmp
        echo "[$LINENO][NOTE ]sending ${g_pack_info_path} ..."
        sshpass -p "${arg_pwd}" rsync -av ${g_pack_info_path} "${arg_user}"@"${arg_ip}":/tmp
        echo "[$LINENO][NOTE ]sending ${arg_file_path} ..."
        sshpass -p "${arg_pwd}" rsync -av ${arg_file_path} "${arg_user}"@"${arg_ip}":/tmp
        if [ "${arg_update_type}" == "revert" ]; then
            echo "[$LINENO][NOTE ]revert ${arg_board} ..."
            sshpass -p "${arg_pwd}" ssh -tt "${arg_user}"@"${arg_ip}" "/tmp/${arg_file} --backup --handle=revert" > ${arg_update_log}
            arg_update_result=$(grep "revert_${arg_board}_success" ${arg_update_log})
            if [ "${arg_update_result}" == "" -o "${arg_update_result}" == " " ]; then
                echo "[$LINENO][ERROR][${arg_board}]revert failed"
            else
                echo "[$LINENO][NOTE ][${arg_board}]revert success"
            fi
        else
            echo "[$LINENO][NOTE ]upgrade ${arg_file_path} ..."
            sshpass -p "${arg_pwd}" ssh -tt "${arg_user}"@"${arg_ip}" "/tmp/${arg_file} ${arg_cmd_args}" > ${arg_update_log}
            arg_update_result=$(grep "install_${arg_board}_success" ${arg_update_log})
            if [ "${arg_update_result}" == "" -o "${arg_update_result}" == " " ]; then
                echo "[$LINENO][ERROR][${arg_board}]update failed"
                g_install_status="func_update_all_for_${arg_board}"
                return
            else
                echo "[$LINENO][NOTE ][${arg_board}]update success"
            fi
        fi
    done
}

function func_record_update_status()
{
    echo "[$LINENO][NOTE ]----- record update status"
    if [ ! -f ${g_update_status_record} ]; then
        if [ "${g_install_status}" == "success" ]; then
            echo "robbody_ota_status=success" > ${g_update_status_record}
        else
            echo "robbody_ota_status=failed" > ${g_update_status_record}
        fi
        return
    fi
    local arg_result=""
    arg_result=$(grep "robbody_ota_status=" ${g_update_status_record})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        if [ "${g_install_status}" == "success" ]; then
            echo "robbody_ota_status=success" >> ${g_update_status_record}
        else
            echo "robbody_ota_status=failed" >> ${g_update_status_record}
        fi
        return
    fi
    if [ "${g_install_status}" == "success" ]; then
        sed -i "s|^robbody_ota_status=.*|robbody_ota_status=success|" ${g_update_status_record}
    else
        sed -i "s|^robbody_ota_status=.*|robbody_ota_status=failed|" ${g_update_status_record}
    fi
}

while true
do
    g_install_status="success"
    func_check_param
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_check_env
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_check_default_info
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_check_account_info
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_check_is_board_match
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_clear_install_flag
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_handle_type_check
    if [ "${g_install_status}" != "success" ]; then
        break
    fi
    if [ "${g_start_install}" != "y" ]; then
        break
    fi

    if [ "${g_is_update_all}" == "y" ]; then
        g_install_status="success"
        func_update_all "common"
        if [ "${g_install_status}" != "success" ]; then
            break
        fi
    fi

    g_install_status="success"
    func_install_prepare
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_cmcc_symbolic_link_check
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    if [ "${g_target_no}" == "1.0" -o "${g_target_no}" == "1.1" ]; then
        g_install_status="success"
        func_sync_data
        if [ "${g_install_status}" != "success" ]; then
            break
        fi
    fi

    g_install_status="success"
    func_extract_zip "n"
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_update_factory
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_get_robot_info
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_all_services_stop
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_install_package
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_install_env
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_robot_deploy
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_all_services_update
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_cmcc_symbolic_link_handle
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_all_services_start
    break
done

if [ "${g_install_status}" != "success" ]; then
    echo "[$LINENO][ERROR][${g_install_status}]${g_install_display}_${g_target_board}_failed"
else
    echo "[$LINENO][NOTE ][${g_install_status}]${g_install_display}_${g_target_board}_success"
fi
if [ "${g_is_ota}" == "y" ]; then
    if [ "${g_install_status}" != "success" ]; then
        echo "[$LINENO][NOTE ]revert all boards"
        func_update_all "revert"
    fi
fi
if [ "${g_start_install}" == "y" -a "${g_is_ota}" == "y" ]; then
    func_record_update_status
fi

if [ "${g_keep_zip}" == "n" ]; then
    func_del_dir_or_file_safe "${g_install_quick_path}" "y" "y"
fi
if [ "${g_start_install}" == "y" -a "${g_is_ota}" == "y" ]; then
    func_update_finish
fi
sync

exit 0
