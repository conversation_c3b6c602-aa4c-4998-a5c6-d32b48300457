#!/bin/bash

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install
g_target_user=""
g_target_pwd=""
g_target_is_test=""

MAX_RETRIES=60       # 最大重试次数
RETRY_INTERVAL=1     # 重试间隔（秒）
TIMEOUT=$((MAX_RETRIES * RETRY_INTERVAL))  # 总超时时间（秒）

sudo apt-get install libcurl4-openssl-dev
pip install pycurl


current_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "xiaoli server 进入时间：$current_time"

check_ros2_init() {
  # 1. 检查 ros2 daemon 进程
  if ! pgrep -x "_ros2_daemon" > /dev/null; then
    echo "错误:ros2_daemon 未运行"
    echo "尝试启动 daemon..."
    ros2 daemon start
    sleep 2  # 等待 daemon 启动
    if ! pgrep -x "_ros2_daemon" > /dev/null; then
	    echo "错误:ros2_daemon 仍未运行"
    	    return 1
    else 
	    echo "ros2_daemon 已经运行"
    fi
  else
    echo "ros2_daemon 正在运行"
  fi
  
  # 2. 检查 ros2 命令可用性
  if ! ros2 -h &> /dev/null; then
    echo "错误:ros2 命令不可用"
    return 1
  else 
    echo "ros2 命令可用"
  fi
  
  echo "ROS 2 初始化完成"
  return 0
}

function func_run_sudo_command()
{
    local arg_command="$1"
    # echo "[$LINENO][NOTE ]pwd: ${g_target_pwd}, cmd: ${arg_command}"
    echo "${g_target_pwd}" | sudo -S bash -c "${arg_command}"
    if [ $? -ne 0 ]; then
        echo "[$LINENO][ERROR][${arg_command}]failed"
        g_cmd_exe_status="func_run_sudo_command"
        return
    fi
}

# 设置默认播放设备,当设备插拔时，或者程序启动时调用
# 优先级: [USB Audio Device]  
# 指定USB设备名
usb_device_name="USB_Audio_Device"

# 通过USB设备名获取声卡ID
#soundcard_id=$(aplay -l | grep "${usb_device_name}" -A 2 | grep "card" | awk '{print $2}' | tr -d ':' | head -n 1)
#name: <alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.analog-stereo>
padevname=$(pacmd list-sinks | grep -e name: | grep -e ${usb_device_name} | head -n 1 | sed 's/.*<\(.*\)>.*/\1/')

if [ "${padevname}" = "" ]; then
echo "no playback card named: \"${usb_device_name}\"!"
else 
# 通过pulseaudio 设置默认声卡
pacmd set-default-sink ${padevname}
echo "set default sink \"${padevname}\""
fi

if [ -d ${g_install_path}/env/ObuVoice ]; then
    echo "[$LINENO][NOTE ]start ifly ..."
    bash ${g_install_path}/env/ObuVoice/pkt/ifly-init.sh &
    echo "[$LINENO][NOTE ]start ifly over"
fi


echo "[$LINENO][NOTE ]start and get unitree history log ..."
bash ${g_install_path}/env/service/app/log_unitree.sh &
echo "[$LINENO][NOTE ] get unitree history log  over"

source /opt/ros/foxy/setup.bash
source ${g_install_path}/setup.bash
source /opt/ros/unitree_ros2/cyclonedds_ws/install/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
export CYCLONEDDS_URI='<CycloneDDS><Domain><General><Interfaces>
                            <NetworkInterface name="eth0" priority="default" multicast="default" />
                        </Interfaces><AllowMulticast>spdp</AllowMulticast></General></Domain></CycloneDDS>'
# 检测mpv进程是否存在，不存在则启动expression节点并等待90秒
echo "[$LINENO][NOTE ]检测mpv进程..."
if ! pgrep -x "mpv" > /dev/null; then
    echo "[$LINENO][NOTE ]mpv进程不存在，启动expression节点..."
    ros2 run expression expression_node &
    expression_pid=$!
    echo "[$LINENO][NOTE ]等待15秒进行开机动画演示..."
    sleep 30
    echo "[$LINENO][NOTE ]表情演示完成，继续启动主程序"
else
    echo "[$LINENO][NOTE ]mpv进程已存在，无需启动expression节点"
fi

# 检测ros2、dds环境是否ok
echo "开始检测 ROS 2 初始化状态，超时时间 $TIMEOUT 秒..."
start_time=$(date +%s)
elapsed_time=0
retry_count=0

while [ $elapsed_time -lt $TIMEOUT ]; do
  echo "--- 尝试 $((retry_count+1))/$(($MAX_RETRIES)) ---"
  
  if check_ros2_init; then
    echo "ROS 2 已成功初始化,耗时 $elapsed_time 秒"
    break
  fi
  
  retry_count=$((retry_count+1))
  elapsed_time=$(($(date +%s) - $start_time))
  
  if [ $elapsed_time -lt $TIMEOUT ]; then
    remaining_time=$(($TIMEOUT - $elapsed_time))
    echo "等待 $RETRY_INTERVAL 秒后重试，剩余时间 $remaining_time 秒..."
    sleep $RETRY_INTERVAL
  fi
done

current_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "ros2 初始化完成时间：$current_time"

export GST_PLUGIN_PATH=/usr/lib/aarch64-linux-gnu/gstreamer-1.0/
if [ -d ${g_install_path}/env/lib ]; then
    echo "[$LINENO][NOTE ]export lib"
    export LD_LIBRARY_PATH=${g_install_path}/env/lib:$LD_LIBRARY_PATH
    if [ "${g_target_is_test}" == "y" ]; then
        if [ -d ${g_install_path}/env/lib/dev ]; then
            echo "[$LINENO][NOTE ]export lib dev"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/dev:$LD_LIBRARY_PATH
        fi
    else
        if [ -d ${g_install_path}/env/lib/pro ]; then
            echo "[$LINENO][NOTE ]export lib pro"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/pro:$LD_LIBRARY_PATH
        fi
    fi
fi
echo "start ros2 launch launch_package unitree_ctrl_robdog.py"
ros2 launch launch_package unitree_ctrl_robdog.py
