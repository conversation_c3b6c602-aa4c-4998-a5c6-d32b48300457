#!/bin/bash

# 快速重新加载 wt 工具
# 用法: source reload-wt.sh 或 . reload-wt.sh

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WT_SCRIPT="$SCRIPT_DIR/wt.sh"

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}重新加载 wt 工具...${NC}"

if [[ -f "$WT_SCRIPT" ]]; then
    source "$WT_SCRIPT"
    echo -e "${GREEN}✓ wt 工具已重新加载${NC}"
    echo ""
    echo "可用命令:"
    echo "  wt t    - 切换到 test"
    echo "  wt u    - 切换到 unitree-debug"
    echo "  wt d    - 切换到 dev-1.1.0"
    echo "  wt m    - 切换到 master"
    echo "  wt x    - 切换到 xuhui"
    echo "  wtlist  - 列出所有 worktrees"
else
    echo -e "${RED}错误: 找不到 $WT_SCRIPT${NC}"
fi
