#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/mine/code/robot-application-origin/3rdParty/factory/factory_update.c
stdio.h
-
stdlib.h
-
fcntl.h
-
unistd.h
-
sys/ioctl.h
-
string.h
-
errno.h
-
vendor.h
/mine/code/robot-application-origin/3rdParty/factory/vendor.h

/mine/code/robot-application-origin/3rdParty/factory/vendor.h
stdint.h
-

