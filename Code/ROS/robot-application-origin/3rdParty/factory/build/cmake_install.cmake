# Install script for directory: /mine/code/robot-application-origin/3rdParty/factory

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/usr/local")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_update" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_update")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_update"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_update")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/mine/code/robot-application-origin/3rdParty/factory/install/factory" TYPE EXECUTABLE FILES "/mine/code/robot-application-origin/3rdParty/factory/build/factory_update")
  if(EXISTS "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_update" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_update")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_update")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_read" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_read")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_read"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_read")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/mine/code/robot-application-origin/3rdParty/factory/install/factory" TYPE EXECUTABLE FILES "/mine/code/robot-application-origin/3rdParty/factory/build/factory_read")
  if(EXISTS "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_read" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_read")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/mine/code/robot-application-origin/3rdParty/factory/install/factory/factory_read")
    endif()
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT)
  set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
file(WRITE "/mine/code/robot-application-origin/3rdParty/factory/build/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
