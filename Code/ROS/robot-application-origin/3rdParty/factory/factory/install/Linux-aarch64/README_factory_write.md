# Factory Write 工具使用说明

## 概述

`factory_write` 是一个用于向设备vendor storage写入工厂数据的工具。该工具已更新为支持通过命令行参数传入4个关键参数。

## 功能特性

- 支持4个参数的命令行输入
- 参数验证（包括MAC地址格式验证）
- 错误处理和详细的错误信息
- 清晰的使用说明和示例

## 支持的参数

程序需要按顺序传入以下4个参数：

1. **SN** - 设备序列号 (VENDOR_CMCC_SN_ID: 0x01)
2. **CMEI** - 设备CMEI (VENDOR_CMCC_CMEI_ID: 0x0F)
3. **MAC** - MAC地址 (VENDOR_CMCC_MAC_ID: 0x02)
4. **OVD Login** - OVD登录密码 (VENDOR_CMCC_OVD_LOGIN_ID: 0x0B)

## 使用方法

### 基本语法
```bash
./factory_write <sn> <cmei> <mac> <ovd_login>
```

### 示例
```bash
./factory_write 1830004229212345670000072 212345670000072 6A:9B:B6:D9:E5:31 1EIpLNAn
```

### 参数说明
- `sn`: 设备序列号，字符串格式，直接写入
- `cmei`: 设备CMEI，字符串格式，直接写入
- `mac`: MAC地址，必须是 XX:XX:XX:XX:XX:XX 格式（支持大小写），**会被转换为6字节二进制数据写入**
- `ovd_login`: OVD登录密码，字符串格式，直接写入

### MAC地址特殊处理
MAC地址参数会进行特殊处理：
- 输入格式：`6A:9B:B6:D9:E5:31`（字符串）
- 写入格式：`0x6A 0x9B 0xB6 0xD9 0xE5 0x31`（6字节二进制数据）
- 这样确保MAC地址以正确的二进制格式存储在vendor storage中

## 编译

确保在CMakeLists.txt中启用了factory_write的编译：

```cmake
add_executable(factory_write
  ${CMAKE_CURRENT_SOURCE_DIR}/factory_write.c
)
target_include_directories(factory_write PRIVATE
  ${CMAKE_CURRENT_SOURCE_DIR}
)
```

然后编译：
```bash
mkdir build
cd build
cmake ..
make factory_write
```

## 测试

使用提供的测试脚本：
```bash
chmod +x test_factory_write.sh
./test_factory_write.sh
```

## 错误处理

程序会进行以下验证：
- 参数数量检查（必须是4个）
- 参数非空检查
- MAC地址格式验证
- vendor storage设备打开检查
- 写入操作错误检查

## 输出示例

成功执行时的输出：
```
Writing factory data with parameters:
  SN: 1830004229212345670000072
  CMEI: 212345670000072
  MAC: 6A:9B:B6:D9:E5:31
  OVD Login: 1EIpLNAn

vendor_write: ID: 0x01, data:
25:1830004229212345670000072
vendor_write: ID: 0x0f, data:
15:212345670000072
MAC address parsed: 6A:9B:B6:D9:E5:31 -> 0x6A 0x9B 0xB6 0xD9 0xE5 0x31
vendor_write: ID: 0x02, MAC data (6 bytes): 0x6A 0x9B 0xB6 0xD9 0xE5 0x31
vendor_write: ID: 0x0b, data:
8:1EIpLNAn

All factory data written successfully!
```

## 注意事项

1. 程序需要root权限才能访问 `/dev/vendor_storage` 设备
2. MAC地址必须严格按照 XX:XX:XX:XX:XX:XX 格式输入
3. 所有参数都不能为空
4. 建议在实际使用前先进行测试验证
