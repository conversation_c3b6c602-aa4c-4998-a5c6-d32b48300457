#!/bin/bash

# 测试factory_write程序的脚本
# 使用示例数据进行测试

echo "=== Factory Write Test Script ==="
echo

# 检查factory_write可执行文件是否存在
if [ ! -f "./factory_write" ]; then
    echo "Error: factory_write executable not found!"
    echo "Please compile the project first using cmake and make."
    exit 1
fi

# 测试参数
SN="1830004229212345670000072"
CMEI="212345670000072"
MAC="6A:9B:B6:D9:E5:31"
OVD_LOGIN="1EIpLNAn"

echo "Testing factory_write with the following parameters:"
echo "  SN: $SN"
echo "  CMEI: $CMEI"
echo "  MAC: $MAC (will be converted to 6 bytes: 0x6A 0x9B 0xB6 0xD9 0xE5 0x31)"
echo "  OVD Login: $OVD_LOGIN"
echo

# 执行factory_write
echo "Executing: ./factory_write $SN $CMEI $MAC $OVD_LOGIN"
echo "----------------------------------------"
./factory_write "$SN" "$CMEI" "$MAC" "$OVD_LOGIN"
RESULT=$?

echo "----------------------------------------"
if [ $RESULT -eq 0 ]; then
    echo "✓ Test completed successfully!"
else
    echo "✗ Test failed with exit code: $RESULT"
fi

echo
echo "=== Test Script Finished ==="
