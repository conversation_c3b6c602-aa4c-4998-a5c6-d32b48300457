# Install script for directory: /mine/worktrees/unitree-debug/3rdParty/factory

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/usr/local")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "TRUE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/opt/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-objdump")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_update" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_update")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_update"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_update")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64" TYPE EXECUTABLE FILES "/mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588/factory_update")
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_update" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_update")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/opt/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-strip" "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_update")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_read" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_read")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_read"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_read")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64" TYPE EXECUTABLE FILES "/mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588/factory_read")
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_read" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_read")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/opt/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-strip" "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_read")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_write" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_write")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_write"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_write")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64" TYPE EXECUTABLE FILES "/mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588/factory_write")
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_write" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_write")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/opt/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-strip" "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/factory_write")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_mac_conversion" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_mac_conversion")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_mac_conversion"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_mac_conversion")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64" TYPE EXECUTABLE FILES "/mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588/test_mac_conversion")
  if(EXISTS "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_mac_conversion" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_mac_conversion")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/opt/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-strip" "$ENV{DESTDIR}/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_mac_conversion")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/test_factory_write.sh;/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64/README_factory_write.md")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/mine/worktrees/unitree-debug/3rdParty/factory/install/Linux-aarch64" TYPE FILE PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE FILES
    "/mine/worktrees/unitree-debug/3rdParty/factory/test_factory_write.sh"
    "/mine/worktrees/unitree-debug/3rdParty/factory/README_factory_write.md"
    )
endif()

if(CMAKE_INSTALL_COMPONENT)
  set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
file(WRITE "/mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
