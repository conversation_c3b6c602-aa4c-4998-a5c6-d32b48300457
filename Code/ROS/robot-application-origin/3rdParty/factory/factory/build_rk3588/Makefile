# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mine/worktrees/unitree-debug/3rdParty/factory

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588/CMakeFiles /mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mine/worktrees/unitree-debug/3rdParty/factory/build_rk3588/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named factory_update

# Build rule for target.
factory_update: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 factory_update
.PHONY : factory_update

# fast build rule for target.
factory_update/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_update.dir/build.make CMakeFiles/factory_update.dir/build
.PHONY : factory_update/fast

#=============================================================================
# Target rules for targets named factory_read

# Build rule for target.
factory_read: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 factory_read
.PHONY : factory_read

# fast build rule for target.
factory_read/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_read.dir/build.make CMakeFiles/factory_read.dir/build
.PHONY : factory_read/fast

#=============================================================================
# Target rules for targets named factory_write

# Build rule for target.
factory_write: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 factory_write
.PHONY : factory_write

# fast build rule for target.
factory_write/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_write.dir/build.make CMakeFiles/factory_write.dir/build
.PHONY : factory_write/fast

#=============================================================================
# Target rules for targets named test_mac_conversion

# Build rule for target.
test_mac_conversion: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_mac_conversion
.PHONY : test_mac_conversion

# fast build rule for target.
test_mac_conversion/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mac_conversion.dir/build.make CMakeFiles/test_mac_conversion.dir/build
.PHONY : test_mac_conversion/fast

factory_read.o: factory_read.c.o
.PHONY : factory_read.o

# target to build an object file
factory_read.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_read.dir/build.make CMakeFiles/factory_read.dir/factory_read.c.o
.PHONY : factory_read.c.o

factory_read.i: factory_read.c.i
.PHONY : factory_read.i

# target to preprocess a source file
factory_read.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_read.dir/build.make CMakeFiles/factory_read.dir/factory_read.c.i
.PHONY : factory_read.c.i

factory_read.s: factory_read.c.s
.PHONY : factory_read.s

# target to generate assembly for a file
factory_read.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_read.dir/build.make CMakeFiles/factory_read.dir/factory_read.c.s
.PHONY : factory_read.c.s

factory_update.o: factory_update.c.o
.PHONY : factory_update.o

# target to build an object file
factory_update.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_update.dir/build.make CMakeFiles/factory_update.dir/factory_update.c.o
.PHONY : factory_update.c.o

factory_update.i: factory_update.c.i
.PHONY : factory_update.i

# target to preprocess a source file
factory_update.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_update.dir/build.make CMakeFiles/factory_update.dir/factory_update.c.i
.PHONY : factory_update.c.i

factory_update.s: factory_update.c.s
.PHONY : factory_update.s

# target to generate assembly for a file
factory_update.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_update.dir/build.make CMakeFiles/factory_update.dir/factory_update.c.s
.PHONY : factory_update.c.s

factory_write.o: factory_write.c.o
.PHONY : factory_write.o

# target to build an object file
factory_write.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_write.dir/build.make CMakeFiles/factory_write.dir/factory_write.c.o
.PHONY : factory_write.c.o

factory_write.i: factory_write.c.i
.PHONY : factory_write.i

# target to preprocess a source file
factory_write.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_write.dir/build.make CMakeFiles/factory_write.dir/factory_write.c.i
.PHONY : factory_write.c.i

factory_write.s: factory_write.c.s
.PHONY : factory_write.s

# target to generate assembly for a file
factory_write.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/factory_write.dir/build.make CMakeFiles/factory_write.dir/factory_write.c.s
.PHONY : factory_write.c.s

test_mac_conversion.o: test_mac_conversion.c.o
.PHONY : test_mac_conversion.o

# target to build an object file
test_mac_conversion.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mac_conversion.dir/build.make CMakeFiles/test_mac_conversion.dir/test_mac_conversion.c.o
.PHONY : test_mac_conversion.c.o

test_mac_conversion.i: test_mac_conversion.c.i
.PHONY : test_mac_conversion.i

# target to preprocess a source file
test_mac_conversion.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mac_conversion.dir/build.make CMakeFiles/test_mac_conversion.dir/test_mac_conversion.c.i
.PHONY : test_mac_conversion.c.i

test_mac_conversion.s: test_mac_conversion.c.s
.PHONY : test_mac_conversion.s

# target to generate assembly for a file
test_mac_conversion.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_mac_conversion.dir/build.make CMakeFiles/test_mac_conversion.dir/test_mac_conversion.c.s
.PHONY : test_mac_conversion.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... factory_read"
	@echo "... factory_update"
	@echo "... factory_write"
	@echo "... test_mac_conversion"
	@echo "... factory_read.o"
	@echo "... factory_read.i"
	@echo "... factory_read.s"
	@echo "... factory_update.o"
	@echo "... factory_update.i"
	@echo "... factory_update.s"
	@echo "... factory_write.o"
	@echo "... factory_write.i"
	@echo "... factory_write.s"
	@echo "... test_mac_conversion.o"
	@echo "... test_mac_conversion.i"
	@echo "... test_mac_conversion.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

