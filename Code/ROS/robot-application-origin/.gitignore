# =============================================================================
# ROS/ROS2 构建和安装目录
# =============================================================================
build/
build_*/
**/build/**
install/
install_*/
**/install/**
devel/
log/
**/log/
**/logs/

# ROS2 特定文件
last_build.log
last_build_*.log
compile_commands.json

# =============================================================================
# 开发工具和编辑器
# =============================================================================
.cache/
.history/
**/.*.swp
**/.*.swo
*~

# 代码导航工具
GPATH
GRTAGS
GTAGS
cscope/
tags
.lh

# =============================================================================
# 同步工具 (Syncthing)
# =============================================================================
.stfolder
.stfolder.removed-*/
.stversions/
.stignore*
.syncthing*
**/*sync-conflict*
stignore
stignore.sh
_gsdata_/

# =============================================================================
# 打包和部署
# =============================================================================
pack/
pack_*/
*.tar.gz
*.deb
*.rpm
*.zip

# =============================================================================
# 编译产物和库文件
# =============================================================================
*.o
*.so
*.a
*.dylib
*.dll
*.exe

# 特定路径的编译产物
deeprobots_application_ros1/src/andlink/scripts/andlink_main/andlinkdaemon
deeprobots_application_ros1/src/andlink/scripts/andlink_main/libAndlinkAdapt.so
deeprobots_application_ros1/src/andlink/scripts/andlink_main/libAndlinkAdapt.a
deeprobots_application_ros1/src/andlink/scripts/andlink_main/src_cpp/tcpclient.o
xiaoli_application_ros2/src/andlink/andlink/andlink_main/adapt/libadapt.a
xiaoli_application_ros2/src/andlink/andlink/andlink_main/src_cpp/tcpclient.o

# =============================================================================
# Python
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
.env

# =============================================================================
# 临时文件和脚本
# =============================================================================
xiaoli01-ROS2
xiaoli02-ROS2
xiaoli03-ROS2
curl1.sh
curl2.sh
curl3.sh
note.md
tmp/
temp/
*.tmp
*.temp

# =============================================================================
# 工具和配置文件
# =============================================================================
# Git Worktree 工具（如果不想提交到仓库）
# worktree-*.sh
# wt.sh
# install-worktree-tools.sh
# test-worktree.sh

# 个人配置和脚本
.cursorrules
check_network.py
fix_python_debug.sh
fix_robdog_control_errors.patch

# =============================================================================
# 系统和IDE文件
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Eclipse
.project
.metadata
.classpath
.settings/

# =============================================================================
# 特定项目文件
# =============================================================================
install_x86_64/
scripts/alg/install_alg.sh
.stversions
./3rdParty/homiCfgTool/bin/libvtkCommonCore-9.4.dll

