#!/bin/bash

# 检查git历史中的大文件脚本
# 帮助识别哪些文件超过GitHub的100MB限制

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🔍 检查git历史中的大文件${NC}"
echo -e "${BLUE}========================${NC}"

# 检查是否在git仓库中
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ 错误：当前目录不是git仓库${NC}"
    exit 1
fi

# 设置大文件阈值（默认100MB）
SIZE_LIMIT=${1:-100}
echo -e "${BLUE}📏 检查大于 ${SIZE_LIMIT}MB 的文件${NC}"

# 检查当前工作区的大文件
echo -e "${BLUE}📁 检查当前工作区的大文件...${NC}"
CURRENT_LARGE_FILES=$(find . -type f -size +${SIZE_LIMIT}M 2>/dev/null | grep -v ".git" || true)

if [ -n "$CURRENT_LARGE_FILES" ]; then
    echo -e "${RED}⚠️  当前工作区发现大文件：${NC}"
    while IFS= read -r file; do
        size=$(du -h "$file" | cut -f1)
        echo -e "  ${RED}$size${NC} - $file"
    done <<< "$CURRENT_LARGE_FILES"
    echo ""
else
    echo -e "${GREEN}✅ 当前工作区没有大于${SIZE_LIMIT}MB的文件${NC}"
fi

# 检查git历史中的大文件
echo -e "${BLUE}📚 检查git历史中的大文件...${NC}"
echo -e "${YELLOW}正在扫描git历史，这可能需要一些时间...${NC}"

# 使用git rev-list和git cat-file检查历史中的大文件
git rev-list --objects --all | \
git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
awk -v size_limit=$((SIZE_LIMIT * 1024 * 1024)) '
    $1 == "blob" && $3 > size_limit {
        size_mb = $3 / 1024 / 1024
        printf "%.1fMB %s %s\n", size_mb, $2, $4
    }
' | sort -nr > /tmp/large_files_in_history.txt

if [ -s /tmp/large_files_in_history.txt ]; then
    echo -e "${RED}⚠️  git历史中发现大文件：${NC}"
    echo -e "${BLUE}大小     SHA1                                     文件路径${NC}"
    echo -e "${BLUE}----     ----                                     --------${NC}"
    head -20 /tmp/large_files_in_history.txt | while IFS= read -r line; do
        echo -e "  ${RED}$line${NC}"
    done
    
    TOTAL_LARGE_FILES=$(wc -l < /tmp/large_files_in_history.txt)
    if [ $TOTAL_LARGE_FILES -gt 20 ]; then
        echo -e "  ${YELLOW}... 还有 $((TOTAL_LARGE_FILES - 20)) 个大文件${NC}"
    fi
    echo ""
    
    echo -e "${YELLOW}💡 建议操作：${NC}"
    echo -e "1. 使用 ${GREEN}./upload_latest_commit_only.sh${NC} 只上传最新代码"
    echo -e "2. 或者使用 git filter-branch 清理历史记录"
    echo -e "3. 或者使用 BFG Repo-Cleaner 工具清理大文件"
    
else
    echo -e "${GREEN}✅ git历史中没有大于${SIZE_LIMIT}MB的文件${NC}"
    echo -e "${GREEN}✅ 可以安全地推送完整历史到GitHub${NC}"
fi

# 检查.gitignore是否正确配置
echo -e "${BLUE}📋 检查.gitignore配置...${NC}"
if [ -f ".gitignore" ]; then
    echo -e "${GREEN}✅ 找到.gitignore文件${NC}"
    
    # 检查是否忽略了常见的大文件类型
    IGNORED_PATTERNS=("*.so" "*.bin" "*.wav" "*.mp4" "*.avi" "*.tar.gz" "build/" "install/")
    for pattern in "${IGNORED_PATTERNS[@]}"; do
        if grep -q "$pattern" .gitignore; then
            echo -e "  ${GREEN}✅${NC} 已忽略: $pattern"
        else
            echo -e "  ${YELLOW}⚠️${NC}  未忽略: $pattern"
        fi
    done
else
    echo -e "${RED}❌ 未找到.gitignore文件${NC}"
fi

# 显示仓库大小信息
echo -e "${BLUE}📊 仓库大小信息：${NC}"
REPO_SIZE=$(du -sh .git | cut -f1)
echo -e "Git仓库大小: ${BLUE}$REPO_SIZE${NC}"

WORKING_SIZE=$(du -sh --exclude=.git . | cut -f1)
echo -e "工作区大小: ${BLUE}$WORKING_SIZE${NC}"

# 清理临时文件
rm -f /tmp/large_files_in_history.txt

echo -e "${BLUE}🎯 总结：${NC}"
if [ -n "$CURRENT_LARGE_FILES" ] || [ -s /tmp/large_files_in_history.txt ]; then
    echo -e "${YELLOW}⚠️  发现大文件，建议使用 ./upload_latest_commit_only.sh${NC}"
else
    echo -e "${GREEN}✅ 没有发现大文件，可以正常推送到GitHub${NC}"
fi
