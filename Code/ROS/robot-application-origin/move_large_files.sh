#!/bin/bash

# 大文件移动脚本 - 排除Git仓库文件
# 用途: 查找并移动大于指定大小的文件，避免影响Git仓库
# 恢复: 使用 ./restore_large_files.sh 恢复移动的文件

# 配置参数
SIZE_LIMIT="50M"  # 文件大小限制
BACKUP_DIR="large_files_backup"  # 备份目录
LOG_FILE="${BACKUP_DIR}/move_log.txt"  # 移动日志文件

# 初始化计数器
moved_count=0
failed_count=0

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 创建日志文件
echo "# 大文件移动日志 - $(date)" > "$LOG_FILE"
echo "# 格式: 时间戳|操作|源文件|目标文件|状态" >> "$LOG_FILE"

echo "🔍 开始查找并移动大于${SIZE_LIMIT}的文件..."
echo "📁 备份目录: $BACKUP_DIR"
echo "🚫 排除目录: 所有子目录下的 .git, node_modules, .cache 等"
echo "=================================="

# 查找大于指定大小的文件并移动（排除常见的系统和开发目录）
find . -type f -size +${SIZE_LIMIT} \
    -not -path "*/.git/*" \
    -not -path "*/node_modules/*" \
    -not -path "*/.cache/*" \
    -not -path "*/.npm/*" \
    -not -path "*/.yarn/*" \
    -not -path "*/build/*" \
    -not -path "*/dist/*" \
    -not -path "*/${BACKUP_DIR}/*" | while read file; do

    # 获取文件大小（人类可读格式）
    file_size=$(du -h "$file" | cut -f1)
    echo "📄 处理文件: $file (大小: $file_size)"

    # 获取文件的相对路径目录
    dir=$(dirname "$file")

    # 在备份目录中创建相应的目录结构
    mkdir -p "$BACKUP_DIR/$dir"

    # 移动文件
    echo "📦 移动: $file -> $BACKUP_DIR/$file"

    # 记录移动操作到日志
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    if mv "$file" "$BACKUP_DIR/$file"; then
        echo "✅ 成功移动: $file"
        echo "$timestamp|MOVE|$file|$BACKUP_DIR/$file|SUCCESS" >> "$LOG_FILE"
        ((moved_count++))
        moved_size=$(echo "$moved_size + $(echo $file_size | sed 's/[^0-9.]//g')" | bc 2>/dev/null || echo "$moved_size")
    else
        echo "❌ 移动失败: $file"
        echo "$timestamp|MOVE|$file|$BACKUP_DIR/$file|FAILED" >> "$LOG_FILE"
        ((failed_count++))
    fi
    echo "---"
done

echo ""
echo "🎉 大文件移动完成！"
echo "=================================="
echo "📁 备份目录: $BACKUP_DIR/"
echo "📊 统计信息:"
echo "   - 成功移动: ${moved_count:-0} 个文件"
echo "   - 移动失败: ${failed_count:-0} 个文件"
echo ""
echo "💡 提示:"
echo "   - 移动日志已保存到: $LOG_FILE"
echo "   - 恢复文件: ./restore_large_files.sh"
echo "   - 列出可恢复文件: ./restore_large_files.sh --list"
echo "   - 强制恢复: ./restore_large_files.sh --force"
echo "   - 所有子目录下的Git仓库文件已被自动排除"
echo "   - 同时排除了 node_modules, .cache 等开发目录"
echo ""
echo "🔄 恢复命令:"
echo "   ./restore_large_files.sh        # 交互式恢复"
echo "   ./restore_large_files.sh --list # 查看可恢复的文件"
