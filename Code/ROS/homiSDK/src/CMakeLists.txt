cmake_minimum_required(VERSION 3.0)

project(homiSDK)

option(HOMI_CONFIG_AI_SPEECH "AI 语音模块" ON)
option(HOMI_CONFIG_ABILITY "ability 模块支持" ON)
option(HOMI_CONFIG_TINGS "物模型支持" ON)
option(HOMI_CONFIG_CLEANROBOT "扫地机支持" ON)

# "编译结果的输出目录"
if ("${HOMI_SDK_OUT_DIR}" STREQUAL "")
set({HOMI_SDK_OUT_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/../out)
endif()

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

if (CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
    set(CMAKE_INSTALL_PREFIX ${HOMI_SDK_OUT_DIR})
endif ()

include(CheckCXXCompilerFlag)
include(CheckCCompilerFlag)
CHECK_CXX_COMPILER_FLAG("-std=c++11" COMPILER_SUPPORTS_CXX11)
CHECK_C_COMPILER_FLAG("-std=gnu99" COMPILER_SUPPORTS_GNU99)
if(COMPILER_SUPPORTS_CXX11)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
else()
     message(FATAL_ERROR "The compiler ${CMAKE_CXX_COMPILER} has no C++11 support. Please use a different C++ compiler.")
endif()
if(COMPILER_SUPPORTS_GNU99)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=gnu99")
else()
     message(FATAL_ERROR "The compiler ${CMAKE_C_COMPILER} has no C99 support. Please use a different C compiler.")
endif()

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O2 -Wall")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O2 -Wall")

set(CMAKE_POLICY_DEFAULT_CMP0077 NEW)

set(CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../cmake;${CMAKE_MODULE_PATH}")

include(homi_print_targets_fun)

include(homi_git_operation)

add_subdirectory(third_party)

enable_testing()

add_subdirectory(homi)

add_subdirectory(test)

add_subdirectory(example)

