homi_git_checkout(${CMAKE_CURRENT_LIST_DIR}/libhv "v1.3.1")

set(WITH_MBEDTLS ON )
set(BUILD_SHARED OFF )
set(BUILD_EXAMPLES OFF)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}  -Wno-sign-compare -Wno-unused-label -Wno-stringop-truncation -Wno-pessimizing-move")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}  -Wno-unused-label -Wno-stringop-truncation -Wno-unused-but-set-variable -Wno-unused-function -Wno-strict-aliasing  -Wno-format-truncation -Wno-unused-result")
add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/libhv)
