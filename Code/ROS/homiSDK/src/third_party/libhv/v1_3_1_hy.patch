From 212277a736e4ed816bc492c9c1f3fea9d3d48ba0 Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Fri, 22 Sep 2023 11:39:04 +0800
Subject: [PATCH 1/8] =?UTF-8?q?=E6=9B=B4=E6=96=B0hconfig.h?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 hconfig.h | 16 +++++++++-------
 1 file changed, 9 insertions(+), 7 deletions(-)

diff --git a/hconfig.h b/hconfig.h
index 15d679c..5a47a9f 100644
--- a/hconfig.h
+++ b/hconfig.h
@@ -10,7 +10,7 @@
 #endif
 
 #ifndef HAVE_STDATOMIC_H
-#define HAVE_STDATOMIC_H 0
+#define HAVE_STDATOMIC_H 1
 #endif
 
 #ifndef HAVE_SYS_TYPES_H
@@ -46,11 +46,11 @@
 #endif
 
 #ifndef HAVE_STRLCPY
-#define HAVE_STRLCPY 1
+#define HAVE_STRLCPY 0
 #endif
 
 #ifndef HAVE_STRLCAT
-#define HAVE_STRLCAT 1
+#define HAVE_STRLCAT 0
 #endif
 
 #ifndef HAVE_CLOCK_GETTIME
@@ -62,15 +62,15 @@
 #endif
 
 #ifndef HAVE_PTHREAD_SPIN_LOCK
-#define HAVE_PTHREAD_SPIN_LOCK 0
+#define HAVE_PTHREAD_SPIN_LOCK 1
 #endif
 
 #ifndef HAVE_PTHREAD_MUTEX_TIMEDLOCK
-#define HAVE_PTHREAD_MUTEX_TIMEDLOCK 0
+#define HAVE_PTHREAD_MUTEX_TIMEDLOCK 1
 #endif
 
 #ifndef HAVE_SEM_TIMEDWAIT
-#define HAVE_SEM_TIMEDWAIT 0
+#define HAVE_SEM_TIMEDWAIT 1
 #endif
 
 #ifndef HAVE_PIPE
@@ -91,9 +91,11 @@
 
 /* #undef WITH_OPENSSL */
 /* #undef WITH_GNUTLS */
-/* #undef WITH_MBEDTLS */
+#define WITH_MBEDTLS   1
+
 /* #undef ENABLE_UDS */
 /* #undef USE_MULTIMAP */
+
 /* #undef WITH_KCP */
 
 #endif // HV_CONFIG_H_
-- 
2.34.1


From f18c7c41e8f76b5bb1a977529a2013a2915087f3 Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Fri, 22 Sep 2023 14:15:25 +0800
Subject: [PATCH 2/8] =?UTF-8?q?=E4=BF=AE=E6=94=B9size=5Ft=E9=97=AE?=
 =?UTF-8?q?=E9=A2=98?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 base/hbase.c            | 4 ++--
 base/hbase.h            | 2 +-
 http/websocket_parser.c | 2 +-
 http/websocket_parser.h | 2 +-
 4 files changed, 5 insertions(+), 5 deletions(-)

diff --git a/base/hbase.c b/base/hbase.c
index 9513002..97825c6 100644
--- a/base/hbase.c
+++ b/base/hbase.c
@@ -373,8 +373,8 @@ bool hv_getboolean(const char* str) {
     }
 }
 
-size_t hv_parse_size(const char* str) {
-    size_t size = 0, n = 0;
+unsigned long long hv_parse_size(const char* str) {
+    unsigned long long size = 0, n = 0;
     const char* p = str;
     char c;
     while ((c = *p) != '\0') {
diff --git a/base/hbase.h b/base/hbase.h
index 2e536a0..6e2af27 100644
--- a/base/hbase.h
+++ b/base/hbase.h
@@ -111,7 +111,7 @@ HV_EXPORT char* hv_random_string(char *buf, int len);
 // 1 y on yes true enable => true
 HV_EXPORT bool   hv_getboolean(const char* str);
 // 1T2G3M4K5B => ?B
-HV_EXPORT size_t hv_parse_size(const char* str);
+HV_EXPORT unsigned long long hv_parse_size(const char* str);
 // 1w2d3h4m5s => ?s
 HV_EXPORT time_t hv_parse_time(const char* str);
 
diff --git a/http/websocket_parser.c b/http/websocket_parser.c
index 96baf20..1b9161c 100644
--- a/http/websocket_parser.c
+++ b/http/websocket_parser.c
@@ -204,7 +204,7 @@ size_t websocket_calc_frame_size(websocket_flags flags, size_t data_len) {
     return size;
 }
 
-size_t websocket_build_frame(char * frame, websocket_flags flags, const char mask[4], const char * data, size_t data_len) {
+size_t websocket_build_frame(char * frame, websocket_flags flags, const char mask[4], const char * data, unsigned long long data_len) {
     size_t body_offset = 0;
     frame[0] = 0;
     frame[1] = 0;
diff --git a/http/websocket_parser.h b/http/websocket_parser.h
index e022125..6efdc9d 100644
--- a/http/websocket_parser.h
+++ b/http/websocket_parser.h
@@ -87,7 +87,7 @@ uint8_t websocket_decode(char * dst, const char * src, size_t len, const char ma
 size_t websocket_calc_frame_size(websocket_flags flags, size_t data_len);
 
 // Create string representation of frame
-size_t websocket_build_frame(char * frame, websocket_flags flags, const char mask[4], const char * data, size_t data_len);
+size_t websocket_build_frame(char * frame, websocket_flags flags, const char mask[4], const char * data, unsigned long long data_len);
 
 #define websocket_parser_get_opcode(p) (p->flags & WS_OP_MASK)
 #define websocket_parser_has_mask(p) (p->flags & WS_HAS_MASK)
-- 
2.34.1


From 17233632da2d41e57be96ba1d46f97577a0007f4 Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Wed, 18 Oct 2023 20:11:53 +0800
Subject: [PATCH 3/8] =?UTF-8?q?=E6=B7=BB=E5=8A=A0hlog=E6=A3=80=E6=9F=A5?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 base/hlog.h | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/base/hlog.h b/base/hlog.h
index d49fa9d..4b83e41 100644
--- a/base/hlog.h
+++ b/base/hlog.h
@@ -105,7 +105,7 @@ HV_EXPORT void logger_set_level_by_str(logger_t* logger, const char* level);
 HV_EXPORT void logger_set_format(logger_t* logger, const char* format);
 HV_EXPORT void logger_set_max_bufsize(logger_t* logger, unsigned int bufsize);
 HV_EXPORT void logger_enable_color(logger_t* logger, int on);
-HV_EXPORT int  logger_print(logger_t* logger, int level, const char* fmt, ...);
+HV_EXPORT int  logger_print(logger_t* logger, int level, const char* fmt, ...) __attribute__((format(printf,3,4)));;
 
 // below for file logger
 HV_EXPORT void logger_set_file(logger_t* logger, const char* filepath);
-- 
2.34.1


From 4af818446d91f8377bb981e39d17acaafeae84ec Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Wed, 18 Oct 2023 20:21:38 +0800
Subject: [PATCH 4/8] =?UTF-8?q?=E4=BF=AE=E6=94=B9=E7=BC=96=E8=AF=91?=
 =?UTF-8?q?=E8=AD=A6=E5=91=8Awsdef.h?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 http/wsdef.h | 5 +++--
 1 file changed, 3 insertions(+), 2 deletions(-)

diff --git a/http/wsdef.h b/http/wsdef.h
index 88c8af8..6859015 100644
--- a/http/wsdef.h
+++ b/http/wsdef.h
@@ -2,7 +2,7 @@
 #define HV_WS_DEF_H_
 
 #include "hexport.h"
-
+#include <memory.h>
 #include <stdbool.h>
 #include <stdlib.h> // import rand
 
@@ -64,7 +64,8 @@ HV_INLINE int ws_client_build_frame(
     enum ws_opcode opcode DEFAULT(WS_OPCODE_TEXT),
     bool fin DEFAULT(true)) {
     char mask[4];
-    *(int*)mask = rand();
+    int randTmp = rand();
+    memcpy(mask,&randTmp,4);
     return ws_build_frame(out, data, data_len, mask, true, opcode, fin);
 }
 
-- 
2.34.1


From 95e13ce766963b8a68769b64d370f00762281b12 Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Thu, 19 Oct 2023 10:41:12 +0800
Subject: [PATCH 5/8] =?UTF-8?q?=E4=BF=AE=E5=A4=8D=E7=BC=96=E8=AF=91?=
 =?UTF-8?q?=E5=99=A8=E5=91=8A=E8=AD=A6?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 event/hloop.c         | 2 +-
 event/nio.c           | 2 +-
 http/http_content.cpp | 2 +-
 3 files changed, 3 insertions(+), 3 deletions(-)

diff --git a/event/hloop.c b/event/hloop.c
index 2309bcf..80cd7b7 100644
--- a/event/hloop.c
+++ b/event/hloop.c
@@ -192,7 +192,7 @@ process_timers:
 static void hloop_stat_timer_cb(htimer_t* timer) {
     hloop_t* loop = timer->loop;
     // hlog_set_level(LOG_LEVEL_DEBUG);
-    hlogd("[loop] pid=%ld tid=%ld uptime=%lluus cnt=%llu nactives=%u nios=%u ntimers=%u nidles=%u",
+    hlogd("[loop] pid=%ld tid=%ld uptime=%luus cnt=%lu nactives=%u nios=%u ntimers=%u nidles=%u",
         loop->pid, loop->tid, loop->cur_hrtime - loop->start_hrtime, loop->loop_cnt,
         loop->nactives, loop->nios, loop->ntimers, loop->nidles);
 }
diff --git a/event/nio.c b/event/nio.c
index b034252..c9f1891 100644
--- a/event/nio.c
+++ b/event/nio.c
@@ -533,7 +533,7 @@ enqueue:
         write_queue_push_back(&io->write_queue, &remain);
         io->write_bufsize += remain.len;
         if (io->write_bufsize > WRITE_BUFSIZE_HIGH_WATER) {
-            hlogw("write len=%d enqueue %u, bufsize=%u over high water %u",
+            hlogw("write len=%ld enqueue %u, bufsize=%u over high water %u",
                 len, (unsigned int)(remain.len - remain.offset),
                 (unsigned int)io->write_bufsize,
                 (unsigned int)WRITE_BUFSIZE_HIGH_WATER);
diff --git a/http/http_content.cpp b/http/http_content.cpp
index 65a2888..a31170d 100644
--- a/http/http_content.cpp
+++ b/http/http_content.cpp
@@ -241,7 +241,7 @@ int parse_json(const char* str, hv::Json& json, std::string& errmsg) {
     try {
         json = nlohmann::json::parse(str);
     }
-    catch(nlohmann::detail::exception e) {
+    catch(nlohmann::detail::exception &e) {
         errmsg = e.what();
         return -1;
     }
-- 
2.34.1


From d3c1080bd2a3a0bef12fdcf5bd4f12c501a19b92 Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Thu, 19 Oct 2023 11:44:25 +0800
Subject: [PATCH 6/8] =?UTF-8?q?=E4=BF=AE=E6=94=B9log.c=EF=BC=8C=E5=A2=9E?=
 =?UTF-8?q?=E5=8A=A0=E7=BA=BF=E7=A8=8B=E5=8F=B7=E6=89=93=E5=8D=B0=E6=A8=A1?=
 =?UTF-8?q?=E5=BC=8F?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 base/hlog.c | 13 ++++++++++++-
 1 file changed, 12 insertions(+), 1 deletion(-)

diff --git a/base/hlog.c b/base/hlog.c
index 64e1dca..06427ec 100644
--- a/base/hlog.c
+++ b/base/hlog.c
@@ -1,5 +1,5 @@
 #include "hlog.h"
-
+#include "hthread.h"
 #include <stdio.h>
 #include <stdlib.h>
 #include <string.h>
@@ -386,6 +386,17 @@ int logger_print(logger_t* logger, int level, const char* fmt, ...) {
                         buf[len++] = plevel[i];
                     }
                     break;
+                case 't':
+                    {
+                        long tid = hv_gettid();
+                        int ret=0; 
+                        if((ret=snprintf(buf + len, bufsize - len,"%ld",tid))<0)
+                        {
+                            ret=0;
+                        }
+                        len+=ret;
+                    }
+                    break;
                 case 's':
                 {
                     va_list ap;
-- 
2.34.1


From 6e869d250d1636dd01212fbc56b55fe8ff9d6679 Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Thu, 19 Oct 2023 16:20:23 +0800
Subject: [PATCH 7/8] =?UTF-8?q?=E4=BF=AE=E6=94=B9=E8=AD=A6=E5=91=8A?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 event/hloop.c             | 2 +-
 event/nio.c               | 2 +-
 http/WebSocketChannel.cpp | 5 +++--
 3 files changed, 5 insertions(+), 4 deletions(-)

diff --git a/event/hloop.c b/event/hloop.c
index 80cd7b7..2309bcf 100644
--- a/event/hloop.c
+++ b/event/hloop.c
@@ -192,7 +192,7 @@ process_timers:
 static void hloop_stat_timer_cb(htimer_t* timer) {
     hloop_t* loop = timer->loop;
     // hlog_set_level(LOG_LEVEL_DEBUG);
-    hlogd("[loop] pid=%ld tid=%ld uptime=%luus cnt=%lu nactives=%u nios=%u ntimers=%u nidles=%u",
+    hlogd("[loop] pid=%ld tid=%ld uptime=%lluus cnt=%llu nactives=%u nios=%u ntimers=%u nidles=%u",
         loop->pid, loop->tid, loop->cur_hrtime - loop->start_hrtime, loop->loop_cnt,
         loop->nactives, loop->nios, loop->ntimers, loop->nidles);
 }
diff --git a/event/nio.c b/event/nio.c
index c9f1891..e0688d6 100644
--- a/event/nio.c
+++ b/event/nio.c
@@ -533,7 +533,7 @@ enqueue:
         write_queue_push_back(&io->write_queue, &remain);
         io->write_bufsize += remain.len;
         if (io->write_bufsize > WRITE_BUFSIZE_HIGH_WATER) {
-            hlogw("write len=%ld enqueue %u, bufsize=%u over high water %u",
+            hlogw("write len=%u enqueue %u, bufsize=%u over high water %u",
                 len, (unsigned int)(remain.len - remain.offset),
                 (unsigned int)io->write_bufsize,
                 (unsigned int)WRITE_BUFSIZE_HIGH_WATER);
diff --git a/http/WebSocketChannel.cpp b/http/WebSocketChannel.cpp
index da233db..bf87dcd 100644
--- a/http/WebSocketChannel.cpp
+++ b/http/WebSocketChannel.cpp
@@ -1,5 +1,5 @@
 #include "WebSocketChannel.h"
-
+#include <memory.h>
 namespace hv {
 
 int WebSocketChannel::send(const char* buf, int len, enum ws_opcode opcode /* = WS_OPCODE_BINARY */, bool fin /* = true */) {
@@ -67,7 +67,8 @@ int WebSocketChannel::sendFrame(const char* buf, int len, enum ws_opcode opcode
     bool has_mask = false;
     char mask[4] = {0};
     if (type == WS_CLIENT) {
-        *(int*)mask = rand();
+        int randTmp = rand();
+        memcpy(mask,&randTmp,4);
         has_mask = true;
     }
     int frame_size = ws_calc_frame_size(len, has_mask);
-- 
2.34.1


From 3752f0263adbffde39e60df747618a9953989950 Mon Sep 17 00:00:00 2001
From: zrj <<EMAIL>>
Date: Thu, 19 Oct 2023 16:32:15 +0800
Subject: [PATCH 8/8] =?UTF-8?q?=E4=BF=AE=E6=94=B9=E8=AD=A6=E5=91=8A?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

---
 event/hloop.c | 2 +-
 event/nio.c   | 2 +-
 2 files changed, 2 insertions(+), 2 deletions(-)

diff --git a/event/hloop.c b/event/hloop.c
index 2309bcf..063f296 100644
--- a/event/hloop.c
+++ b/event/hloop.c
@@ -193,7 +193,7 @@ static void hloop_stat_timer_cb(htimer_t* timer) {
     hloop_t* loop = timer->loop;
     // hlog_set_level(LOG_LEVEL_DEBUG);
     hlogd("[loop] pid=%ld tid=%ld uptime=%lluus cnt=%llu nactives=%u nios=%u ntimers=%u nidles=%u",
-        loop->pid, loop->tid, loop->cur_hrtime - loop->start_hrtime, loop->loop_cnt,
+        loop->pid, loop->tid, (long long unsigned int)(loop->cur_hrtime - loop->start_hrtime), (long long unsigned int)loop->loop_cnt,
         loop->nactives, loop->nios, loop->ntimers, loop->nidles);
 }
 
diff --git a/event/nio.c b/event/nio.c
index e0688d6..f7ad534 100644
--- a/event/nio.c
+++ b/event/nio.c
@@ -534,7 +534,7 @@ enqueue:
         io->write_bufsize += remain.len;
         if (io->write_bufsize > WRITE_BUFSIZE_HIGH_WATER) {
             hlogw("write len=%u enqueue %u, bufsize=%u over high water %u",
-                len, (unsigned int)(remain.len - remain.offset),
+                (unsigned int)len, (unsigned int)(remain.len - remain.offset),
                 (unsigned int)io->write_bufsize,
                 (unsigned int)WRITE_BUFSIZE_HIGH_WATER);
         }
-- 
2.34.1

