project(homi_example)

if(HOMI_CONFIG_AI_SPEECH)
add_executable(SpeechDemo SpeechDemo.cpp)
target_link_libraries(SpeechDemo 
    PRIVATE 
    homi
    hv_static
    nlohmann_json
    )
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/SpeechDemo.cpp DESTINATION  example )
endif(HOMI_CONFIG_AI_SPEECH)

if(HOMI_CONFIG_CLEANROBOT)
add_executable(cleanrobot cleanrobot.cpp miniz.c)
target_link_libraries(cleanrobot 
    PRIVATE 
    homi
    )
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/cleanrobot.cpp DESTINATION  example )
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/json.hpp DESTINATION  example )
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/DataModel.h DESTINATION  example )
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/EventLoop.h DESTINATION  example )
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/miniz.c DESTINATION  example )
install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/miniz.h DESTINATION  example )
endif(HOMI_CONFIG_CLEANROBOT)

add_executable(mytest mytest.cpp)
target_link_libraries(mytest 
    PRIVATE 
    gmock
    homi
    hv_static
    nlohmann_json
    )

