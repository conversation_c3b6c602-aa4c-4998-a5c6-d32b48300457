#ifndef __SRC_EXAMPLE_DATAMODEL_H_
#define __SRC_EXAMPLE_DATAMODEL_H_
#include <sstream>
#include <cmath>
#include <cstring>
#include <random>
#include <memory>
#include <vector>
#include <fstream>
#include <map>
#include "json.hpp"
#define PI  3.1415926
namespace homi::example
{
    struct position
    {
        unsigned int x;
        unsigned int y;
        std::string toString() const
        {
            return "("+std::to_string(x)+","+std::to_string(y)+")";
        }
    };
    template <typename T>
    class Matrix
    {
    public:
        using valueType = T;
        using iterator = typename std::vector<std::vector<valueType>>::iterator;
        using constIterator = typename std::vector<std::vector<valueType>>::const_iterator;
        Matrix &setOrigin(const position &origin)
        {
            _origin = origin;
            return *this;
        }
        position getOrigin(void) const
        {
            return _origin;
        }
        Matrix &resize(std::size_t rows,std::size_t cols,const valueType &val)
        {
            if(rows>getRows())
            {
                for(auto & row:_data)
                {
                    row.resize(cols,val);
                }
                _data.resize(rows,std::vector<valueType>(cols,val));
            }
            else
            {
                _data.resize(rows,std::vector<valueType>(cols,val));
                for(auto & row:_data)
                {
                    row.resize(cols,val);
                }
            }
            return *this;
        }
        std::string toString() const
        {
            std::string s = _origin.toString() + " " + std::to_string(getRows()) + " " + std::to_string(getCols()) + "\n";
            s += "{";
            if(_data.empty())
            {
                s += "}";
            }
            else
            {
                for(auto &row:_data)
                {
                    s+="\n\t{ ";
                    for(auto &val:row)
                    {
                        s+=std::to_string(val)+" ";
                    }
                    s+="}";
                }
                s += "\n}";
            }
            return s;
        }
        std::size_t getRows() const {
            return _data.size();
        }

        std::size_t getCols() const {
            if (_data.empty()) {
                return 0;
            }
            return _data[0].size();
        }
        Matrix getArea(const position &pos1,const position &pos2) const
        {
            Matrix res;
            if(getCols()==0) return res;
            position start ;
            start.x = pos2.x > pos1.x?pos1.x:pos2.x;
            start.y = pos2.y > pos1.y?pos1.y:pos2.y;
            if(start.x>=getCols()||start.y>=getRows()) return res;
            position end;
            end.x = pos2.x < pos1.x?pos1.x:pos2.x;
            end.y = pos2.y < pos1.y?pos1.y:pos2.y;
            end.x = (end.x) > (getCols()-1)?(getCols()-1):(end.x); 
            end.y = (end.y) > (getRows()-1)?(getRows()-1):(end.y); 
            auto first = _data.begin()+start.y;
            auto last = _data.begin() + end.y;
            for(auto iter=first;iter<=last;iter++)
            {
                auto colFirst = (*iter).begin()+start.x;
                auto colLast = (*iter).begin()+end.x+1;
                res._data.emplace_back(colFirst,colLast);
            }
            res._origin = start;
            return res;
        }
        Matrix &setArea(const Matrix &mx)
        {
            if(getCols()==0||mx.getCols()==0) return *this;
            position start =mx._origin;
            if(start.x>=getCols()||start.y>=getRows()) return *this;
            position end;
            end.x = (mx.getCols()-1 + start.x) > (getCols()-1)?(getCols()-1):(mx.getCols()-1 + start.x); 
            end.y = (mx.getRows()-1 + start.y) > (getRows()-1)?(getRows()-1):(mx.getRows()-1 + start.y); 
            auto first = _data.begin()+start.y;
            auto mxIter = mx._data.begin();
            auto last = _data.begin() + end.y;
            for(auto iter=first;iter<=last;iter++,mxIter++)
            {
                auto colFirst = (*iter).begin()+start.x;
                auto colLast = (*iter).begin()+end.x;
                auto mxColIter = (*mxIter).begin();
                for(auto colIter=colFirst;colIter<=colLast;colIter++,mxColIter++)
                {
                    *colIter = *mxColIter;
                }
            }
            return *this;
        }

        Matrix &fill(std::size_t rows,std::size_t cols,const valueType *pbuf)
        {
            _data.resize(rows);
            auto last = _data.begin() + rows;
            unsigned int index=0;
            for(auto iter=_data.begin();iter<last;iter++,index+=cols)
            {
                (*iter).resize(cols);
                (*iter).assign((pbuf+index),(pbuf+index+cols));
            }
            return *this;
        }
        std::vector<valueType>& operator[](std::size_t n)
        {
            return _data[n];
        }
        const std::vector<valueType>& operator[](std::size_t n) const
        {
            return _data[n];
        }
        iterator begin() 
        { 
            return _data.begin(); 
        }
        constIterator begin() const
        { 
            return _data.begin(); 
        }
        iterator end() 
        { 
            return _data.end(); 
        }
        constIterator end() const
        { 
            return _data.end(); 
        }
    private:
        std::vector<std::vector<valueType>> _data;
        position _origin;
    };
    class IArea
    {
    public:
        virtual bool inArea(const position &pos) const = 0;

    };
    class Rectangle : public IArea
    {
    public:
        Rectangle(const position &pos1,const position &pos2)
        {
            _top.x = pos2.x > pos1.x?pos1.x:pos2.x;
            _top.y = pos2.y > pos1.y?pos1.y:pos2.y;
            _bottom.x = pos2.x < pos1.x?pos1.x:pos2.x;
            _bottom.y = pos2.y < pos1.y?pos1.y:pos2.y;            
        }
        virtual bool inArea(const position &pos) const override
        {
            if((pos.x>=_top.x && pos.x <=_bottom.x)&&(pos.y>=_top.y && pos.y<=_bottom.y))
            {
                return true;
            }
            else
            {
                return false;
            } 
        }
        position getTop() const
        {
            return _top;
        }
        position getBottom() const
        {
            return _bottom;
        }
    private:
        position _top;
        position _bottom;
    };
    struct ForbiddenZone:public IArea
    {
        std::vector<Rectangle> cleanForbidden;
        std::vector<Rectangle> waterForbidden;
        std::vector<Rectangle> wallForbidden;
        virtual bool inArea(const position &pos) const override
        {
            for(auto &e:cleanForbidden)
            {
                if(e.inArea(pos)) return true;
            }
            for(auto &e:waterForbidden)
            {
                if(e.inArea(pos)) return true;
            }  
            return false;  
        }
    };

    class PGM : private Matrix<unsigned char> 
    {
    public:
        std::string serialization() const
        {
            std::stringstream ss;
            ss<< _version <<"\n" << getCols() << " " << getRows() << "\n" << _maxPixelValue << "\n";
            for(auto &e:*this)
            {
                ss.write(reinterpret_cast<const char*>(e.data()),e.size());
            }
            return std::move(ss.str());
        }
        PGM &init(std::size_t width,std::size_t height,const unsigned char vaule)
        {
            resize(height,width,vaule).setOrigin({0,0});
            _version = "P5";
            _maxPixelValue = 255;
            return *this;
        }
        PGM & deserialization(const std::string &filePath)
        {
            std::ifstream file(filePath, std::ios::binary);
            if (!file.is_open()) {
                throw std::runtime_error("Failed to open file");
            }
            unsigned int width,height;
            file >> _version >> width >> height >> _maxPixelValue;
            // 跳过换行符
            file.ignore(1, '\n');
            char pixelValue;
            std::vector<unsigned char> data;
            std::size_t size=0;
            while (file.get(pixelValue)) {
                size++;
                data.push_back(static_cast<unsigned char>(pixelValue));
            }
            file.close();
            if(size<width*height)
            {
                throw std::runtime_error("pgm format err,size="+std::to_string(size));
            }
            fill(height,width,data.data()).setOrigin({0,0});
            return *this;
        }
        std::size_t getWidth() const
        {
            return getCols();
        }
        std::size_t getHeight() const
        {
            return getRows();
        }
        using Matrix::operator[];
        using Matrix::setArea;
        using Matrix::getArea;
    private:
        std::string _version;
        unsigned int _maxPixelValue;
    };
    class Map
    {
    public:
        Map &init(const std::string &filePath)
        {
            unsigned int width = 240;
            unsigned int height =320;
            try
            {
                _pgmBackend.deserialization(filePath);
                width = _pgmBackend.getWidth();
                height = _pgmBackend.getHeight();
            }
            catch(const std::exception& e)
            {
                _pgmBackend.init(width,height,0xff);
            }
            _pgm.init(width,height,0x99);
            auto area = _pgmBackend.getArea({0,0,},{width,height});
            _pgm.setArea(area);
            return *this;
        }
        Map &init(std::size_t width,std::size_t height)
        {
            _pgm.init(width,height,0x99);
            _pgmBackend.init(width,height,0xff);
            return *this;
        }
        Map &set(const position &pos)
        {
            const unsigned int step = 10;
            position pos1 ={ pos.x>step?pos.x-step:0,pos.y>step?pos.y-step:0 };
            position pos2 ={ pos.x+step,pos.y+step };
            auto area = _pgmBackend.getArea(pos1,pos2);
            _pgm.setArea(area);
            return *this;
        }
        bool isVaildPos(const position &pos) const
        {
            if(pos.x>=_pgm.getWidth()||pos.y>=_pgm.getHeight() || (_pgmBackend[pos.y][pos.x] <0xf0)) 
            {
                return false;
            }
            else return true;
        }
        bool inRoom(const position &pos,const std::vector<unsigned char> &rooms) const
        {
            auto value = _pgmBackend[pos.y][pos.x];
            for(auto &e:rooms)
            {
                if(e==value) return true;
            }
            return false;
        }
        bool isVaildRoom(const unsigned char roomNo)
        {
            for(unsigned int i=0;i<_pgm.getHeight();i++)
            {
                for(unsigned int j=0;j<_pgm.getWidth();j++)
                {
                    auto v = _pgm[i][j];
                    if(v==roomNo)
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        bool isVaildArea(const Rectangle &rect)
        {
            for(unsigned int i=0;i<_pgm.getHeight();i++)
            {
                for(unsigned int j=0;j<_pgm.getWidth();j++)
                {
                    position pos = {j,i};
                    if(rect.inArea(pos))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        std::string serialization() const
        {
            return _pgm.serialization();
        }
        std::size_t getWidth() const
        {
            return _pgm.getWidth();
        }
        std::size_t getHeight() const
        {
            return _pgm.getHeight();
        }
    private:
        PGM _pgm;
        PGM _pgmBackend;
    };
    class WifiHeat
    {
    public:
        WifiHeat &init(std::size_t width,std::size_t height)
        {
            _pgm.init(width,height,0xff);
            return *this;
        }   
        WifiHeat &set(const position &pos,const unsigned char value) 
        {
            auto modif = value;
            if(modif<20) modif =20;
            if(modif>100) modif =100;
            _pgm[pos.x][pos.y] = modif;
            return *this;
        }   
        void setRand(const position &pos)
        {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<> dis(20,100);
            unsigned char value = (int)std::round(dis(gen));
            _pgm[pos.x][pos.y] = value;
        }
        std::string serialization() const
        {
            return _pgm.serialization();
        }
    private:
        PGM _pgm;
    };
    class RobotMove
    {
    public:
        RobotMove()
        {
            _x = 0;
            _y = 0;
            _tha = 0;
            _xSpeed = std::cos(_tha);
            _ySpeed = std::sin(_tha);
        }
        position setCurrent(const position &pos)
        {
            _x = pos.x;
            _y = pos.y;
            return pos;
        }
        position getCurrent()
        {
            return float2position(_x,_y);
        }
        position getNext(float speed=25)
        {
            _x += _xSpeed*speed;
            _y += _ySpeed*speed;

            return float2position(_x,_y);
        }
        position tryNext(float speed=25)
        {
            auto x =_x + _xSpeed*speed;
            auto y =_y + _ySpeed*speed;

            return float2position(x,y);
        }
        void turn(float k=1,float tha=(PI/180))
        {
            _tha += k*tha;
            _xSpeed = std::cos(_tha);
            _ySpeed = std::sin(_tha);
        
        }
        void turnRand()
        {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<> dis(-1,1);
            _xSpeed = dis(gen);
            _ySpeed = dis(gen);
        }
        std::string direction()
        {
            std::stringstream ss;
            ss<<"("<< (_tha*180)/PI <<")";
            return std::move(ss.str());        
        }
        position moveNormal(const Map &map,const ForbiddenZone &forbiddenZone)
        {
            auto pos = tryNext();
            int count = 10;
            while(!map.isVaildPos(pos)||forbiddenZone.inArea(pos))
            {
                if(--count<0)
                {
                    setCurrent({100,100});
                    return getCurrent();
                }
                turnRand();
                pos = tryNext();
            }
            return getNext();
        }
        position moveInRoom(const Map &map,const ForbiddenZone &forbiddenZone,const std::vector<unsigned char> &rooms)
        {
            if(!map.inRoom(getCurrent(),rooms)) return moveNormal(map,forbiddenZone);
            auto pos = tryNext();
            while(!map.inRoom(pos,rooms)||!map.isVaildPos(pos)||forbiddenZone.inArea(pos))
            {
                turnRand();
                pos = tryNext();
            }
            return getNext();            
        }
        position moveInArea(const Map &map,const ForbiddenZone &forbiddenZone,const std::vector<Rectangle> &areas)
        {
            auto isInArea = [](const position &pos,const std::vector<Rectangle> &areas){
                for(auto &e:areas)
                {
                    if(e.inArea(pos))
                        return true;
                }
                return false;
            };
            if(!isInArea(getCurrent(),areas)) return moveNormal(map,forbiddenZone);
            auto pos = tryNext();
            while(!isInArea(pos,areas)||!map.isVaildPos(pos)||forbiddenZone.inArea(pos))
            {
                turnRand();
                pos = tryNext();
            }
            return getNext();            
        }
    private:
        position float2position(float x,float y)
        {
            int xx = std::round(x);
            int yy = std::round(y);
            return {xx<0?0:(unsigned int)xx,yy<0?0:(unsigned int)yy };
        }
        float _xSpeed;
        float _ySpeed;
        float _x;
        float _y;
        float _tha;
    };
    struct Quiet 
    {
        bool enable;
        unsigned int time1;
        unsigned int time2;
    };
    void to_json(nlohmann::json& json, const Quiet& myStruct);
    void from_json(const nlohmann::json& json, Quiet& myStruct);
    
}


#endif  // __SRC_EXAMPLE_DATAMODEL_H_
