#include <string>
#include <memory>
#include <chrono>
#include <thread>
#include "gtest/gtest.h"
#include "gmock/gmock.h"
#include "hv/hlog.h"
#include "hv/EventLoopThread.h"
#include "hv/AsyncHttpClient.h"
#include "nlohmann/json.hpp"

class HttpClient
{
public:
    HttpClient(){
        hlogd("in HttpClient()");
        _loopThreadPtr = std::make_shared<hv::EventLoopThread>();
        _loopThreadPtr->start();
        _clientPtr = std::make_shared<hv::AsyncHttpClient>(_loopThreadPtr->loop());
    }
    ~HttpClient(){
        hlogd("in ~HttpClient() %ld %ld",_loopThreadPtr.use_count(),_clientPtr.use_count());
        _clientPtr.reset();
        std::this_thread::sleep_for(std::chrono::seconds(1));
        _loopThreadPtr.reset();
    }
    void req(const std::string &url,const std::string &deviceId,const std::string &secret)
    {
        HttpRequestPtr req = std::make_shared<HttpRequest>();
        nlohmann::json obj = {{"deviceId",deviceId},{"secret",secret},{"extraParams",nlohmann::json::parse("{}")}};
        req->method = HTTP_POST;
        req->url = url;
        req->headers["Content-Type"] = "application/json";
        req->body = obj.dump();
        req->timeout = 5;
        HttpResponseCallback respcallback = [this](const HttpResponsePtr& resp)
        {
            if(resp!=nullptr)
            {
                hlogi("%d %s\r\n", resp->status_code, resp->status_message());
                hlogi("%s\n", resp->body.c_str());
                try
                {
                    auto obj = nlohmann::json::parse(resp->body);
                    auto url = obj["data"]["url"];
                    if(url!=nullptr)
                    {
                        hlogd("resp url = %s",url.get<std::string>().c_str());
                    }
                }
                catch(const std::exception& e)
                {
                    hlogw("%s",e.what());
                }
            }
        };
        _clientPtr->send(req,respcallback);
    }
private:
    std::shared_ptr<hv::EventLoopThread> _loopThreadPtr;
    std::shared_ptr<hv::AsyncHttpClient> _clientPtr;
};

int main(int argc, char** argv) 
{
    hlog_set_handler(stdout_logger);
    hlog_set_format("[%t] " DEFAULT_LOG_FORMAT);
    hlog_set_level(LOG_LEVEL_DEBUG);
    hlogi("begin...");
    HttpClient req;
    req.req("http://36.138.107.136:10000/robot/business/api/user/client/device/connect/url","1008611","1008611");


    std::this_thread::sleep_for(std::chrono::seconds(10));
    hlogi("end....");
    return 0;
}