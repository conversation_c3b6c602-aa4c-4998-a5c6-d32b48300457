#include <stdio.h>
#include <stdlib.h>
#include <getopt.h>
#define OPT_STRING "i:p:"
#define OPT_HELP "help\n"
static struct option long_options[] = {
    {"ip", required_argument, NULL, 'i'},
    {"port", required_argument, NULL, 'p'},
    {"opt", optional_argument, NULL, 'o'},
    {"noarg", no_argument, NULL, 'n'},
    {NULL, 0, NULL, 0},
};
int main(int argc, char *argv[]) {
    int opt;
    while ((opt = getopt_long(argc, argv, OPT_STRING, long_options,
                              NULL)) != -1) {
        switch (opt) {
        case 'i':
            break;
        case 'p':
            break;
        default:
            printf(OPT_HELP);
            exit(EXIT_FAILURE);
        }
    }
    return 0;
}