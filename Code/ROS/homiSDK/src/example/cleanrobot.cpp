#include <iostream>
#include <fstream>
#include <stdio.h>
#include <thread>
#include <chrono>
#include <string>
#include <memory>
#include <mutex>
#include <getopt.h>
#include <sstream>
#include <random>
#include <cmath>
#include <fstream>
#include "homi/OpenAPI.h"
#include "homi/CleanRobotApp.h"
#include "json.hpp"
#include "miniz.h"
#include "EventLoop.h"
#include "StateMachine.h"
#include "DataModel.h"


#define TMP_COMPRESS_FILE_PATH   "./mycompress.zip"
namespace homi::example
{
    void to_json(nlohmann::json& json, const Quiet& myStruct)
    {
        json = nlohmann::json({
                    {"enable", myStruct.enable},
                    {"time", {myStruct.time1,myStruct.time2}}
                });
    }
    void from_json(const nlohmann::json& json, Quiet& myStruct)
    {
        try
        {
            myStruct.enable = false;
            myStruct.time1 = 0;
            myStruct.time2= 0;
            auto iter = json.find("enable");
            if(iter!=json.end()) iter->get_to(myStruct.enable);
            iter = json.find("time");
            if(iter!=json.end())
            {
                myStruct.time1 = iter->at(0);
                myStruct.time2 = iter->at(1);
            }
        }
        catch(const std::exception& e)
        {
            loggerW("Quiet 格式解析错误 ,%s",e.what());
        }        
    }
    bool isCurrentTimeInRange(unsigned int start, unsigned int end) {
        auto now = std::chrono::system_clock::now();
        auto now_c = std::chrono::system_clock::to_time_t(now);
        auto tm_now = *std::localtime(&now_c);

        // 计算当天的00:00:00的时间点
        tm_now.tm_hour = 0;
        tm_now.tm_min = 0;
        tm_now.tm_sec = 0;
        auto midnight = std::chrono::system_clock::from_time_t(std::mktime(&tm_now));

        // 计算当前时间相对于当天00:00:00的秒数
        auto seconds_since_midnight = std::chrono::duration_cast<std::chrono::seconds>(now - midnight).count();
        // 如果end < start，表示end代表第二天的秒数，需要加上一天的秒数
        if (end < start) {
            end += 86400; // 一天的秒数
        }
        std::cout <<"seconds_since_midnight= " <<seconds_since_midnight << " start=" <<start << " end="<<end <<std::endl; 
        return (seconds_since_midnight >= start && seconds_since_midnight <= end);
    }
    bool isQuiet(const Quiet &quiet)
    {
        if(quiet.enable)
        {
            return isCurrentTimeInRange(quiet.time1,quiet.time2);
        }
        else
        {
            return false;
        }
    }
    class Cleanrobot
    {
    public:
        enum State
        {
            WAITING =0,
            CHARGING=1,
            CLEANING=2,
            CLEAN_PAUSE=3,
            BACK_CHARGING=4,
            BACK_CHARGE_STOP=5,
            DUST_COLLECTING=11,
            MOP_WASHING=12
        };
        enum SubCmd
        {
            sc_NO = 0,
            sc_AREA_CLEAN,
            sc_ROOM_CLEAN
        };
        Cleanrobot(const std::string &mapfilePath):_stateMachine(WAITING)
        {
            _power = 60;
            _quiet.enable = false;
            position pos = {100,100};
            _subCmd = sc_NO;
            _rm.setCurrent(pos);
            _map.init(mapfilePath);
            auto width = _map.getWidth();
            auto height= _map.getHeight();
            _wifiHeat.init(width,height);
            _info["mapInfo"]["w"] = width;
            _info["mapInfo"]["h"] = height;
            _info["chargingStationLocation"]["x"] = 100;
            _info["chargingStationLocation"]["y"] = 100;
            _info["chargingStationLocation"]["w"] = 20;
            _info["chargingStationLocation"]["h"] = 20;
            _info["robotLocation"]["x"] = pos.x;
            _info["robotLocation"]["y"] = pos.y;
            _info["robotLocation"]["w"] = 20;
            _info["robotLocation"]["h"] = 20;
            registerCallback();
            _stateMachine.AddTransition(State::WAITING,State::WAITING,[this](){});
            _stateMachine.AddTransition(State::CHARGING,State::CHARGING,[this](){});
            _stateMachine.AddTransition(State::CLEANING,State::CLEANING,[this](){});
            _stateMachine.AddTransition(State::CLEAN_PAUSE,State::CLEAN_PAUSE,[this](){});
            _stateMachine.AddTransition(State::BACK_CHARGING,State::BACK_CHARGING,[this](){});
            _stateMachine.AddTransition(State::BACK_CHARGE_STOP,State::BACK_CHARGE_STOP,[this](){});
            _stateMachine.AddTransition(State::DUST_COLLECTING,State::DUST_COLLECTING,[this](){});
            _stateMachine.AddTransition(State::MOP_WASHING,State::MOP_WASHING,[this](){});



            _stateMachine.AddTransition(State::WAITING,State::CLEANING,[this](){ _cleaningArea=0;_cleanCost=0; });
            
            _stateMachine.AddTransition(State::BACK_CHARGING,State::CLEANING,[this](){ _cleaningArea=0;_cleanCost=0; });
            _stateMachine.AddTransition(State::BACK_CHARGE_STOP,State::CLEANING,[this](){ _cleaningArea=0;_cleanCost=0; });
            _stateMachine.AddTransition(State::CHARGING,State::CLEANING,[this](){_cleaningArea=0;_cleanCost=0; });
            _stateMachine.AddTransition(State::CLEAN_PAUSE,State::CLEANING,[this](){});
            _stateMachine.AddTransition(State::CLEANING,State::CLEAN_PAUSE,[this](){});
            _stateMachine.AddTransition(State::CLEAN_PAUSE,State::CLEANING,[this](){});
            _stateMachine.AddTransition(State::CLEAN_PAUSE,State::BACK_CHARGE_STOP,[this](){});
            _stateMachine.AddTransition(State::CLEANING,State::WAITING,[this](){});
            _stateMachine.AddTransition(State::CLEANING,State::BACK_CHARGE_STOP,[this](){});

            _stateMachine.AddTransition(State::WAITING,State::BACK_CHARGING,[this](){_chargeCnt = 0;});
            _stateMachine.AddTransition(State::CLEAN_PAUSE,State::BACK_CHARGING,[this](){_chargeCnt = 0;});
            _stateMachine.AddTransition(State::CLEANING,State::BACK_CHARGING,[this](){_chargeCnt = 0;});
            _stateMachine.AddTransition(State::BACK_CHARGE_STOP,State::BACK_CHARGING,[this](){_chargeCnt = 0;});
            _stateMachine.AddTransition(State::BACK_CHARGING,State::BACK_CHARGE_STOP,[this](){});
            _stateMachine.AddTransition(State::BACK_CHARGING,State::CHARGING,[this](){});
            _stateMachine.AddTransition(State::CHARGING,State::WAITING,[this](){});

            _stateMachine.SetExitStateFunc([this](){
                loggerI("exit state %d",_stateMachine.getState());
            });
            _stateMachine.SetEnterStateFunc([this](){
                loggerI("entry state %d",_stateMachine.getState());
                switch (_stateMachine.getState())
                {
                    case State::WAITING:
                    case State::CHARGING:
                    case State::DUST_COLLECTING:
                    case State::MOP_WASHING:
                        _rm.setCurrent({100,100});
                    break;
                    default:
                    break;
                }
            });
            
        }
        void registerCallback(){
            auto ret =homi::getCleanRobot()->registerPropertiesReadCallback({
                    {"runningMode",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            output = std::to_string(_stateMachine.getState());
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"power",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            output = std::to_string(_power);
                        },std::ref(input),std::ref(output));
                        res.get();
                    }}
                });
            loggerI("registerCallback step1 ret=%d",ret);
            ret = homi::getCleanRobot()->registerPropertiesWriteCallback({
                {"forbiddenZones",[this](const std::string &input,std::string &output){
                    auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                        nlohmann::json j = nlohmann::json::parse(input);
                        try
                        {
                            auto iter = j.find("cleanForbidden");
                            if(iter!=j.end())
                            {
                                _forbiddenZone.cleanForbidden.clear();
                                for(auto &e:*iter)
                                {
                                    position pos1 = {e["x1"],e["y1"] };
                                    position pos2 = {e["x2"],e["y2"] };
                                    Rectangle area(pos1,pos2);
                                    loggerI("add cleanForbidden %s %s",area.getTop().toString().c_str(),area.getBottom().toString().c_str());
                                    _forbiddenZone.cleanForbidden.emplace_back(area);
                                }
                            }
                            iter = j.find("waterForbidden");
                            if(iter!=j.end())
                            {
                                _forbiddenZone.waterForbidden.clear();
                                for(auto &e:*iter)
                                {
                                    position pos1 = {e["x1"],e["y1"] };
                                    position pos2 = {e["x2"],e["y2"] };
                                    Rectangle area(pos1,pos2);
                                    loggerI("add waterForbidden %s %s",area.getTop().toString().c_str(),area.getBottom().toString().c_str());
                                    _forbiddenZone.waterForbidden.emplace_back(area);
                                }
                            }
                            iter = j.find("wallForbidden");
                            if(iter!=j.end())
                            {
                                _forbiddenZone.wallForbidden.clear();
                                for(auto &e:*iter)
                                {
                                    position pos1 = {e["x1"],e["y1"] };
                                    position pos2 = {e["x2"],e["y2"] };
                                    Rectangle area(pos1,pos2);
                                    loggerI("add wallForbidden %s %s",area.getTop().toString().c_str(),area.getBottom().toString().c_str());
                                    _forbiddenZone.wallForbidden.emplace_back(area);
                                }
                            }
                            output = input;
                        }
                        catch(const std::exception& e)
                        {
                            loggerI("%s",e.what());
                        }
                    },std::ref(input),std::ref(output));
                    res.get();
                }},
                {"quiet",[this](const std::string &input,std::string &output){
                    auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                    try{
                        nlohmann::json j = nlohmann::json::parse(input);
                        _quiet = j.get<Quiet>();
                        output = nlohmann::json(_quiet).dump();
                    }
                    catch(const std::exception& e)
                    {
                        loggerI("%s",e.what());
                    }
                    },std::ref(input),std::ref(output));
                    res.get();
                }}
            });
            loggerI("registerCallback step2 ret=%d",ret);
            ret = homi::getCleanRobot()->registerServiceCallback({
                    {"cleanWhole",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::CLEANING))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            try
                            {
                                std::string mapUrl;
                                std::string trajectoryMap;
                                std::string wifiHeatMap;
                                unsigned long long cid;    
                                nlohmann::json j = nlohmann::json::parse(input);  
                                if(j.find("mapUrl")==j.end()||j.find("trajectoryMap")==j.end()||j.find("wifiHeatMap")==j.end()||j.find("cid")==j.end())
                                {
                                    output = R"({"errorCode":1000})";
                                    return ;
                                }
                                mapUrl = j["mapUrl"].get<std::string>();
                                trajectoryMap = j["trajectoryMap"].get<std::string>();
                                wifiHeatMap = j["wifiHeatMap"].get<std::string>();
                                cid = j["cid"].get<unsigned long long>();     
                                _mapUrl = mapUrl;
                                _trajectoryMap = trajectoryMap;
                                _wifiHeatMap = wifiHeatMap;
                                _cid = cid;                          
                            }
                            catch(const std::exception& e)
                            {
                                output = R"({"errorCode":1000})";
                                return ;
                            }
                            nlohmann::json out;
                            _subCmd = sc_NO;
                            _stateMachine.ChangeState(State::CLEANING);
                            out["runingMode"] = _stateMachine.getState();
                            out["cleanRecord"]["cid"] = _cid;
                            out["cleanRecord"]["cleaningArea"] = 0;
                            out["cleanRecord"]["costTime"] = 0;
                            out["errorCode"] = 0;
                            output = out.dump(); 
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"cleanArea",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::CLEANING))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            try
                            {
                                nlohmann::json j = nlohmann::json::parse(input);  
                                if(j.find("mapUrl")==j.end()||j.find("trajectoryMap")==j.end()||j.find("wifiHeatMap")==j.end()||j.find("cid")==j.end())
                                {
                                    output = R"({"errorCode":1000})";
                                    return ;
                                }
                                std::string mapUrl;
                                std::string trajectoryMap;
                                std::string wifiHeatMap;
                                unsigned long long cid;    
                                mapUrl = j["mapUrl"].get<std::string>();
                                trajectoryMap = j["trajectoryMap"].get<std::string>();
                                wifiHeatMap = j["wifiHeatMap"].get<std::string>();
                                cid = j["cid"].get<unsigned long long>();   
                                auto iter = j.find("area");
                                std::vector<Rectangle> areas;
                                if(iter!=j.end())
                                {
                                    for(auto &e:*iter)
                                    {
                                        position pos1 = {e["x1"],e["y1"] };
                                        position pos2 = {e["x2"],e["y2"] };
                                        Rectangle area(pos1,pos2);
                                        if(_map.isVaildArea(area))
                                        {
                                            loggerI("add areas %s %s",area.getTop().toString().c_str(),area.getBottom().toString().c_str());
                                            areas.emplace_back(area);
                                        }
                                    }
                                }
                                if(areas.empty()) 
                                {
                                    loggerI("no vaild areas");
                                    output = R"({"errorCode":1000})";
                                    return ;
                                }
                                _areas = areas;
                                _mapUrl = mapUrl;
                                _trajectoryMap = trajectoryMap;
                                _wifiHeatMap = wifiHeatMap;
                                _cid = cid;                          
                            }
                            catch(const std::exception& e)
                            {
                                output = R"({"errorCode":1000})";
                                return ;
                            }
                            nlohmann::json out;
                            _subCmd = sc_AREA_CLEAN;
                            _stateMachine.ChangeState(State::CLEANING);
                            out["runingMode"] = _stateMachine.getState();
                            out["cleanRecord"]["cid"] = _cid;
                            out["cleanRecord"]["cleaningArea"] = 0;
                            out["cleanRecord"]["costTime"] = 0;
                            out["errorCode"] = 0;
                            output = out.dump(); 
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"cleanRooms",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::CLEANING))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            try
                            {
                                nlohmann::json j = nlohmann::json::parse(input);  
                                if(j.find("mapUrl")==j.end()||j.find("trajectoryMap")==j.end()||j.find("wifiHeatMap")==j.end()||j.find("cid")==j.end())
                                {
                                    output = R"({"errorCode":1000})";
                                    return ;
                                }
                                std::string mapUrl;
                                std::string trajectoryMap;
                                std::string wifiHeatMap;
                                unsigned long long cid;    
                                mapUrl = j["mapUrl"].get<std::string>();
                                trajectoryMap = j["trajectoryMap"].get<std::string>();
                                wifiHeatMap = j["wifiHeatMap"].get<std::string>();
                                cid = j["cid"].get<unsigned long long>();   
                                std::vector<unsigned char> rooms;
                                auto iter = j.find("rooms");
                                if(iter!=j.end())
                                {
                                    for(auto &e:*iter)
                                    {
                                        std::stringstream ss;
                                        auto roomNo = e.get<std::string>();
                                        ss << std::hex << roomNo;
                                        unsigned int hexValue;
                                        ss >> hexValue;
                                        if(_map.isVaildRoom(hexValue))
                                        {
                                            loggerI("add room =%s(%d)",roomNo.c_str(), (unsigned int)hexValue);
                                            rooms.emplace_back(hexValue);
                                        }
                                    }
                                }
                                if(rooms.empty())
                                {
                                    loggerI("no vaild rooms");
                                    output = R"({"errorCode":1000})";
                                    return ;
                                }
                                _rooms = rooms;
                                _mapUrl = mapUrl;
                                _trajectoryMap = trajectoryMap;
                                _wifiHeatMap = wifiHeatMap;
                                _cid = cid;                          
                            }
                            catch(const std::exception& e)
                            {
                                output = R"({"errorCode":1000})";
                                return ;
                            }
                            nlohmann::json out;
                            _subCmd = sc_ROOM_CLEAN;
                            _stateMachine.ChangeState(State::CLEANING);
                            out["runingMode"] = _stateMachine.getState();
                            out["cleanRecord"]["cid"] = _cid;
                            out["cleanRecord"]["cleaningArea"] = 0;
                            out["cleanRecord"]["costTime"] = 0;
                            out["errorCode"] = 0;
                            output = out.dump(); 
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"cleanPause",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::CLEAN_PAUSE))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            _stateMachine.ChangeState(State::CLEAN_PAUSE);
                            std::stringstream ss;
                            ss<<R"({"runningMode":")"<< std::to_string(_stateMachine.getState()) <<R"(","errorCode":0})" ;
                            output = ss.str();
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"cleanResume",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::CLEANING))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            std::stringstream ss;
                            // if(isQuiet(_quiet))
                            // {
                            //     ss<<R"({"runningMode":")"<< std::to_string(_stateMachine.getState())<<R"(","errorCode":1000})" ;
                            // }
                            // else
                            {
                                _stateMachine.ChangeState(State::CLEANING);
                                std::stringstream ss;
                                ss<<R"({"runningMode":")"<< std::to_string(_stateMachine.getState()) <<R"(","errorCode":0})" ;
                            }
                            output = ss.str();
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"backCharge",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::BACK_CHARGING))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            _stateMachine.ChangeState(State::BACK_CHARGING);
                            std::stringstream ss;
                            ss<<R"({"runningMode":")"<< std::to_string(_stateMachine.getState()) <<R"(","errorCode":0})" ;
                            output = ss.str();
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"backChargeStop",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::BACK_CHARGE_STOP))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            _stateMachine.ChangeState(State::BACK_CHARGE_STOP);
                            std::stringstream ss;
                            ss<<R"({"runningMode":")"<< std::to_string(_stateMachine.getState()) <<R"(","errorCode":0})" ;
                            output = ss.str();
                        },std::ref(input),std::ref(output));
                        res.get();
                    }},
                    {"backChargeResume",[this](const std::string &input,std::string &output){
                        auto res =_ep.runInLoop([this](const std::string &input,std::string &output){
                            if(!_stateMachine.TryState(State::BACK_CHARGING))
                            {
                                    output = R"({"errorCode":1000})";
                                    return ;  
                            }
                            _stateMachine.ChangeState(State::BACK_CHARGING);
                            std::stringstream ss;
                            ss<<R"({"runningMode":")"<< std::to_string(_stateMachine.getState()) <<R"(","errorCode":0})" ;
                            output = ss.str();
                        },std::ref(input),std::ref(output));
                        res.get();
                    }}
            });
            loggerI("registerCallback step3 ret=%d",ret);
        }
        void run(void)
        {
            _ep.setTimer([this](int id){
                if(_stateMachine.getState()==State::CLEANING)
                {
                    if(_cleaningArea>100*50&&_cleanCost>70)
                    {
                        _stateMachine.ChangeState(State::WAITING);
                        position pos = {100,100};
                        _rm.setCurrent(pos);
                        _wifiHeat.setRand(pos);
                        _info["robotLocation"]["x"] = pos.x;
                        _info["robotLocation"]["y"] = pos.y;
                        loggerI("update pos x=%d y=%d status=%d _cid=%llu",pos.x,pos.y,(int)_stateMachine.getState(),_cid);                        
                        homi::getCleanRobot()->updateOSS(_trajectoryMap,_info.dump(),"trajectoryMap");

                    }
                    if(isInVaildCleaningPosition())
                    {
                        _cleaningArea+=100;
                        _cleanCost+=1;
                        std::stringstream ss;
                        ss<<R"({"cleaningArea":)"<<_cleaningArea<<R"(,"costTime":)"<<_cleanCost<<R"(,"cid":)"<<_cid<<R"(})" ;
                        homi::getCleanRobot()->sendEvent("cleanRecord",ss.str());
                    }    
                    if(_power>50)
                        _power--;
                }
            },1000);
            _ep.setTimer([this](int id){
                if(_stateMachine.getState()==State::CLEANING)
                {
                    loggerI("updateMap trajectory");
                    homi::getCleanRobot()->updateOSS(_trajectoryMap,_info.dump(),"trajectoryMap");
                    std::string mapContent;
                    if(mycompress("map.pgm",_map.serialization(),mapContent)==0)
                    {
                        std::cout<< "updateMap map" <<std::endl;
                        homi::getCleanRobot()->updateOSS(_mapUrl,mapContent,std::to_string(_cid)+_mapUrl);
                    }
                    
                    std::string wifiContent;
                    if(mycompress("wifi.pgm",_wifiHeat.serialization(),wifiContent)==0)
                    {
                        std::cout<< "updateMap wifiheat" <<std::endl;
                        homi::getCleanRobot()->updateOSS(_wifiHeatMap,wifiContent,"wifiHeat");
                    }
                }            
            },5000);     
            _ep.setTimer([this](int id){
                if(_stateMachine.getState()==State::CLEANING)
                {
                    position pos = {0,0};
                    if(_subCmd == sc_NO)
                        pos = _rm.moveNormal(_map,_forbiddenZone);
                    else if(_subCmd == sc_ROOM_CLEAN)
                        pos = _rm.moveInRoom(_map,_forbiddenZone,_rooms);
                    else if(_subCmd == sc_AREA_CLEAN)
                        pos = _rm.moveInArea(_map,_forbiddenZone,_areas);
                    else
                        pos = _rm.getCurrent();
                    _map.set(pos);
                    _wifiHeat.setRand(pos);
                    _info["robotLocation"]["x"] = pos.x;
                    _info["robotLocation"]["y"] = pos.y;
                    loggerI("update pos x=%d y=%d status=%d _cid=%llu",pos.x,pos.y,(int)_stateMachine.getState(),_cid);
                    
                }
                else if(_stateMachine.getState()==State::BACK_CHARGING)
                {
                    _chargeCnt++;
                    if(_chargeCnt>5)
                    {
                        _chargeCnt = 0;
                        _stateMachine.ChangeState(State::CHARGING);
                        position pos = {100,100};
                        _rm.setCurrent(pos);
                        _wifiHeat.setRand(pos);
                        _info["robotLocation"]["x"] = pos.x;
                        _info["robotLocation"]["y"] = pos.y;
                        loggerI("update pos x=%d y=%d status=%d _cid=%llu",pos.x,pos.y,(int)_stateMachine.getState(),_cid);                        
                        homi::getCleanRobot()->updateOSS(_trajectoryMap,_info.dump(),"trajectoryMap");
                    }
                } 
                else if(_stateMachine.getState()==State::CHARGING)
                {
                    _power++;
                    if(_power>100)
                    {
                        _power = 100;
                        _stateMachine.ChangeState(State::WAITING);
                    }
                }        
            },1000);           
            int cnt = 0;
            while (true)
            {
                std::this_thread::sleep_for(std::chrono::seconds(5));
                {
                    loggerI("idle... count=%d state=%d",cnt++,_stateMachine.getState());
                }
            }
        }
        int mycompress(const std::string &filename,const std::string &input,std::string &output)
        {
            std::remove(TMP_COMPRESS_FILE_PATH);
            auto status = mz_zip_add_mem_to_archive_file_in_place(TMP_COMPRESS_FILE_PATH, filename.c_str(), input.c_str(), input.size(), "test", (mz_uint16)strlen("test"), MZ_BEST_COMPRESSION);
            if (!status)
            {
            std::cout<<"mz_zip_add_mem_to_archive_file_in_place failed!"<<std::endl;
            return -1;
            }
            std::ifstream file(TMP_COMPRESS_FILE_PATH);
            if(!file.is_open()) return -1;
            std::string content((std::istreambuf_iterator<char>(file)), (std::istreambuf_iterator<char>())); // 读取文件内容到std::string
            output = std::move(content);
            file.close();
            return 0;
        }
        bool isInVaildCleaningPosition()
        {
            bool ret=false;
            auto pos = _rm.getCurrent();
            switch (_subCmd)
            {
                case SubCmd::sc_NO:
                    {
                        ret = true;
                    }
                    break;
                case SubCmd::sc_ROOM_CLEAN:
                    {
                        ret = _map.inRoom(pos,_rooms);
                    }
                    break;
                case SubCmd::sc_AREA_CLEAN:
                    {
                        for(auto &e:_areas)
                        {
                            if(e.inArea(pos))
                            {
                                ret = true;
                                break;
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            return ret;
        }
    private:
        StateMachine<Cleanrobot::State> _stateMachine ; 
        SubCmd _subCmd;
        int _cleaningArea;
        int _cleanCost;
        int _chargeCnt;
        int _power;
        EventLoop _ep;
        Map _map;
        WifiHeat _wifiHeat;
        nlohmann::json _info;
        unsigned long long _cid;
        std::string _mapUrl ; //: "ossurl,用来上传地图",
        std::string _trajectoryMap;// "轨迹oss",
        std::string _wifiHeatMap;   //"wifi热力图oss",
        RobotMove _rm;
        std::vector<unsigned char> _rooms;
        std::vector<Rectangle> _areas;
        ForbiddenZone _forbiddenZone;
        Quiet _quiet;
    };
}

#define OPT_STRING "i:s:u:l:m:q"
#define OPT_HELP "help\n"
static struct option long_options[] = {
    {"id", required_argument, NULL, 'i'},
    {"secret", required_argument, NULL, 's'},
    {"url",required_argument, NULL, 'u'},
    {"logfile",required_argument, NULL, 'l'},
    {"mapfile",required_argument, NULL, 'm'},
    {"quiet",no_argument, NULL, 'q'},
    {NULL, 0, NULL, 0},
};
int main(int argc,char *argv[])
{
    int opt;
    std::string deviceId = "10088";
    std::string secret ="10088";
    std::string url ="http://36.138.107.136:10000/robot/business/api/device/client/connect/url";
    std::string logFilePath="";
    std::string mapFilePath="";
    homi::LogLevel logLevel = homi::LogLevel::LOG_LEVEL_DEBUG;
    while ((opt = getopt_long(argc, argv, OPT_STRING, long_options,
                              NULL)) != -1) {
        switch (opt) {
        case 'i':
            if(optarg!=nullptr)
                deviceId = optarg;
            break;
        case 's':
            if(optarg!=nullptr)
                secret = optarg;
            break;
        case 'u':
            if(optarg!=nullptr)
                url = optarg;
            break;
        case 'l':
            if(optarg!=nullptr)
                logFilePath = optarg;
            break;
        case 'm':
            if(optarg!=nullptr)
                mapFilePath = optarg;
            break;
        case 'q':
            logLevel = homi::LogLevel::LOG_LEVEL_INFO;
            break;
        default:
            printf(OPT_HELP);
            exit(EXIT_FAILURE);
        }
    }
    auto h = homi::getOpenAPI();
    if(h==nullptr) return -1;
    nlohmann::json jsonParam;
    jsonParam["url"] = url;
    jsonParam["config"]["sn"] = deviceId;
    jsonParam["config"]["deviceId"] = deviceId;
    jsonParam["config"]["macId"] = "00-00-00-00-00-00";
    jsonParam["config"]["deviceType"] = "xxxxx";
    jsonParam["config"]["firmwareVersion"] = "3.0.1";
    auto ret = h->Init(jsonParam.dump(),logLevel,logFilePath);
    if(ret==-1) return -1;
    homi::example::Cleanrobot cb(mapFilePath);
    cb.run();
    return 0;
}
