#ifndef __SRC_EXAMPLE_EVENTLOOP_H_
#define __SRC_EXAMPLE_EVENTLOOP_H_
#include <functional>
#include <memory>
#include <queue>
#include <vector>
#include <thread>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <future>
namespace homi::example
{
    class EventLoop
    {
    public:
        using timderCallback_t = std::function<void(const int timerId)>;
        struct timer_t
        {
            timderCallback_t timercb;
            std::chrono::steady_clock::time_point expire;
            int delayms;
            int repeat;
            int timerId;
            bool always;
        };
        struct Compare {
            bool operator()(const std::shared_ptr<timer_t>& lhs, const std::shared_ptr<timer_t>& rhs) const {
                return lhs->expire > rhs->expire;
            }
        };
        EventLoop():_stop(false),_timerId(0)
        {
            _worker = std::thread(
                [this]
                {
                    _threadID = std::this_thread::get_id();
                    for(;;)
                    {
                        std::priority_queue<std::shared_ptr<timer_t>, std::vector<std::shared_ptr<timer_t>>, Compare> timerQueueTmp;
                        while(!_timerQueue.empty())
                        {
                            auto now = std::chrono::steady_clock::now();
                            auto timerPtr = _timerQueue.top();
                            if(now>timerPtr->expire)
                            {
                                _timerQueue.pop();
                                timerPtr->timercb(timerPtr->timerId);
                                if(!timerPtr->always)
                                {
                                    timerPtr->repeat--;
                                }
                                if(timerPtr->repeat>0)
                                {
                                    timerPtr->expire = std::chrono::steady_clock::now()+std::chrono::milliseconds(timerPtr->delayms);
                                    timerQueueTmp.push(timerPtr);
                                }
                            }else{break;}
                        }
                        while(!timerQueueTmp.empty())
                        {
                            auto timerPtr = timerQueueTmp.top();
                            timerQueueTmp.pop();
                            _timerQueue.push(timerPtr);
                        }
                        std::chrono::steady_clock::time_point waitTime;
                        if(!_timerQueue.empty())
                        {
                            auto timerPtr = _timerQueue.top();
                            waitTime = timerPtr->expire;
                        }
                        else
                        {
                            waitTime = std::chrono::steady_clock::now()+std::chrono::milliseconds(2000);
                        }
                        
                        std::function<void()> task=nullptr;
                        {
                            std::unique_lock<std::mutex> lock(this->_queueMutex);
                            this->_condition.wait_until(lock,waitTime,
                                [this]{ return this->_stop || !this->_tasks.empty(); });
                            if(this->_stop && this->_tasks.empty())
                                return;
                            if(!this->_tasks.empty())
                            {
                                task = std::move(this->_tasks.front());
                                this->_tasks.pop();
                            }
                        }
                        if(task) 
                        {
                            task();
                        }
                    }
                }
            );
        }
        ~EventLoop(){
            {
                std::unique_lock<std::mutex> lock(_queueMutex);
                _stop = true;
            }
            _condition.notify_all();
            _worker.join();
        }
        template<class F, class... Args>
        auto runInLoop(F&& f, Args&&... args) 
            -> std::future<typename std::result_of<F(Args...)>::type>
        {
            using return_type = typename std::result_of<F(Args...)>::type;

            auto task = std::make_shared< std::packaged_task<return_type()> >(
                    std::bind(std::forward<F>(f), std::forward<Args>(args)...)
                );
                
            std::future<return_type> res = task->get_future();
            if(_threadID !=std::this_thread::get_id())
            {
                {
                    std::unique_lock<std::mutex> lock(_queueMutex);

                    // don't allow enqueueing after stopping the pool
                    if(_stop)
                        throw std::runtime_error("enqueue on stopped ThreadPool");

                    _tasks.emplace([task](){ (*task)(); });
                }
                _condition.notify_one();
            }
            else
            {
                (*task)();
            }
            return res;        
        }
        int setTimer(const timderCallback_t&timercb,int delayms,int repeat = -1)
        {
            auto timerPtr= std::make_shared<timer_t>();
            bool always = false;
            if(timerPtr==nullptr) return -1;
            if(delayms<0) delayms = 0;
            if(repeat<=0) 
            {
                always = true;
                repeat = 1;
            }
            auto timerId = ++_timerId;
            timerPtr->repeat = repeat;
            timerPtr->expire = std::chrono::steady_clock::now()+std::chrono::milliseconds(delayms);
            timerPtr->timerId = timerId;
            timerPtr->timercb = timercb;
            timerPtr->delayms = delayms;
            timerPtr->always = always;
            runInLoop([this](const std::shared_ptr<timer_t> &timerPtr){
                _timerQueue.push(timerPtr);
            },timerPtr);
            return timerId;
        }
        void killTimer(int timerId)
        {
            if(timerId<0) return ;
            runInLoop([this](int timerId){
                std::vector<std::shared_ptr<timer_t>> tmpQueue;
                while (!_timerQueue.empty()) {
                    auto top = _timerQueue.top();
                    _timerQueue.pop();

                    if (top->timerId != timerId) {
                        tmpQueue.push_back(top);
                    }
                }
                for (const std::shared_ptr<timer_t>& t : tmpQueue) {
                    _timerQueue.push(t);
                }
            },timerId);
        }
    private:
        bool _stop;
        std::queue< std::function<void()> > _tasks;  
        std::thread _worker;
        // synchronization
        std::mutex _queueMutex;
        std::condition_variable _condition;
        std::atomic<int> _timerId;
        std::priority_queue<std::shared_ptr<timer_t>, std::vector<std::shared_ptr<timer_t>>, Compare> _timerQueue;
        std::thread::id _threadID;
    };
}
#endif  // __SRC_EXAMPLE_EVENTLOOP_H_
