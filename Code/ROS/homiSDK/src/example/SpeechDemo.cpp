#include "homi/OpenAPI.h"
#include "homi/speech.h"
#include "homi/Ability.h"
#include "homi/Logger.h"
#include <unistd.h>
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>
int main(int argc, char const *argv[])
{
    auto h = homi::getOpenAPI();
    int ret;
    std::cout << "begin" <<std::endl;
    loggerI("hello1 %d",1);

    std::string params = R"({
        "url":"http://36.138.107.136:10000/robot/business/api/device/client/connect/url",
        "config":{
            "sn":"1008611",
            "deviceId":"1008611",
            "macId":"xx-xx-xx-xx-xx-xx",	
            "deviceType":"xxx",	
            "firmwareVersion":"xx.xx.xx"
        }
    })"; 
    ret = h->Init(params,homi::LogLevel::LOG_LEVEL_DEBUG);
    
    std::cout << "init ret =" << ret << std::endl;

    loggerI("hello2 %d",1);

    loggerI("version: %s",h->getVersionInfo().c_str());

    ret = h->registerOTACallback([](const std::string &fromVersion,const std::string &toVersion,const std::string &url,const std::string &MD5){
        std::cout<< "OTA >> fromVersion:" << fromVersion << " toVersion:" << toVersion <<" url:" << url << " MD5" << MD5 << std::endl;
    });

    std::cout << "register OTA callback... " << ret << std::endl;

    ret = homi::registerAbilityPhoneCall([](const std::string &phoneNumber){
        std::cout<< "phone call >> " << phoneNumber.c_str() << std::endl;
    });

    std::cout << "register phone call callback... " << ret << std::endl;

    ret = homi::registerAbilityVoicePlay([](const std::string &url){
        std::cout<< "url >> " << url.c_str() << std::endl;
    });
    std::cout << "register voice play call callback... " << ret << std::endl;    
    
    ret = speechMediumCallback([](const char *buf,unsigned int len){
        std::cout << "medium recv :" << len << std::endl;

    });
    std::cout << "register medium send callback... " << ret << std::endl;

    ret = speechMediumAsrCallback([](const char *buf,unsigned int len){
        std::cout << "medium asr recv :" << len << "\n"<<buf << std::endl;

    });
    std::cout << "register medium asr callback... " << ret << std::endl;

    ret = speechMediumAsrEndCallback([](const char *buf,unsigned int len){
        std::cout << "medium asr end recv :" << len << "\n"<<buf << std::endl;

    });
    std::cout << "register medium asr end callback... " << ret << std::endl;

    ret = speechMediumStatusCallback([](const int status){
        std::cout << "medium Status :" << status << std::endl;

    });
    std::cout << "register medium Status callback... " << ret << std::endl;

    ret = speechCmdCallback([](const char *buf,unsigned int len){
        std::cout << "cmd recv :" << len << " " << buf<< std::endl;
    });
    std::cout << "register cmd send callback... " << ret << std::endl;
    const char *token = "abcdefg";
    ret = speechMediumConnect(25000,token);
    while(ret<0){
        ret = speechMediumConnect(25000,token);
        sleep(1);
    }
    std::cout << "open medium channel... " << ret << std::endl;
    if(argc==2)
    {
        auto sendFile = [token](const char *filename){
            for(;;)
            {
                bool retry = false;
                while(speechMediumConnect(25000,token)<0);
                std::ifstream file(filename, std::ios::binary);
                    if (file.is_open()) {
                    // 文件已成功打开，可以进行写入操作
                    char buffer[1024];
                    while (file.read(buffer, sizeof(buffer))) {
                        std::streamsize bytesRead = file.gcount();
                        if (bytesRead == 0) {
                            break;
                        }
                        // 处理读取的数据
                        auto ret = speechMediumSend(buffer,sizeof(buffer));
                        std::cout << "medium send1 len :" << ret << std::endl; 
                        if(ret<0)
                        {
                            retry = true;
                            break ;
                        }   
                        std::this_thread::sleep_for(std::chrono::milliseconds(32));
                    }    
                    file.close();  // 关闭文件
                    if(!retry) 
                    {
                        auto ret = speechMediumVAD();
                        std::cout << "speechMediumVAD,ret= " << ret << std::endl; 
                        return ;
                    }
                } else {
                    std::cout << "无法打开文件" << std::endl;
                    return ;
                }
            }
        };
        sendFile(argv[1]);
    }


    std::string cmdBuf = R"({
        "parameter":{
            "hardWareModel": "XF80",
            "aLGVoiceType": "soft",
            "deviceMac": "20:FF:00:00:00:01",
            "deviceID": "123456789",
            "systemVersion": "1.0.1"
        },
        "authentication": {
            "signature": "57FCA467674A6CFEC319A6A2BF03B1A8",
            "partnerId": "1001",
            "timestamp": "1700560931304"
        }
    })"; 
    ret = speechCmdSend(cmdBuf.c_str(),cmdBuf.size());
    std::cout << "cmd send len :" << ret << std::endl;
    sleep(10);
    speechMediumClose();

    int count = 0;
    while (true)
    {
        count++;
        std::cout << "idle..." << count <<std::endl;
        sleep(10);
    }
    
    return 0;
}