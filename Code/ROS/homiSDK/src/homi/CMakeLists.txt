project(homi)

# 使用git命令获取当前代码提交次数和提交ID，作为版本号的第三段和第四段
execute_process(
    COMMAND git rev-list HEAD --count
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_COMMIT_COUNT
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

execute_process(
    COMMAND git rev-parse --short HEAD
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_COMMIT_ID
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

# 设置版本号
set(PROJECT_VERSION_MAJOR 0)
set(PROJECT_VERSION_MINOR 9)


if(HOMI_CONFIG_AI_SPEECH)
add_definitions(-DHOMI_CONFIG_AI_SPEECH=1)
endif(HOMI_CONFIG_AI_SPEECH)

if(HOMI_CONFIG_ABILITY)
add_definitions(-DHOMI_CONFIG_ABILITY=1)
endif(HOMI_CONFIG_ABILITY)

if(HOMI_CONFIG_TINGS)
add_definitions(-DHOMI_CONFIG_TINGS=1)
endif(HOMI_CONFIG_TINGS)

if(HOMI_CONFIG_CLEANROBOT)
if(NOT HOMI_CONFIG_TINGS)
    message(FATAL_ERROR "HOMI_CONFIG_CLEANROBOT is ON,but HOMI_CONFIG_TINGS is OFF")
endif(NOT HOMI_CONFIG_TINGS)

add_definitions(-DHOMI_CONFIG_CLEANROBOT=1)
endif(HOMI_CONFIG_CLEANROBOT)


add_library(homi STATIC "")
target_link_libraries(homi 
    PRIVATE
    hv_static
    PUBLIC
    nlohmann_json
)
target_include_directories(homi 
    PUBLIC
    $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
    PRIVATE
    ${PROJECT_SOURCE_DIR}
    ${PROJECT_BINARY_DIR}/include
)


# 将版本号信息写入version.h文件中
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/include/inner/version.h.in ${PROJECT_BINARY_DIR}/include/homi/version.h @ONLY)

#  add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR/osal})
add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/util)

add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/protocol)

add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/framework)

add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/service)

add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/app)

add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/test)

install(
    TARGETS homi
    EXPORT homiConfig
    ARCHIVE DESTINATION lib)
install(EXPORT homiConfig DESTINATION cmake)

install(FILES  ${CMAKE_CURRENT_SOURCE_DIR}/include/homi/OpenAPI.h DESTINATION  include/homi/ )    
if(HOMI_CONFIG_AI_SPEECH)
install(FILES  ${CMAKE_CURRENT_SOURCE_DIR}/include/homi/speech.h DESTINATION  include/homi/ )    
install(FILES  ${CMAKE_CURRENT_SOURCE_DIR}/include/homi/Logger.h DESTINATION  include/homi/ )    
endif(HOMI_CONFIG_AI_SPEECH)
if(HOMI_CONFIG_ABILITY)
install(FILES  ${CMAKE_CURRENT_SOURCE_DIR}/include/homi/Ability.h DESTINATION  include/homi/ )    
endif(HOMI_CONFIG_ABILITY)
if(HOMI_CONFIG_TINGS)
if(NOT HOMI_CONFIG_CLEANROBOT)
install(FILES  ${CMAKE_CURRENT_SOURCE_DIR}/include/homi/Things.h DESTINATION  include/homi/ )  
endif(NOT HOMI_CONFIG_CLEANROBOT)  
endif(HOMI_CONFIG_TINGS)
if(HOMI_CONFIG_CLEANROBOT)
install(FILES  ${CMAKE_CURRENT_SOURCE_DIR}/include/homi/CleanRobotApp.h DESTINATION  include/homi/ )    
install(FILES  ${CMAKE_CURRENT_SOURCE_DIR}/include/ext/Logger.h DESTINATION  include/homi/ ) 
endif(HOMI_CONFIG_CLEANROBOT)

