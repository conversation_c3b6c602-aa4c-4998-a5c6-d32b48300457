#ifndef __SRC_HOMI_FRAMEWORK_MEDIATOR_H_
#define __SRC_HOMI_FRAMEWORK_MEDIATOR_H_
#include "framework/Framework.h"
namespace homi::framework{
    class Mediator;
    using MediatorPtr_t = std::shared_ptr<Mediator>;
    class IColleague {
    public:
        virtual ~IColleague(){};
        virtual void colleagueSend(const BeanVisitorPtr_t &beanVisitorPtr , const std::string & to="") = 0;
        virtual void colleagueReceive(const BeanVisitorPtr_t &beanVisitorPtr , const std::string & from) = 0;
    };
    using ColleaguePtr_t = std::shared_ptr<IColleague>;
    using ColleagueWeakPtr_t = std::weak_ptr<IColleague>;
    class Mediator :public Bean 
    {
    public:
        virtual void addColleague(const std::string &colleagueName,const ColleaguePtr_t &colleaguePtr) = 0;
        virtual void delColleague(const std::string &colleagueName) = 0;
        virtual void send(const BeanVisitorPtr_t &beanVisitorPtr ,const std::string & from, const std::string & to="") = 0;
    };
    MediatorPtr_t mediatorFactory();
}

#endif  // __SRC_HOMI_FRAMEWORK_MEDIATOR_H_
