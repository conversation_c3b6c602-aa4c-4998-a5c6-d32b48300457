#ifndef __SRC_HOMI_FRAMEWORK_REDIS_H_
#define __SRC_HOMI_FRAMEWORK_REDIS_H_

#include <chrono>
#include "framework/Framework.h"
namespace homi::framework{

    class Redis :public Bean 
    {
    public:
        // 设置键值对，带过期时间（毫秒）
        virtual void set(const std::string& key, const std::string& value, unsigned int milliseconds = 0) = 0;
        // 获取键对应的值
        virtual std::string get(const std::string& key) = 0;
        // 删除键值对
        virtual void del(const std::string& key) = 0;
    };
    using RedisPtr = std::shared_ptr<Redis>;
    RedisPtr redisFactory(); 

}

#endif  // __SRC_HOMI_FRAMEWORK_REDIS_H_
