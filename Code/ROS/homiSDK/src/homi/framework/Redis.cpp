#include <mutex>
#include <unordered_map>
#include "Redis.h"
namespace homi::framework{

    class RedisLite :public Redis{
    // 键值对数据结构
    struct KeyValue {
        std::string value;
        std::chrono::steady_clock::time_point expirationTime;
    };    
    public:
        virtual void set(const std::string& key, const std::string& value, unsigned int milliseconds) override
        {
            std::lock_guard<std::mutex> lock(mutex_);
            KeyValue kv;
            kv.value = value;
            if(milliseconds!=0)
                kv.expirationTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(milliseconds);
            else
                kv.expirationTime = std::chrono::steady_clock::time_point{};
            data_[key] = std::move(kv);
        }
        virtual std::string get(const std::string& key) override 
        {
            std::lock_guard<std::mutex> lock(mutex_);

            auto it = data_.find(key);
            if (it != data_.end()) {
                if (isExpired(it->second.expirationTime)) {
                    // 过期了，删除键值对
                    data_.erase(it);
                    return "";
                } else {
                    return it->second.value;
                }
            } else {
                return "";
            }
        }
        virtual void del(const std::string& key) override 
        {
            std::lock_guard<std::mutex> lock(mutex_);

            data_.erase(key);
        }
        virtual void accept(const BeanVisitorPtr_t &visitor) override
        {
            visitor->visit(shared_from_this());
        }
        virtual bool onBind(void) override
        {
            return true;
        }
        virtual ~RedisLite() override
        {
            hlogd("Destructor RedisLite");
        }
    private:
        std::unordered_map<std::string, KeyValue> data_;
        std::mutex mutex_;

        // 检查是否过期
        bool isExpired(const std::chrono::steady_clock::time_point& expirationTime) {
            return (std::chrono::steady_clock::now() >= expirationTime && expirationTime!=std::chrono::steady_clock::time_point{} );
        }
    };
    std::shared_ptr<Redis> redisFactory()
    {
        return BeanFactory<Redis,RedisLite>("");
    }
}