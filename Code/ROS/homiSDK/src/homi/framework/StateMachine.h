#ifndef __SRC_HOMI_FRAMEWORK_STATEMACHINE_H_
#define __SRC_HOMI_FRAMEWORK_STATEMACHINE_H_

#include <functional>
#include <unordered_map>
#include <vector>
#include <atomic>
namespace homi::framework{

template<typename StateType>
class StateMachine {
public:
    StateMachine(StateType init) {
        currentState.store(init);
        recursionFlag = false;
    }

    void AddTransition(StateType fromState, StateType toState, std::function<void()> transitionFunc) {
        transitions[fromState][toState] = transitionFunc;
    }

    void ChangeState(StateType newState) {
        if (recursionFlag) return;
        recursionFlag = true;
        auto transitionIt = transitions.find(currentState.load());
        if (transitionIt != transitions.end()) {
            auto& stateTransitions = transitionIt->second;
            auto transitionFuncIt = stateTransitions.find(newState);
            if (transitionFuncIt != stateTransitions.end()) {
                if (exitStateFunc) exitStateFunc();
                currentState.store(newState);
                transitionFuncIt->second();
                if (enterStateFunc) enterStateFunc();

            }
        }
        recursionFlag = false;
    }

    void FromChangeState(StateType fromState, StateType toState) {
        if (fromState == currentState.load()) {
            ChangeState(toState);
        }
    }
    void FromChangeState(const std::vector<StateType>& fromStates, StateType toState) {
        for (auto& e : fromStates)
        {
            if (e == currentState.load()) {
                FromChangeState(e, toState);
                break;
            }
        }
    }

    void SetEnterStateFunc(std::function<void()> func) {
        enterStateFunc = func;
    }

    void SetExitStateFunc(std::function<void()> func) {
        exitStateFunc = func;
    }

    StateType getState()
    {
        return currentState.load();
    }

    private:
        bool recursionFlag;
        std::atomic<StateType> currentState;
        std::unordered_map<StateType, std::unordered_map<StateType, std::function<void()>>> transitions;
        std::function<void()> enterStateFunc;
        std::function<void()> exitStateFunc;
    };

}
#endif  // __SRC_HOMI_FRAMEWORK_STATEMACHINE_H_