#ifndef __SRC_HOMI_FRAMEWORK_FRAMEWORK_H_
#define __SRC_HOMI_FRAMEWORK_FRAMEWORK_H_

#include <string>
#include <map>
#include <memory>
#include <unordered_map>
#include <functional>
#include <typeinfo>
#include <typeindex>
#include <mutex>
#include <type_traits>
#include "nlohmann/json.hpp"
#include "hv/hlog.h"
namespace homi::framework {

    class BeanVisitor;
    using BeanVisitorPtr_t = std::shared_ptr<BeanVisitor>;
    class Bean :public std::enable_shared_from_this<Bean>
    {
    public:
        virtual bool onBind(void) = 0;
        virtual void accept(const BeanVisitorPtr_t &visitor) = 0;
        virtual ~Bean(){};
    };
    using BeanPtr_t = std::shared_ptr<Bean>;
    // 访问者接口
    class BeanVisitor {
    public:
        template <typename T>
        void registerVisit(std::function<void(const std::shared_ptr<T> &)> func) {
            visitFuncs[typeid(T)] = [func](const BeanPtr_t &bean) {
                if (auto p = std::dynamic_pointer_cast<T>(bean)) {
                    func(p);
                }
            };
        }
        void visit(const BeanPtr_t &bean);
        virtual ~BeanVisitor(){}
    private:
        std::unordered_map<std::type_index, std::function<void(const BeanPtr_t &)>> visitFuncs;
    };

    class BeanContainer {
    private:
        std::unordered_map<std::string, BeanPtr_t> _beans;
        std::recursive_mutex _reMutex;
        static BeanContainer _sInstancePtr;
        // 将构造函数、析构函数和赋值运算符声明为私有，防止外部创建对象
        BeanContainer();
        ~BeanContainer();
    public:
        // 禁用拷贝构造函数和赋值运算符
        BeanContainer(const BeanContainer&) = delete;
        BeanContainer& operator=(const BeanContainer&) = delete;   
        static BeanContainer * getInstance();
        int onBind(void); 
        void clear(void);   
        // 注册 Bean
        template<typename T>
        void registerBean(const BeanPtr_t &bean,const std::string &beanId = "") {
            if(bean==nullptr) return ;
            std::lock_guard<std::recursive_mutex> lock(_reMutex);
            auto index = typeid(T).name()+beanId;
            hlogd("register Bean index=%s",index.c_str());
            _beans[index] = bean;
        }

        // 获取 Bean
        template<typename T>
        std::shared_ptr<T> getBean(const std::string &beanId = "") {
            std::lock_guard<std::recursive_mutex> lock(_reMutex);
            auto index = typeid(T).name()+beanId;
            hlogd("getBean Bean index=%s",index.c_str());
            auto iter = _beans.find(index);
            if (iter != _beans.end()) {
                if(auto bean = std::dynamic_pointer_cast<T>(iter->second))
                {
                    return bean;
                }
                else
                {
                    hloge("不能转换到%s 类型指针",typeid(T).name());
                    return nullptr;
                }
            }else{
                hlogd("找不到%s类型的bean",index.c_str());
                return nullptr;
            }
        }
    };
    template<class IF,class Impl,class... Args>
    std::shared_ptr<IF> BeanFactory(const std::string &name,Args&&... args)
    {
        auto bc = BeanContainer::getInstance();
        auto p = bc->getBean<IF>(name);
        if(p) return p;
        p = std::make_shared<Impl>(std::forward<Args>(args)...);
        if(p&&p->onBind())
        {
            bc->registerBean<IF>(p,name);
            return p;
        }
        else
        {
            return nullptr;
        }         
    }
}

#endif  // __SRC_HOMI_FRAMEWORK_FRAMEWORK_H_