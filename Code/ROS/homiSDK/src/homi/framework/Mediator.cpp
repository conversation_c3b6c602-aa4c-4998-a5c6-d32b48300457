#include "Mediator.h"
#include "util/RWLock.h"
namespace homi::framework{
    class ImplMediator :public Mediator
    {
    private:
        std::unordered_map<std::string, ColleagueWeakPtr_t> _colleagues;
        util::RWLock _rwLock;
    public:
        virtual void addColleague(const std::string &colleagueName,const ColleaguePtr_t &colleaguePtr) override
        {
            if(colleaguePtr==nullptr||colleagueName.empty()) return ;
            util::RWLock::Locker locker(_rwLock, util::RWLock::LockType::Write);
            _colleagues[colleagueName] = colleaguePtr;
        }
        virtual void send(const BeanVisitorPtr_t &beanVisitorPtr ,const std::string & from, const std::string & to) override
        {
            util::RWLock::Locker locker(_rwLock, util::RWLock::LockType::Read);
            if(!to.empty())
            {
                auto iter = _colleagues.find(to);
                if(iter!=_colleagues.end())
                {
                    auto p = iter->second.lock();
                    if(p!=nullptr)
                    {
                        p->colleague<PERSON><PERSON>eive(beanVisitorPtr,from);
                    }
                }
            }
            else
            {
                for(auto &e:_colleagues)
                {
                    if(e.first==from) continue;
                    auto p = e.second.lock();
                    if(p!=nullptr)
                    {
                        p->colleagueReceive(beanVisitorPtr,from);
                    }
                }
            }   
        }
        virtual void delColleague(const std::string &colleagueName)
        {
            util::RWLock::Locker locker(_rwLock, util::RWLock::LockType::Write);
            _colleagues.erase(colleagueName);
        }
        virtual void accept(const BeanVisitorPtr_t &visitor) override
        {
            visitor->visit(shared_from_this());
        }
        virtual bool onBind(void) override
        {
            return true;
        }
        virtual ~ImplMediator() override
        {
            hlogd("Destructor ImplMediator");
        }
    };
    MediatorPtr_t mediatorFactory()
    {
        return BeanFactory<Mediator,ImplMediator>("");
    }
}