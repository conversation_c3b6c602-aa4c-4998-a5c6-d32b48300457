#include "Framework.h"

namespace homi::framework{

    void BeanVisitor::visit(const BeanPtr_t &bean) {
        if (visitFuncs.count(typeid(*bean))) {
            visitFuncs[typeid(*bean)](bean);
        }
    }

    BeanContainer BeanContainer::_sInstancePtr;
    BeanContainer::BeanContainer(){}
    BeanContainer::~BeanContainer(){}
    BeanContainer* BeanContainer::getInstance()
    {
        return &BeanContainer::_sInstancePtr;
    }
    int BeanContainer::onBind(void)
    {
        std::lock_guard<std::recursive_mutex> lock(_reMutex);
        int err=0;
        for(auto &e:_beans)
        {
            if(!e.second->onBind())
            {
                err-=1;
                hloge("bind fail %d",err);
            }
        }
        return err;
    }
    void BeanContainer::clear(void)
    {
        std::lock_guard<std::recursive_mutex> lock(_reMutex);
        _beans.clear();
    }
}