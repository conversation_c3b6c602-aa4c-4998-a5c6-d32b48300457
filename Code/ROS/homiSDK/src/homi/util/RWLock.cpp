#include "RWLock.h"

namespace homi::util{

    void RWLock::ReadLock() {
        std::unique_lock<std::mutex> lock(mtx_);
        while (writing_) {
            read_cv_.wait(lock);
        }
        readers_++;
    }

    void RWLock::ReadUnlock() {
        std::lock_guard<std::mutex> lock(mtx_);
        readers_--;
        if (readers_ == 0) {
            write_cv_.notify_one();
        }
    }

    void RWLock::WriteLock() {
        std::unique_lock<std::mutex> lock(mtx_);
        while (readers_ > 0 || writing_) {
            write_cv_.wait(lock);
        }
        writing_ = true;
    }

    void RWLock::WriteUnlock() {
        std::lock_guard<std::mutex> lock(mtx_);
        writing_ = false;
        read_cv_.notify_all();
        write_cv_.notify_one();
    }    
    RWLock::Locker::Locker(RWLock& rwlock, LockType type) : rwlock_(rwlock), type_(type) {
        if (type_ == LockType::Read) {
            rwlock_.ReadLock();
        } else if (type_ == LockType::Write) {
            rwlock_.WriteLock();
        }
    }

    RWLock::Locker::~Locker() {
        if (type_ == LockType::Read) {
            rwlock_.ReadUnlock();
        } else if (type_ == LockType::Write) {
            rwlock_.WriteUnlock();
        }
    }
}