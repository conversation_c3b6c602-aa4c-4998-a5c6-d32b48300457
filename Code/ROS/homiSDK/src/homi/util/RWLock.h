#ifndef __SRC_HOMI_UTIL_RWLOCK_H_
#define __SRC_HOMI_UTIL_RWLOCK_H_
#include <condition_variable>
#include <mutex>
namespace homi::util{

    class RWLock {
    public:
        enum class LockType {
            Read,
            Write,
        };

        class Locker {
        public:
            Locker(RWLock& rwlock, LockType type);
            ~Locker();
        private:
            RWLock& rwlock_;
            LockType type_;
        };

        void ReadLock();

        void ReadUnlock();

        void WriteLock();

        void WriteUnlock();
    private:
        std::mutex mtx_;
        std::condition_variable read_cv_;
        std::condition_variable write_cv_;
        int readers_;
        bool writing_;
    };

}

#endif  // __SRC_HOMI_UTIL_RWLOCK_H_
