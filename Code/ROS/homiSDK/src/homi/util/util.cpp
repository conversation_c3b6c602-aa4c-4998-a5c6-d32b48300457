#include <stddef.h>
#include <atomic>
#include "util.h"
namespace homi::util{
    
    time_t getTimestamp()
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        return tv.tv_sec;
    }
    
    static struct GlobalId{
        GlobalId(){
            struct timeval tv;
            gettimeofday(&tv, NULL);
            _globalId = tv.tv_sec*1000+tv.tv_usec/1000;
        }
        std::atomic<GlobalId_t> _globalId;
    }__globalId;
    
    GlobalId_t getGlobalID(void)
    {
        ++ __globalId._globalId;
        return __globalId._globalId;
    }
}