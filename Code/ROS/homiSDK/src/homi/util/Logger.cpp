#include "homi/Logger.h"
#include "hv/hlog.h"



namespace homi{
    
    static bool s_loggerInit;

    logger_t* loggerGet()
    {
        if(!s_loggerInit)
            return nullptr; 
        return hv_default_logger();
    }
    void loggerInit(void)
    {
        s_loggerInit = true;
    }

    int loggerLevel2Int(LogLevel level)
    {
        switch (level)
        {
        case LogLevel::LOG_LEVEL_TRACE :
        case LogLevel::LOG_LEVEL_DEBUG :
            /* code */
            return log_level_e::LOG_LEVEL_DEBUG;
        case LogLevel::LOG_LEVEL_INFO :
            /* code */
            return log_level_e::LOG_LEVEL_INFO;
        case LogLevel::LOG_LEVEL_WARN :
            /* code */
            return log_level_e::LOG_LEVEL_WARN;
        case LogLevel::LOG_LEVEL_ERROR :
            /* code */
            return log_level_e::LOG_LEVEL_ERROR;
        case LogLevel::LOG_LEVEL_FATAL :
            /* code */
            return log_level_e::LOG_LEVEL_FATAL;        
        default:
            return log_level_e::LOG_LEVEL_VERBOSE;
        }
    }
}

