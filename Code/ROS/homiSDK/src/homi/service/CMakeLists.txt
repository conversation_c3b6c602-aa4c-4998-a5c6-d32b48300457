project(homi_service)

target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/service.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/DeviceInfo.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/ServerInfo.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/EventLoopThread.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/WebSocketClientSIGC.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SignalControlService.cpp
    )
    
if(HOMI_CONFIG_AI_SPEECH)
target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/SpeechService.cpp
    )
endif(HOMI_CONFIG_AI_SPEECH)

if(HOMI_CONFIG_TINGS)
target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/ThingsService.cpp
    )
endif(HOMI_CONFIG_TINGS)