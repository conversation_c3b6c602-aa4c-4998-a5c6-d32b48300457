#ifndef __SRC_HOMI_SERVICE_EVENTLOOPTHREAD_H_
#define __SRC_HOMI_SERVICE_EVENTLOOPTHREAD_H_
#include <memory>
#include "framework/Framework.h"
#include "hv/EventLoopThread.h"
namespace homi::service{

    class IEventLoopThread:public framework::Bean
    {
    public:
        virtual hv::EventLoopPtr getEventLoopPtr(void) = 0; 
        virtual int runInLoop(const std::function<void(void)> &fun) = 0;
        virtual hv::TimerID setTimerInLoop(int timeout_ms, const hv::TimerCallback &cb, uint32_t repeat = INFINITE, hv::TimerID timerID = INVALID_TIMER_ID) =0;
        virtual hv::TimerID setTimer(int timeout_ms, const hv::TimerCallback &cb, uint32_t repeat = INFINITE, hv::TimerID timerID = INVALID_TIMER_ID) =0;
        virtual void killTimer(hv::TimerID timerID) =0;
        virtual void resetTimer(hv::TimerID timerID, int timeout_ms)  =0;
    };
    std::shared_ptr<IEventLoopThread> eventLoopThreadFactory(const std::string &name);

}

#endif  // __SRC_HOMI_SERVICE_EVENTLOOPTHREAD_H_
