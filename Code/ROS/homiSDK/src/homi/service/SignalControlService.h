#ifndef __SRC_HOMI_SERVICE_SIGNALCONTROLSERVICE_H_
#define __SRC_HOMI_SERVICE_SIGNALCONTROLSERVICE_H_

#include "framework/Framework.h"
#include "WebSocketClientSIGC.h"
namespace homi::service{
    // 定义结构体类型
    struct SIGCCmd 
    {
        std::string deviceId;
        std::string domain;
        std::string event;
        std::string eventId;
        unsigned long long seq;
        bool response;
        nlohmann::json body;
    };
    void to_json(nlohmann::json& json, const SIGCCmd& myStruct);
    void from_json(const nlohmann::json& json, SIGCCmd& myStruct);
    class ISignalControlService:public framework::Bean 
    {
    public:
        enum ThreadPoolNum{
        System = 0,
        Things,
        Speech,
        Ability,
        User
        };

        using SendCallback_t = std::function<int(const std::shared_ptr<std::string> &,const bool)>;
        using ProcessCallback_t = std::function<void(const SIGCCmd&,const SendCallback_t &)>;
        virtual int registerCallback(const std::string &domain,const std::string &event,ThreadPoolNum num,const ProcessCallback_t &fun) = 0;
        virtual int send(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body) = 0;
        virtual int send(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false) = 0 ;
        virtual int sendOOB(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false,unsigned int milliseconds = 0) = 0 ;
        virtual int sendOOB(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body,unsigned int milliseconds = 0) = 0;
    };
    std::shared_ptr<ISignalControlService> signalControlServiceFactory(
        std::map<ISignalControlService::ThreadPoolNum,int> threadPoolConfig = {}
    );
}

#endif  // __SRC_HOMI_SERVICE_SIGNALCONTROLSERVICE_H_
