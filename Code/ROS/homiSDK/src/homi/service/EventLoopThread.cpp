#ifndef __SRC_HOMI_SERVICE_EVENTLOOPTHREAD_CPP_
#define __SRC_HOMI_SERVICE_EVENTLOOPTHREAD_CPP_
#include "nlohmann/json.hpp"
#include "EventLoopThread.h"

namespace homi::service{

    class ImplEventLoopThread:public IEventLoopThread
    {
    public:
        ImplEventLoopThread()
        {
            _isBound = false;
        }
        virtual bool onBind(void)
        {
            if(_isBound) return true;
            auto eventLoopThreadPtr = std::make_shared<hv::EventLoopThread>();
            if(eventLoopThreadPtr)
            {
                auto eventLoopPtr = eventLoopThreadPtr->loop();
                if(eventLoopPtr)
                {
                    _eventLoopThreadPtr = eventLoopThreadPtr;
                    _eventLoopPtr = eventLoopPtr;
                    _eventLoopThreadPtr->start();
                    _isBound = true;
                }
            }  
            return _isBound;
        }
        virtual void accept(const framework::BeanVisitorPtr_t &visitor) override
        {
            visitor->visit(shared_from_this());
        }
        virtual hv::EventLoopPtr getEventLoopPtr(void) override
        {
            return _eventLoopPtr;
        }
        virtual int runInLoop(const std::function<void(void)> &fun) override
        {
            if(_eventLoopPtr!=nullptr){
                _eventLoopPtr->runInLoop(fun);
                return 0;
            }
            else
            {
                return -1;
            }
        }
        virtual hv::TimerID setTimerInLoop(int timeout_ms, const hv::TimerCallback &cb, uint32_t repeat, hv::TimerID timerID) override
        {
            if(_eventLoopPtr!=nullptr){
                return _eventLoopPtr->setTimerInLoop(timeout_ms,cb,repeat,timerID);
            }else{
                return INVALID_TIMER_ID;
            }
        }
        virtual hv::TimerID setTimer(int timeout_ms, const hv::TimerCallback &cb, uint32_t repeat, hv::TimerID timerID) override
        {
            if(_eventLoopPtr!=nullptr){
                return _eventLoopPtr->setTimer(timeout_ms,cb,repeat,timerID);
            }else{
                return INVALID_TIMER_ID;
            }
        } 
        virtual void killTimer(hv::TimerID timerID) override
        {
            if(_eventLoopPtr!=nullptr){
                _eventLoopPtr->killTimer(timerID);
            }
        }
        virtual void resetTimer(hv::TimerID timerID, int timeout_ms) override
        {
            if(_eventLoopPtr!=nullptr){
                _eventLoopPtr->resetTimer(timerID,timeout_ms);
            }
        }
        virtual ~ImplEventLoopThread() override
        {
            hlogd("Destructor ImplEventLoopThread,_eventLoopThreadPtr use_count =%ld %ld",_eventLoopThreadPtr.use_count(),_eventLoopThreadPtr->loop().use_count());
        }
    private:
        std::shared_ptr<hv::EventLoopThread> _eventLoopThreadPtr;
        std::shared_ptr<hv::EventLoop> _eventLoopPtr;
        bool _isBound;
    };

    std::shared_ptr<IEventLoopThread> eventLoopThreadFactory(const std::string &name)
    {
        return framework::BeanFactory<IEventLoopThread,ImplEventLoopThread>(name);
    }
}

#endif  // __SRC_HOMI_SERVICE_EVENTLOOPTHREAD_CPP_
