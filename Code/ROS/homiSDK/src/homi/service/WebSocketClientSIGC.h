#ifndef __SRC_HOMI_SERVICE_WEBSOCKETCLIENTSIGC_H_
#define __SRC_HOMI_SERVICE_WEBSOCKETCLIENTSIGC_H_

#include <string>
#include <functional>
#include <memory>
#include "framework/Framework.h"

namespace homi::service{

    class IWebSocketClientSIGC:public framework::Bean
    {
    public:
        virtual int reconnect(unsigned int milliseconds = 0)=0;
        virtual int registerOnMessage(const std::function<void(const std::string &)> &func,unsigned int milliseconds = 0)=0;
        virtual int send(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false)=0;
        virtual int sendOOB(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false,unsigned int milliseconds = 0) =0;
    };
    std::shared_ptr<IWebSocketClientSIGC> webSocketClientSIGCFactory(void);
}

#endif  // __SRC_HOMI_SERVICE_WEBSOCKETCLIENTSIGC_H_
