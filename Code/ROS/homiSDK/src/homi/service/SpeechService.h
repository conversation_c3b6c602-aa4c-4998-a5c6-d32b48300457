#ifndef __SRC_HOMI_SERVICE_SPEECHSERVICE_H_
#define __SRC_HOMI_SERVICE_SPEECHSERVICE_H_

#include <string>
#include <functional>
#include <memory>
#include "framework/Framework.h"
namespace homi::service{

    class ISpeechService:public framework::Bean
    {
    public:
        using SpeechMediaCallback_t = std::function<void(const char *buf,unsigned int len)>; 
        using SpeechMediaStatusCallback_t = std::function<void(const int status)>; 
        using SpeechCmdCallback_t = std::function<void(const char *buf,unsigned int len)>; 
        virtual int connect(unsigned int milliseconds,const std::string &token) = 0;
        virtual int disconnect(void) = 0;
        virtual int sendMedia(const std::shared_ptr<std::string> &dataPtr) = 0;
        virtual int sendMediaFlagVoiceEnd(void) = 0;
        virtual int sendCmd(const std::string &data) = 0;
        virtual int registerMediaCallback(const SpeechMediaCallback_t &fun) = 0;
        virtual int registerMediaAsrCallback(const SpeechMediaCallback_t &fun) = 0;
        virtual int registerMediaAsrEndCallback(const SpeechMediaCallback_t &fun) = 0;
        virtual int registerMediaStatusCallback(const SpeechMediaStatusCallback_t &fun) = 0;
        virtual int registerCmdCallback(const SpeechCmdCallback_t &fun) = 0;
    };
    std::shared_ptr<ISpeechService> speechServiceFactory();
}

#endif  // __SRC_HOMI_SERVICE_SPEECHSERVICE_H_
