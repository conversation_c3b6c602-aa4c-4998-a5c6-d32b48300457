#include <mutex>
#include <condition_variable>
#include <chrono>
#include <future>
#include "inner/systemdef.h"
#include "framework/Redis.h"
#include "framework/StateMachine.h"
#include "EventLoopThread.h"
#include "WebSocketClientSIGC.h"
#include "hv/EventLoopThread.h"
#include "hv/WebSocketClient.h"
#include "hv/AsyncHttpClient.h"

#define CONNECT_URL SERVICE_DEF"sigc_connect_url"
#define CONNECT_URL_EXPIRE (5*60*1000)
#define FIFO_SIZE   100
namespace homi::service{

    class ImplWebSocketClientSIGC:public IWebSocketClientSIGC
    {
    public:
        struct SendData_t
        {
            std::shared_ptr<std::string> dataPtr;
            bool isBinary;
        };
        enum State
        {
            s_disconnect = 0,
            s_connecting1,
            s_connecting2,
            s_connected
        };
        ImplWebSocketClientSIGC():_stateMachine(State::s_disconnect)
        {
            _isBound = false;
            _stateMachine.AddTransition(State::s_disconnect,State::s_connecting1,[this](){ });
            _stateMachine.AddTransition(State::s_connecting1,State::s_connecting2,[this](){ });
            _stateMachine.AddTransition(State::s_connecting2,State::s_connected,[this](){ });
            _stateMachine.AddTransition(State::s_connecting1,State::s_connecting1,[this](){ });
            _stateMachine.AddTransition(State::s_connecting2,State::s_connecting1,[this](){ });
            _stateMachine.AddTransition(State::s_connected,State::s_connecting1,[this](){ });
            _stateMachine.AddTransition(State::s_connecting1,State::s_disconnect,[this](){ });
            _stateMachine.AddTransition(State::s_connecting2,State::s_disconnect,[this](){_webSocketClientPtr->close();});
            _stateMachine.AddTransition(State::s_connected,State::s_disconnect,[this](){_webSocketClientPtr->close();});
            _stateMachine.SetExitStateFunc([this](){ hlogd("exit state %d",_stateMachine.getState());});
            _stateMachine.SetEnterStateFunc([this](){
                auto state = _stateMachine.getState();
                hlogd("entry state %d",_stateMachine.getState());
                switch (state)
                {
                case State::s_connecting1:
                    {
                        HttpRequestPtr req = std::make_shared<HttpRequest>();
                        req->method = HTTP_POST;
                        req->url = _entryUrl;
                        req->headers["Content-Type"] = "application/json";
                        req->body = _initParams.dump();
                        req->timeout = 5;
                        HttpResponseCallback respcallback = [this](const HttpResponsePtr& resp)
                        {
                            if(resp!=nullptr)
                            {
                                hlogi("%d %s\r\n", resp->status_code, resp->status_message());
                                hlogi("%s\n", resp->body.c_str());
                                try
                                {
                                    auto obj = nlohmann::json::parse(resp->body);
                                    auto url = obj["data"]["url"];
                                    if(url!=nullptr)
                                    {
                                        _connectUrl = url.get<std::string>();
                                        _stateMachine.FromChangeState(State::s_connecting1,State::s_connecting2);
                                        return ;
                                    }
                                }
                                catch(const std::exception& e)
                                {
                                    hlogw("%s",e.what());
                                }
                            }
                            auto eventLoopThreadPtr = _eventLoopThreadPtr.lock();
                            if(eventLoopThreadPtr!=nullptr)
                            {
                                eventLoopThreadPtr->setTimer(300,[this](hv::TimerID id){
                                    _stateMachine.FromChangeState(State::s_connecting1,State::s_connecting1);
                                },1,INVALID_TIMER_ID);
                            }
                            
                        };
                        _asyncHttpClientPtr->send(req,respcallback);                           
                        break;
                    }
                case State::s_connecting2:
                    {
                        _webSocketClientPtr->onopen = [this]() {
                            //const HttpResponsePtr& resp = _webSocketClientPtr->getHttpResponse();
                            hlogi("onopen\n\n");
                            _stateMachine.FromChangeState(State::s_connecting2,State::s_connected);
                        };
                        _webSocketClientPtr->onclose = [this]() {
                            hlogi("onclose %d\n",_webSocketClientPtr->isReconnect());
                            if(_stateMachine.getState()!=State::s_disconnect) _stateMachine.ChangeState(State::s_connecting1);
                        };
                        _webSocketClientPtr->onmessage = _msgCallback;
                        _webSocketClientPtr->open(_connectUrl.c_str());  
                    }
                    break;  
                case State::s_connected:
                    _send();
                    break;   
                case State::s_disconnect:

                    break;              
                default:
                    break;
                }
            });
        }
        virtual bool onBind(void) override
        {
            if(_isBound) return true;
            auto redisPtr  = framework::redisFactory();
            auto eventLoopThreadPtr = eventLoopThreadFactory(BEAN_EVENTLOOPTHREAD_MAIN);
            if(eventLoopThreadPtr==nullptr||redisPtr==nullptr) return -1;
            auto webSocketClientPtr = std::make_shared<hv::WebSocketClient>(eventLoopThreadPtr->getEventLoopPtr());
            auto asyncHttpClientPtr = std::make_shared<hv::AsyncHttpClient>(eventLoopThreadPtr->getEventLoopPtr());
            if(webSocketClientPtr==nullptr||asyncHttpClientPtr==nullptr)
                return false;
            _webSocketClientPtr = webSocketClientPtr;
            _asyncHttpClientPtr = asyncHttpClientPtr;
            _eventLoopThreadPtr = eventLoopThreadPtr;
            _redisPtr =redisPtr;
            _isBound = true;
            return _isBound;
        }
        virtual void accept(const framework::BeanVisitorPtr_t &visitor) override
        {
            visitor->visit(shared_from_this());
        }
        virtual int reconnect(unsigned int milliseconds = 0) override
        {
            if(!_isBound) return -1;
            auto eventLoopThreadPtr = _eventLoopThreadPtr.lock();
            if(eventLoopThreadPtr==nullptr) return -2;
            auto promisePtr = std::make_shared<std::promise<int>>();
            auto future = promisePtr->get_future();
            eventLoopThreadPtr->runInLoop([this,promisePtr](){
                auto redisPtr = _redisPtr.lock();
                if(redisPtr==nullptr) { promisePtr->set_value(-3); return ;}
                auto entryUrl = redisPtr->get(SYS_SIGC_ENTRY_URL);
                auto deviceId = redisPtr->get(SYS_DEVICE_ID);
                auto initParams = redisPtr->get(SYS_INIT_PARAMS);
                if(entryUrl.empty()||deviceId.empty()||initParams.empty()){ promisePtr->set_value(-4); return ;}
                _entryUrl = entryUrl;
                _deviceId = deviceId;
                try
                {
                    _initParams = nlohmann::json::parse(redisPtr->get(SYS_INIT_PARAMS));
                    _initParams["deviceId"] = _deviceId;
                }
                catch(const std::exception& e)
                {
                    hlogw("json 格式错误 %s",e.what());
                    promisePtr->set_value(-4); return ;
                }
                promisePtr->set_value(0);
                _stateMachine.ChangeState(State::s_disconnect);
                _stateMachine.ChangeState(State::s_connecting1);
            });
            if(milliseconds==0)
                return -1;
            auto futureStatus  = future.wait_for(std::chrono::milliseconds(milliseconds));
            if(futureStatus== std::future_status::ready)
            {
                return future.get();
            }
            return -1;
        }
        virtual int registerOnMessage(const std::function<void(const std::string &)> &func,unsigned int milliseconds = 0) override
        {
            if(!_isBound) return -1;
            auto eventLoopThreadPtr = _eventLoopThreadPtr.lock();
            if(eventLoopThreadPtr==nullptr) return -1;
            auto promisePtr = std::make_shared<std::promise<int>>();
            auto future = promisePtr->get_future();
            eventLoopThreadPtr->runInLoop([this,promisePtr,func](){
                if(_webSocketClientPtr!=nullptr)
                {
                    _msgCallback = func;
                    promisePtr->set_value(0);
                }
                else
                {
                    promisePtr->set_value(-1);
                }
            });
            if(milliseconds==0)
                return -1;
            auto futureStatus  = future.wait_for(std::chrono::milliseconds(milliseconds+100));
            if(futureStatus== std::future_status::ready)
            {
                return future.get();
            }
            return -1;
        }
        virtual int send(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false) override
        {
            if(!_isBound) return -1;
            auto eventLoopThreadPtr = _eventLoopThreadPtr.lock();
            if(eventLoopThreadPtr==nullptr) return -1;
            std::lock_guard<std::recursive_mutex> lock(_fifoQueueMutex);
            if(_fifoQueue.size()>FIFO_SIZE) return -1;
            _fifoQueue.push({dataPtr,isBinary});
            eventLoopThreadPtr->runInLoop([this](){
                _send();
            });
            return dataPtr->size();
        }
        virtual int sendOOB(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false,unsigned int milliseconds = 0) override
        {
            if(!_isBound) return -1;
            auto eventLoopThreadPtr = _eventLoopThreadPtr.lock();
            if(eventLoopThreadPtr==nullptr) return -1;
            auto promisePtr = std::make_shared<std::promise<int>>();
            auto future = promisePtr->get_future();
            eventLoopThreadPtr->runInLoop([this,promisePtr,dataPtr,isBinary](){
                if(_stateMachine.getState()==State::s_connected)
                {
                    int ret = _webSocketClientPtr->send(dataPtr->c_str(),dataPtr->size(),isBinary?ws_opcode::WS_OPCODE_BINARY:WS_OPCODE_TEXT);
                    if(ret>0) ret = dataPtr->size();
                    promisePtr->set_value(ret);
                }
                else
                {
                    promisePtr->set_value(-1);
                }

            });
            if(milliseconds==0)
                return -1;
            auto futureStatus  = future.wait_for(std::chrono::milliseconds(milliseconds+100));
            if(futureStatus== std::future_status::ready)
            {
                return future.get();
            }
            return -1;
        }
        virtual ~ImplWebSocketClientSIGC() override
        {
            hlogd("Destructor ImplWebSocketClientSIGC");
        }
    private:
        void _send(void)
        {
            std::lock_guard<std::recursive_mutex> lock(_fifoQueueMutex);
            if(_stateMachine.getState()!=State::s_connected) return;
            while(!_fifoQueue.empty())
            {
                auto data = _fifoQueue.front();
                if((_webSocketClientPtr->send(data.dataPtr->c_str(),data.dataPtr->size(),data.isBinary?ws_opcode::WS_OPCODE_BINARY:WS_OPCODE_TEXT))<0)
                {
                    hlogd("_send fail %u %d",(unsigned int)_fifoQueue.size(),_stateMachine.getState());
                    return ;
                }
                _fifoQueue.pop();
            }
        }
        std::shared_ptr<hv::WebSocketClient> _webSocketClientPtr;
        std::shared_ptr<hv::AsyncHttpClient> _asyncHttpClientPtr;
        std::weak_ptr<IEventLoopThread> _eventLoopThreadPtr;
        std::weak_ptr<framework::Redis> _redisPtr;
        std::queue<SendData_t> _fifoQueue;
        std::recursive_mutex _fifoQueueMutex;
        framework::StateMachine<State> _stateMachine;
        std::string _entryUrl;
        std::string _deviceId;
        nlohmann::json _initParams;
        std::string _connectUrl;
        std::function<void(const std::string &)> _msgCallback;
        bool _isBound;
    };
    std::shared_ptr<IWebSocketClientSIGC> webSocketClientSIGCFactory(void)
    {
        return framework::BeanFactory<IWebSocketClientSIGC,ImplWebSocketClientSIGC>("");
    }
}