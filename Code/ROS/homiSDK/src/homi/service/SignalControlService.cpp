#include "inner/systemdef.h"
#include "EventLoopThread.h"
#include "framework/ThreadPool.h"
#include "framework/Redis.h"
#include "util/util.h"
#include "SignalControlService.h"

#include "hv/EventLoopThread.h"
#include "hv/WebSocketClient.h"

#define KEEP_ALLIVE     (10*1000)

namespace homi::service{

    void from_json(const nlohmann::json& json, SIGCCmd& myStruct) {
        try
        {
            myStruct.deviceId.clear();
            myStruct.domain.clear();
            myStruct.event.clear();
            myStruct.eventId.clear();
            myStruct.response = false;
            myStruct.body.clear();
            auto iter = json.find("deviceId");
            if(iter!=json.end()) iter->get_to(myStruct.deviceId);
            iter = json.find("domain");
            if(iter!=json.end()) iter->get_to(myStruct.domain);
            iter = json.find("event");
            if(iter!=json.end()) iter->get_to(myStruct.event);
            iter = json.find("eventId");
            if(iter!=json.end()) iter->get_to(myStruct.eventId);
            iter = json.find("seq");
            if(iter!=json.end()) iter->get_to(myStruct.seq);
            iter = json.find("response");
            if(iter!=json.end()) iter->get_to(myStruct.response);
            iter = json.find("body");
            if(iter!=json.end()) iter->get_to(myStruct.body);
        }
        catch(const std::exception& e)
        {
            hlogw("homi::protocol::SIGCCmd,%s",e.what());
        }
    }
    void to_json(nlohmann::json& json, const SIGCCmd& myStruct)
    {
        json = nlohmann::json({
                    {"deviceId", myStruct.deviceId},
                    {"domain", myStruct.domain},
                    {"event", myStruct.event},
                    {"eventId", myStruct.eventId},
                    {"seq", myStruct.seq},
                    {"response", myStruct.response},
                    {"body", myStruct.body}
                });
    }
    class ImplSignalControlService : public ISignalControlService
    {
    public:
        using SaveCallbackType_t = std::pair<ThreadPoolNum,ProcessCallback_t>;
        ImplSignalControlService(const std::map<ThreadPoolNum,int> &threadPoolConfig)
        {
            _isBound = false;
            _keepAliveTimerId = INVALID_TIMER_ID;
            _send=[](const std::shared_ptr<std::string> &dataPtr,const bool isBinary){ return -1;};
            for(auto &e:threadPoolConfig)
            {
                hlogi("init thread pool %d %d",e.first,e.second);
                _ThreadPoolGroup[e.first]=std::make_shared<framework::ThreadPool>(e.second);
            }
        }
        virtual ~ImplSignalControlService(){ hlogd("Destructor ImplSignalControlService");}
        virtual bool onBind(void) override
        {
            if(_isBound) return true;
            auto redisPtr  = framework::redisFactory();
            auto eventLoopThreadPtr = eventLoopThreadFactory(BEAN_EVENTLOOPTHREAD_MAIN);
            auto webSocketClientSIGCPtr = webSocketClientSIGCFactory();
            if(redisPtr==nullptr||eventLoopThreadPtr==nullptr||webSocketClientSIGCPtr==nullptr) return false;
            _redisPtr = redisPtr;
            _eventLoopThreadPtr = eventLoopThreadPtr;
            _webSocketClientSIGCPtr = webSocketClientSIGCPtr;
            std::weak_ptr<ImplSignalControlService> signalControlServicePtr = std::dynamic_pointer_cast<ImplSignalControlService>(shared_from_this());
            webSocketClientSIGCPtr->registerOnMessage([signalControlServicePtr](const std::string &msg){
                auto shareSignalControlServicePtr = signalControlServicePtr.lock();
                shareSignalControlServicePtr.get()->_parser(msg);
            });
            int reconnectRet = webSocketClientSIGCPtr->reconnect(1000);
            if(reconnectRet<0) hlogw("SIGC connect fail %d",reconnectRet);
            _send = [signalControlServicePtr](const std::shared_ptr<std::string> &dataPtr,const bool isBinary){
                hlogd("_send %d %d",(int)dataPtr->size(),(isBinary==true));
                auto shareSignalControlServicePtr = signalControlServicePtr.lock();
                return shareSignalControlServicePtr->send(dataPtr,isBinary);
            };
            _isBound = true;
            _keepAliveTimerId = eventLoopThreadPtr->setTimerInLoop(KEEP_ALLIVE,[this](hv::TimerID id){
                _keepAlive();
            });
            hlogd("_keepAliveTimerId = %llu",(long long unsigned int)_keepAliveTimerId);
            return true;
        }
        int sendto(const std::string &domain,const std::string &event,const bool &response,const nlohmann::json &body,bool oob)
        {
            if(!_isBound) return -1;
            auto webSocketClientSIGCPtr = _webSocketClientSIGCPtr.lock();
            if(webSocketClientSIGCPtr==nullptr) return -1;
            SIGCCmd mySIGCmd;
            auto redisPtr = _redisPtr.lock();
            auto deviceId = redisPtr->get(SYS_DEVICE_ID);
            if(deviceId.empty()) return -1;
            mySIGCmd.deviceId = deviceId;
            mySIGCmd.domain = domain;
            mySIGCmd.event = event;
            mySIGCmd.eventId = std::to_string(util::getGlobalID());
            mySIGCmd.seq = util::getTimestamp();
            mySIGCmd.response = response;
            mySIGCmd.body = body;
            std::string data;
            try
            {
                data = nlohmann::json(mySIGCmd).dump();
            }
            catch(const std::exception& e)
            {
                hlogw("%s",e.what());
                return -1;
            }
            if(oob){
                return webSocketClientSIGCPtr->sendOOB(std::make_shared<std::string>(std::move(data)));
            }else{
                return webSocketClientSIGCPtr->send(std::make_shared<std::string>(std::move(data)));
            }
        }
        virtual int send(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body) override
        {
            return sendto(domain,event,response,body,false);
        }
        virtual int sendOOB(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body,unsigned int milliseconds = 0) override
        {
            return sendto(domain,event,response,body,true);
        }
        virtual int send(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false) override
        {
            if(!_isBound) return -1;
            auto webSocketClientSIGCPtr = _webSocketClientSIGCPtr.lock();
            if(webSocketClientSIGCPtr==nullptr) return -1;
            return webSocketClientSIGCPtr->send(dataPtr,isBinary);
        }
        virtual int sendOOB(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false,unsigned int milliseconds = 0) override
        {
            if(!_isBound) return -1;
            auto webSocketClientSIGCPtr = _webSocketClientSIGCPtr.lock();
            if(webSocketClientSIGCPtr==nullptr) return -1;
            return webSocketClientSIGCPtr->sendOOB(dataPtr,isBinary,milliseconds);
        }
        virtual int registerCallback(const std::string &domain,const std::string &event,ThreadPoolNum num,const ProcessCallback_t &fun) override
        {
            if(!_isBound) return -1;
            auto eventLoopThreadPtr = _eventLoopThreadPtr.lock();
            if(eventLoopThreadPtr==nullptr) return -1;
            return eventLoopThreadPtr->runInLoop([this,domain,event,num,fun](){
                SaveCallbackType_t element = std::pair<ThreadPoolNum,ProcessCallback_t>(num,fun);
                _callbacks[domain][event] = element;
                hlogd("registerCallback domain=%s event=%s threadPoolNum=%d funType=%s(%u)",domain.c_str(),event.c_str(),num,typeid(fun).name(),(unsigned int)sizeof(fun));
            });
        }
        virtual void accept(const framework::BeanVisitorPtr_t &visitor) override
        {
            visitor->visit(shared_from_this());
        }       
    private:
        void _parser(const std::string &msg)
        {
            hlogd("%s",msg.c_str());
            auto obj = nlohmann::json::parse(msg);
            try
            {
                SIGCCmd myStruct = obj.get<SIGCCmd>();
                auto redisPtr = _redisPtr.lock();
                if(redisPtr==nullptr) return ;
                auto deviceId = redisPtr->get(SYS_DEVICE_ID);
                if(myStruct.deviceId==deviceId)
                {
                    runCallback(myStruct);
                }
                else
                {
                    hlogw("device(%s) is not match,recv %s",deviceId.c_str(),msg.c_str());
                }
            }
            catch(const std::exception& e)
            {
                hlogw("%s",e.what());
            }
        }
        void _keepAlive(void)
        {
            auto webSocketClientSIGCPtr = _webSocketClientSIGCPtr.lock();
            if(webSocketClientSIGCPtr==nullptr) return ;
            auto redisPtr = _redisPtr.lock();
            if(redisPtr==nullptr) return ;
            auto deviceId = redisPtr->get(SYS_DEVICE_ID);
            if(deviceId.empty()) return ;
            nlohmann::json obj = {
                {"deviceId",deviceId},
                {"domain","KEEP_ALIVE"},
                {"event","keep_alive"},
                {"eventId",std::to_string(util::getGlobalID())},
                {"seq",util::getTimestamp()}
            };
            std::string data = obj.dump();
            hlogd("send keep alive %s",data.c_str());
            webSocketClientSIGCPtr->sendOOB(std::make_shared<std::string>(std::move(data)));
        }
        void runCallback(const SIGCCmd& myStruct)
        {
            auto callbackIter = _callbacks.find(myStruct.domain);
            if(callbackIter!=_callbacks.end())
            {
                auto &saveCallbackMap = callbackIter->second;
                auto eventIter = saveCallbackMap.find(myStruct.event);
                if(eventIter!=saveCallbackMap.end())
                {
                    auto element = eventIter->second;
                    auto iter = _ThreadPoolGroup.find(element.first);
                    if(iter!=_ThreadPoolGroup.end())
                    {
                        hlogd("(%s)-(%s) run in thread pool %d...",myStruct.domain.c_str(),myStruct.event.c_str(),iter->first);
                        iter->second->enqueue(element.second,myStruct,_send);
                    }
                    else
                    {
                        hloge("(%s)-(%s) no thread pool match",myStruct.domain.c_str(),myStruct.event.c_str());
                        //element.second(myStruct,_send);
                    }
                }
            }
        }
        std::weak_ptr<IEventLoopThread> _eventLoopThreadPtr;
        std::weak_ptr<IWebSocketClientSIGC> _webSocketClientSIGCPtr;
        std::weak_ptr<framework::Redis> _redisPtr;
        std::unordered_map<ThreadPoolNum,std::shared_ptr<framework::ThreadPool>> _ThreadPoolGroup;
        std::unordered_map<std::string,std::unordered_map<std::string, SaveCallbackType_t>> _callbacks;
        SendCallback_t _send;
        hv::TimerID _keepAliveTimerId;
        bool _isBound;
    };
    std::shared_ptr<ISignalControlService> signalControlServiceFactory(
        std::map<ISignalControlService::ThreadPoolNum,int> threadPoolConfig
    )
    {
        if(threadPoolConfig.empty())
        {
            threadPoolConfig[ISignalControlService::ThreadPoolNum::System] = 4;
#ifdef HOMI_CONFIG_AI_SPEECH
            threadPoolConfig[ISignalControlService::ThreadPoolNum::Speech] = 1;
#endif
#ifdef HOMI_CONFIG_ABILITY
            threadPoolConfig[ISignalControlService::ThreadPoolNum::Ability] = 4;
#endif
#ifdef HOMI_CONFIG_TINGS
            threadPoolConfig[ISignalControlService::ThreadPoolNum::Things] = 1;   
#endif 
            threadPoolConfig[ISignalControlService::ThreadPoolNum::User] = 1;           
        }
        return framework::BeanFactory<ISignalControlService,ImplSignalControlService>("",threadPoolConfig);
    }
}