#ifndef __SRC_HOMI_SERVICE_THINGSSERVICE_H_
#define __SRC_HOMI_SERVICE_THINGSSERVICE_H_

#include <memory>
#include <string>
#include <map>
#include "framework/Framework.h"


namespace homi::service{
    
    class IThingsService : public framework::Bean
    {
    public:
        using ThingsCallback_t = std::function<void(const std::string &input,std::string &output)>;
        virtual int registerPropertiesReadCallback(const std::string &property,const ThingsCallback_t &func)=0;
        virtual int registerPropertiesWriteCallback(const std::string &property,const ThingsCallback_t &func)=0;
        virtual int registerServiceCallback(const std::string &service,const ThingsCallback_t &func)=0;
        virtual int registerPropertiesReadCallback(const std::map<std::string,ThingsCallback_t> &conf)=0;
        virtual int registerPropertiesWriteCallback(const std::map<std::string,ThingsCallback_t> &conf)=0;
        virtual int registerServiceCallback(const std::map<std::string,ThingsCallback_t> &conf)=0;
        virtual int sendEvent(const std::string &event,const std::string &output)=0;
    };
    std::shared_ptr<IThingsService> thingsServiceFactory();
}
#endif  // __SRC_HOMI_SERVICE_THINGSSERVICE_H_
