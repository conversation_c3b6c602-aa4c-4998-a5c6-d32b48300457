#include <mutex>
#include "SignalControlService.h"
#include "ThingsService.h"
#include "hv/hlog.h"
#define CLEAN_DOMAIN "CLEAN_DEVICE"
#define CLEAN_EVENT_EVENT  "device_event"
#define CLEAN_EVENT_PROPERTIES_READ  "properties_read"
#define CLEAN_EVENT_PROPERTIES_WRITE "properties_write"
#define CLENT_EVENT_SERVICE     "clean_service"
namespace homi::service{

    class ImplThingsService:public IThingsService
    {
    public:
        ImplThingsService()
        { 
            _isBound=false;
        }
        virtual ~ImplThingsService(){ hlogd("Destructor ImplThingsService");}
        virtual bool onBind(void) override
        {
            if(_isBound) return true;
            auto signalControlServicePtr = signalControlServiceFactory();
            if(signalControlServicePtr==nullptr) return false;
            _signalControlServicePtr = signalControlServicePtr;
            std::weak_ptr<ImplThingsService> mySelfServicePtr = std::dynamic_pointer_cast<ImplThingsService>(shared_from_this()); 
            if(signalControlServicePtr->registerCallback(CLEAN_DOMAIN,CLEAN_EVENT_PROPERTIES_READ,ISignalControlService::ThreadPoolNum::Things,
            [mySelfServicePtr](const SIGCCmd &cmd,const ISignalControlService::SendCallback_t &send){
                auto myselfPtr = mySelfServicePtr.lock();
                if(myselfPtr==nullptr) return ;
                auto ptr = myselfPtr.get();
                ptr->runProPertiesReadCallback(cmd,send);
            }
            )<0){ return false; }
            if(signalControlServicePtr->registerCallback(CLEAN_DOMAIN,CLEAN_EVENT_PROPERTIES_WRITE,ISignalControlService::ThreadPoolNum::Things,
            [mySelfServicePtr](const SIGCCmd &cmd,const ISignalControlService::SendCallback_t &send){
                auto myselfPtr = mySelfServicePtr.lock();
                if(myselfPtr==nullptr) return ;
                auto ptr = myselfPtr.get();
                ptr->runProPertiesWriteCallback(cmd,send);
            }
            )<0){ return false; }
            if(signalControlServicePtr->registerCallback(CLEAN_DOMAIN,CLENT_EVENT_SERVICE,ISignalControlService::ThreadPoolNum::Things,
            [mySelfServicePtr](const SIGCCmd &cmd,const ISignalControlService::SendCallback_t &send){
                auto myselfPtr = mySelfServicePtr.lock();
                if(myselfPtr==nullptr) return ;
                auto ptr = myselfPtr.get();
                ptr->runServiceCallback(cmd,send);
            }
            )<0){ return false; }
            _isBound = true;
            return true;
        }
        virtual void accept(const framework::BeanVisitorPtr_t &visitor) override
        {
            visitor->visit(shared_from_this());
        }
        virtual int registerPropertiesReadCallback(const std::string &property,const ThingsCallback_t &func) override
        {
            if(!_isBound) return -1;
            std::lock_guard<std::mutex> lock(_mutex);
            hlogd("register %s",property.c_str());
            _PropertiesReadcallbacks[property] = func;
            return 0;
        }
        virtual int registerPropertiesReadCallback(const std::map<std::string,ThingsCallback_t> &conf) override
        {
            if(!_isBound) return -1;
            std::lock_guard<std::mutex> lock(_mutex);
            for(auto &e:conf)
            {
                hlogd("register %s",e.first.c_str());
                _PropertiesReadcallbacks[e.first] = e.second;
            }
            return 0;
        }
        virtual int registerPropertiesWriteCallback(const std::string &property,const ThingsCallback_t &func) override
        {
            if(!_isBound) return -1;
            std::lock_guard<std::mutex> lock(_mutex);
            hlogd("register %s",property.c_str());
            _PropertiesWritecallbacks[property] = func;
            return 0;
        }
        virtual int registerPropertiesWriteCallback(const std::map<std::string,ThingsCallback_t> &conf) override
        {
            if(!_isBound) return -1;
            std::lock_guard<std::mutex> lock(_mutex);
            for(auto &e:conf)
            {
                hlogd("register %s",e.first.c_str());
                _PropertiesWritecallbacks[e.first] = e.second;
            }
            return 0;
        }
        virtual int registerServiceCallback(const std::string &service,const ThingsCallback_t &func) override
        {
            if(!_isBound) return -1;
            std::lock_guard<std::mutex> lock(_mutex);
            hlogd("register %s",service.c_str());
            _Servicecallbacks[service] = func;
            return 0;
        }
        virtual int registerServiceCallback(const std::map<std::string,ThingsCallback_t> &conf) override
        {
            if(!_isBound) return -1;
            std::lock_guard<std::mutex> lock(_mutex);
            for(auto &e:conf)
            {
                hlogd("register %s",e.first.c_str());
                _Servicecallbacks[e.first] = e.second;
            }
            return 0;
        }
        virtual int sendEvent(const std::string &event,const std::string &output) override
        {
            if(!_isBound) return -1;
            auto signalControlServicePtr = _signalControlServicePtr.lock();
            if(signalControlServicePtr==nullptr)
            {
                hlogd("signalControlServicePtr is nullptr");
                return -1;
            }
            nlohmann::json body;
            try
            {
                body["event"][event] = nlohmann::json::parse(output);
                hlogd("send %s %s",event.c_str(),body.dump().c_str());
            }
            catch(const std::exception& e)
            {
                hloge("json格式异常 %s",e.what());
            }
            return signalControlServicePtr->send(CLEAN_DOMAIN,CLEAN_EVENT_EVENT,false,body);
        }
        void runProPertiesReadCallback(const SIGCCmd &cmd,const ISignalControlService::SendCallback_t &send)
        {
            try
            {
                auto &properties = cmd.body["properties"];
                nlohmann::json resProperties;
                if(!properties.is_array()) return;
                std::lock_guard<std::mutex> lock(_mutex);
                for(auto &e:properties)
                {
                    auto property = e.get<std::string>();
                    auto iter = _PropertiesReadcallbacks.find(property);
                    if(iter!=_PropertiesReadcallbacks.end())
                    {
                        std::string output;
                        try
                        {
                            iter->second(property,output);
                            if(!output.empty())
                            {
                                auto josnObject =nlohmann::json::parse(output);
                                resProperties[property] = josnObject;
                            }
                        }
                        catch(const std::exception& e)
                        {
                            hloge("properties回调函数,json格式异常 %s",e.what());
                        }
                    }
                }
                if(cmd.response==true)
                {
                    SIGCCmd ret;
                    ret.deviceId = cmd.deviceId;
                    ret.domain = cmd.domain;
                    ret.event = cmd.event;
                    ret.eventId = cmd.eventId;
                    ret.response = false;
                    ret.seq = cmd.seq;
                    ret.body["properties"] = std::move(resProperties);
                    auto dataPtr = std::make_shared<std::string>(nlohmann::json(ret).dump());
                    hlogd("send %s",dataPtr->c_str());
                    send(dataPtr,false);
                }
            }
            catch(const std::exception& e)
            {
                hloge("%s",e.what());
            }
        }
        void runProPertiesWriteCallback(const SIGCCmd &cmd,const ISignalControlService::SendCallback_t &send)
        {
            try
            {
                auto &properties = cmd.body["properties"];
                nlohmann::json resProperties;
                if(!properties.is_object()) return;
                std::lock_guard<std::mutex> lock(_mutex);
                for(auto &e:properties.items())
                {
                    auto property = e.key();
                    auto iter = _PropertiesWritecallbacks.find(property);
                    if(iter!=_PropertiesWritecallbacks.end())
                    {
                        try
                        {
                            std::string output;
                            iter->second(e.value().dump(),output);
                            if(!output.empty())
                            {
                                auto josnObject =nlohmann::json::parse(output);
                                resProperties[property] = josnObject;
                            }
                        }
                        catch(const std::exception& e)
                        {
                            hloge("properties回调函数,json格式异常 %s",e.what());
                        }         
                    }

                }
                if(cmd.response==true)
                {
                    SIGCCmd ret;
                    ret.deviceId = cmd.deviceId;
                    ret.domain = cmd.domain;
                    ret.event = cmd.event;
                    ret.eventId = cmd.eventId;
                    ret.response = false;
                    ret.seq = cmd.seq;
                    ret.body["properties"] = std::move(resProperties);
                    auto dataPtr = std::make_shared<std::string>(nlohmann::json(ret).dump());
                    hlogd("send %s",dataPtr->c_str());
                    send(dataPtr,false);
                }
            }
            catch(const std::exception& e)
            {
                hloge("%s",e.what());
            }        
        }
        void runServiceCallback(const SIGCCmd &cmd,const ISignalControlService::SendCallback_t &send)
        {
            try
            {
                auto service = cmd.body["service"];
                if(!service.is_object()) return;
                std::lock_guard<std::mutex> lock(_mutex);
                auto iter = _Servicecallbacks.find(service.items().begin().key());
                if(iter!=_Servicecallbacks.end())
                {
                    std::string output;
                    try
                    {   
                        auto input = service.items().begin().value()["inputs"];
                        iter->second(input.dump(),output);
                        if(output.empty())
                            output = R"({})";
                    }
                    catch(const std::exception& e)
                    {
                        hloge("service 回调函数,json格式异常 %s",e.what());
                        output = R"({"errorCode":1000})";
                    }
                    if(cmd.response==true)
                    {
                        SIGCCmd ret;
                        ret.deviceId = cmd.deviceId;
                        ret.domain = cmd.domain;
                        ret.event = cmd.event;
                        ret.eventId = cmd.eventId;
                        ret.response = false;
                        ret.seq = cmd.seq;
                        ret.body["service"]["outputs"] = nlohmann::json::parse(output);
                        auto dataPtr = std::make_shared<std::string>(nlohmann::json(ret).dump());
                        hlogd("send %s",dataPtr->c_str());
                        send(dataPtr,false);
                    }
                }
            }
            catch(const std::exception& e)
            {
                hloge("%s",e.what());
            }
            
        }
    private:
        bool _isBound;
        std::weak_ptr<ISignalControlService> _signalControlServicePtr;
        std::unordered_map<std::string, ThingsCallback_t> _Servicecallbacks;
        std::unordered_map<std::string, ThingsCallback_t> _PropertiesReadcallbacks;
        std::unordered_map<std::string, ThingsCallback_t> _PropertiesWritecallbacks;
        std::mutex _mutex;
    };

    std::shared_ptr<IThingsService> thingsServiceFactory()
    {
        return framework::BeanFactory<IThingsService,ImplThingsService>("");
    }  
}