#include <future>
#include <chrono>
#include <mutex>
#include <condition_variable>
#include "inner/systemdef.h"
#include "framework/ThreadPool.h"
#include "framework/Redis.h"
#include "framework/StateMachine.h"
#include "SpeechService.h"
#include "util/util.h"
#include "SignalControlService.h"

#include "hv/WebSocketClient.h"

#define FIFO_SIZE   100
#define WAIT_EXPIRED_MS     100
namespace homi::service{

    // 定义结构体类型
    struct SpeechCmd 
    {
        std::string deviceId;
        std::string domain;
        std::string event;
        std::string eventId;
        unsigned long long seq;
        bool response;
        nlohmann::json body;
    };
    void from_json(const nlohmann::json& json, SpeechCmd& myStruct) {
        try
        {
            myStruct.deviceId.clear();
            myStruct.domain.clear();
            myStruct.event.clear();
            myStruct.eventId.clear();
            auto iter = json.find("deviceId");
            if(iter!=json.end()) iter->get_to(myStruct.deviceId);
            iter = json.find("domain");
            if(iter!=json.end()) iter->get_to(myStruct.domain);
            iter = json.find("event");
            if(iter!=json.end()) iter->get_to(myStruct.event);
            iter = json.find("eventId");
            if(iter!=json.end()) iter->get_to(myStruct.eventId);
            iter = json.find("seq");
            if(iter!=json.end()) iter->get_to(myStruct.seq);
            iter = json.find("response");
            if(iter!=json.end()) iter->get_to(myStruct.response);
            iter = json.find("body");
            if(iter!=json.end()) iter->get_to(myStruct.body);
        }
        catch(const std::exception& e)
        {
            hlogw("homi::protocol::SIGCCmd,%s",e.what());
        }
    }
    void to_json(nlohmann::json& json, const SpeechCmd& myStruct)
    {
        json = nlohmann::json({
                    {"deviceId", myStruct.deviceId},
                    {"domain", myStruct.domain},
                    {"event", myStruct.event},
                    {"eventId", myStruct.eventId},
                    {"seq", myStruct.seq},
                    {"response", myStruct.response},
                    {"body", myStruct.body}
                });
    }

    class ImplSpeechService : public ISpeechService
    {
    public:
        enum State{
            s_disconnect = 0,
            s_connecting1,
            s_connecting2,
            s_connecting3,
            s_connected
        };
        ImplSpeechService():_stateMachine(State::s_disconnect)
        {
            _isBound = false;
            _stateMachine.AddTransition(State::s_disconnect,State::s_connecting1,[this](){ _url.clear();_retryConnecting1=0; });
            _stateMachine.AddTransition(State::s_connecting1,State::s_connecting2,[this](){ });
            _stateMachine.AddTransition(State::s_connecting2,State::s_connecting3,[this](){ });
            _stateMachine.AddTransition(State::s_connecting3,State::s_connected,[this](){ });
            _stateMachine.AddTransition(State::s_connecting1,State::s_connecting1,[this](){ });
            _stateMachine.AddTransition(State::s_connecting2,State::s_connecting1,[this](){ _url.clear();_retryConnecting1=0;});
            _stateMachine.AddTransition(State::s_connecting3,State::s_connecting1,[this](){ _url.clear();_retryConnecting1=0;});
            _stateMachine.AddTransition(State::s_connecting3,State::s_connecting3,[this](){ });
            _stateMachine.AddTransition(State::s_connected,State::s_connecting1,[this](){ });
            _stateMachine.AddTransition(State::s_connecting1,State::s_disconnect,[this](){ });
            _stateMachine.AddTransition(State::s_connecting2,State::s_disconnect,[this](){_websocketClientPtr->close();});
            _stateMachine.AddTransition(State::s_connecting3,State::s_disconnect,[this](){_websocketClientPtr->close();});
            _stateMachine.AddTransition(State::s_connected,State::s_disconnect,[this](){_websocketClientPtr->close();});
            _stateMachine.SetExitStateFunc([this](){ hlogd("exit state %d",_stateMachine.getState());});
            _stateMachine.SetEnterStateFunc([this](){
                auto state = _stateMachine.getState();
                hlogd("entry state %d",_stateMachine.getState());
                switch (state)
                {
                case State::s_connecting1:
                    {
                        auto signalControlService = _signalControlService.lock();
                        if(signalControlService==nullptr)
                        {
                            _stateMachine.FromChangeState(State::s_connecting1,State::s_disconnect);
                            return ;                
                        }
                        auto body = nlohmann::json::parse(R"({})");
                        if(_retryConnecting1==0)
                        {
                            auto ret = signalControlService->send("INTERACTION_MULTIMODAL","multimodal_get_url",true,body);
                            if(ret<0)
                            {
                                _eventLoopPtr->setTimeout(100,[this](hv::TimerID id){
                                    _stateMachine.FromChangeState(State::s_connecting1,State::s_connecting1);
                                });
                                break;
                            }
                            _retryConnecting1=1;
                        }else
                        {
                            signalControlService->sendOOB("INTERACTION_MULTIMODAL","multimodal_get_url",true,body);
                        }
                        _eventLoopPtr->setTimeout(1000,[this](hv::TimerID id){
                                _stateMachine.FromChangeState(State::s_connecting1,State::s_connecting1);
                            });                                  
                    }
                    break;
                case State::s_connecting2:
                    {
                        _websocketClientPtr->onopen = [this]() {
                            //const HttpResponsePtr& resp = _websocketClientPtr->getHttpResponse();
                            hlogi("onopen\n\n");
                            _stateMachine.FromChangeState(State::s_connecting2,State::s_connecting3);
                        };
                        _websocketClientPtr->onclose = [this]() {
                            hlogi("onclose %d\n",_websocketClientPtr->isReconnect());
                            //if(_stateMachine.getState()!=State::s_disconnect) _stateMachine.ChangeState(State::s_connecting1);
                            _stateMachine.FromChangeState({State::s_connecting3,State::s_connected},State::s_disconnect);
                        };
                        _websocketClientPtr->onmessage = [this](const std::string& msg){
                            hlogd("onmessage(type=%s len=%d)\n", _websocketClientPtr->opcode() == WS_OPCODE_TEXT ? "text" : "binary",
                                (int)msg.size());
                            if(_websocketClientPtr->opcode() == WS_OPCODE_TEXT)
                            {
                                hlogd("recv:%s",msg.c_str());
                                try{
                                    SpeechCmd myCmd = nlohmann::json::parse(msg.c_str()).get<SpeechCmd>();
                                    if(myCmd.deviceId != _deviceId) return;
                                    if(_stateMachine.getState()==State::s_connecting3)
                                    {
                                        if(
                                            (myCmd.domain == "VOICE_INTERACTION_XF") &&
                                            (myCmd.event == "voice_ready") &&
                                            (myCmd.eventId == _lastEventId)
                                        )
                                        {
                                            _stateMachine.FromChangeState(State::s_connecting3,State::s_connected);
                                        }
                                    }
                                    else
                                    {
                                        if(myCmd.domain == "VOICE_INTERACTION")
                                        {
                                            if((myCmd.event == "voice_response_asr") && _speechMediaAsrCallback)
                                            {
                                                _threadPoolMediaPtr->enqueue([this](const std::string &data){
                                                    if(_speechMediaAsrCallback)
                                                        _speechMediaAsrCallback(data.c_str(),data.size());
                                                },myCmd.body.dump());
                                            }
                                            else if((myCmd.event == "voice_response_end") && _speechMediaAsrEndCallback)
                                            {
                                                _threadPoolMediaPtr->enqueue([this](const std::string &data){
                                                    if(_speechMediaAsrEndCallback)
                                                        _speechMediaAsrEndCallback(data.c_str(),data.size());
                                                },myCmd.body.dump());
                                            }
                                            else {}
                                        }
                                    }
                                }catch(const std::exception& e)
                                {
                                    hlogw("%s",e.what());
                                }
                            }
                            else
                            {
                                if(_stateMachine.getState()==State::s_connected&&_speechMediaCallback)
                                {
                                    _threadPoolMediaPtr->enqueue([this](const std::string &data){
                                        if(_speechMediaCallback)
                                            _speechMediaCallback(data.c_str(),data.size());
                                    },msg);
                                }
                            }                    
                        };
                        _websocketClientPtr->open(_url.c_str());   
                    }
                    break;  
                case State::s_connecting3:
                    {
                        SpeechCmd myCmd;
                        myCmd.deviceId = _deviceId;
                        myCmd.domain = "VOICE_INTERACTION_XF";
                        myCmd.event = "voice_start";
                        myCmd.body["token"] = _token;
                        _lastEventId = std::to_string(util::getGlobalID());
                        myCmd.eventId = _lastEventId;
                        myCmd.seq = util::getTimestamp();
                        auto data = nlohmann::json(myCmd).dump();
                        hlogd("send cmd voice_start: %s\n",data.c_str());
                        _websocketClientPtr->send(data); 
                        _eventLoopPtr->setTimeout(5000,[this](hv::TimerID id){
                            _stateMachine.FromChangeState(State::s_connecting3,State::s_connecting3);
                        });     
                    }
                    break;
                case State::s_connected:
                    {
                        _connectCV.notify_all();
                    }
                    break;   
                case State::s_disconnect:
                    {
                        if(_speechMediaStatusCallback)
                            _speechMediaStatusCallback(-1);
                    }
                    break;              
                default:
                    break;
                }
            });
        }
        ~ImplSpeechService(){ hlogd("Destructor ImplSpeechService"); }
        virtual bool onBind(void) override
        {
            if(_isBound) return true;

            auto signalControlService = signalControlServiceFactory();
            auto redisPtr = framework::redisFactory(); 
            if(signalControlService==nullptr||redisPtr==nullptr) return false;
            auto eventLoopThreadPtr = std::make_shared<hv::EventLoopThread>();
            if(eventLoopThreadPtr==nullptr) return false;
            auto eventLoopPtr = eventLoopThreadPtr->loop();
            if(eventLoopPtr==nullptr) return false;
            eventLoopThreadPtr->start();
            auto websocketClientPtr = std::make_shared<hv::WebSocketClient>(eventLoopPtr);
            if(websocketClientPtr==nullptr) return false;
            auto threadPoolMediaPtr = std::make_shared<framework::ThreadPool>(1);
            if(threadPoolMediaPtr==nullptr) return false;
            _redisPtr = redisPtr;
            _eventLoopPtr = eventLoopPtr;
            _eventLoopThreadPtr = eventLoopThreadPtr;
            _websocketClientPtr = websocketClientPtr;
            _signalControlService = signalControlService;
            _threadPoolMediaPtr = threadPoolMediaPtr;
            std::weak_ptr<ImplSpeechService> speechServicePtr = std::dynamic_pointer_cast<ImplSpeechService>(shared_from_this());
            signalControlService->registerCallback("INTERACTION_MULTIMODAL","multimodal_get_url",ISignalControlService::ThreadPoolNum::System,
            [speechServicePtr](const SIGCCmd&myCmd,const ISignalControlService::SendCallback_t &send){
                auto shareSpeechServicePtr = speechServicePtr.lock();
                if(shareSpeechServicePtr==nullptr) return ;
                auto iter = myCmd.body.find("url");
                if(iter!=myCmd.body.end())
                {
                    try
                    {
                        auto url = iter->get<std::string>();
                        auto p = shareSpeechServicePtr.get();
                        p->_eventLoopPtr->runInLoop([p,url](){
                            if(p->_stateMachine.getState()==State::s_connecting1)
                            {
                                p->_url = url;
                                p->_stateMachine.ChangeState(State::s_connecting2);
                            }
                        });
                    }
                    catch(const std::exception& e)
                    {
                        hlogw("%s",e.what());
                    }
                }
            });
            _isBound=true;
            return true;
        }
        virtual void accept(const framework::BeanVisitorPtr_t &visitor) override
        {
            visitor->visit(shared_from_this());
        }
        virtual int connect(unsigned int milliseconds,const std::string &token) override
        {
            if(!_isBound) return -1;
            auto state = _stateMachine.getState();
            if(state==State::s_connected){
                return 0;
            }
            _eventLoopPtr->runInLoop([this,token](){
                auto state = _stateMachine.getState();
                if(state==State::s_disconnect)
                {
                    auto redisPtr = _redisPtr.lock();
                    if(redisPtr==nullptr) {return ;}
                    auto deviceId = redisPtr->get(SYS_DEVICE_ID);
                    if(deviceId.empty()) {return ;}
                    _token = std::move(token);
                    _deviceId = std::move(deviceId);
                    _stateMachine.ChangeState(State::s_connecting1);
                }
            });
            auto timeout = std::chrono::steady_clock::now() + std::chrono::milliseconds(milliseconds);
            std::unique_lock<std::mutex> lock(_connectMutex);
            _connectCV.wait_until(lock,timeout,[this](){ return (_stateMachine.getState()==State::s_connected);});
            state = _stateMachine.getState();
            if(state==State::s_connected)
            {
                return 0;
            }
            else if(state==State::s_connecting1||state==State::s_connecting2||state==State::s_connecting3)
            {
                return -2;
            }
            else
            {
                return -1;
            }  
        }
        virtual int disconnect(void) override
        {
            if(!_isBound) return -1;
            _eventLoopPtr->runInLoop([this](){
                _stateMachine.ChangeState(State::s_disconnect);
            });
            return 0;
        }
        virtual int sendMedia(const std::shared_ptr<std::string> &dataPtr) override
        {
            if(!_isBound) return -1;
            auto state = _stateMachine.getState();
            if(state!=State::s_connected)
            {
                return -1;
            }
            if(dataPtr==nullptr)
            {
                return 0;
            }
            std::shared_ptr<std::promise<int>> promisePtr;
            try{
                promisePtr = std::make_shared<std::promise<int>>();
            }catch(...) {return -1;}
            auto future = promisePtr->get_future();
            _eventLoopPtr->runInLoop([this,promisePtr,dataPtr](){
                int ret = _websocketClientPtr->send(dataPtr->c_str(),dataPtr->size(),ws_opcode::WS_OPCODE_BINARY);
                if(ret<0)
                {
                    promisePtr->set_value(-1);
                    _stateMachine.ChangeState(State::s_disconnect);
                }
                else
                {
                    promisePtr->set_value(dataPtr->size());
                    hlogd("_send len= %d connect status=%d",ret,_stateMachine.getState());
                }
            });
            return future.get();
        }
        virtual int sendMediaFlagVoiceEnd(void) override
        {
            if(!_isBound) return -1;
            auto state = _stateMachine.getState();
            if(state!=State::s_connected)
            {
                return -1;
            }
            std::shared_ptr<std::promise<int>> promisePtr;
            try{
                promisePtr = std::make_shared<std::promise<int>>();
            }catch(...) {return -1;}
            auto future = promisePtr->get_future();
            _eventLoopPtr->runInLoop([this,promisePtr](){
                SpeechCmd myCmd;
                myCmd.deviceId = _deviceId;
                myCmd.domain = "VOICE_INTERACTION_XF";
                myCmd.event = "voice_end";
                myCmd.eventId = _lastEventId;
                myCmd.seq = util::getTimestamp();
                auto data = nlohmann::json(myCmd).dump();
                int ret = _websocketClientPtr->send(data); 
                if(ret<0)
                {
                    promisePtr->set_value(-1);
                    _stateMachine.ChangeState(State::s_disconnect);
                }
                else
                {
                    promisePtr->set_value(0);
                    hlogd("send cmd voice_end: %s\n",data.c_str());
                }
            });
            return future.get();            
        }
        virtual int registerMediaCallback(const SpeechMediaCallback_t &fun) override
        {
            if(!_isBound) return -1;
            _eventLoopPtr->runInLoop([this,fun](){
                _speechMediaCallback = fun;
            });
            return 0;
        }
        virtual int registerMediaAsrCallback(const SpeechMediaCallback_t &fun) override
        {
            if(!_isBound) return -1;
            _eventLoopPtr->runInLoop([this,fun](){
                _speechMediaAsrCallback = fun;
            });
            return 0;
        }
        virtual int registerMediaAsrEndCallback(const SpeechMediaCallback_t &fun) override
        {
            if(!_isBound) return -1;
            _eventLoopPtr->runInLoop([this,fun](){
                _speechMediaAsrEndCallback = fun;
            });
            return 0;
        }
        virtual int registerMediaStatusCallback(const SpeechMediaStatusCallback_t &fun) override
        {
            if(!_isBound) return -1;
            _eventLoopPtr->runInLoop([this,fun](){
                _speechMediaStatusCallback = fun;
            });
            return 0;
        }
        virtual int sendCmd(const std::string &data) override
        {
            if(!_isBound) return -1;
            auto signalControlService = _signalControlService.lock();
            if(signalControlService==nullptr) return -1; 
            nlohmann::json body;
            try
            {
                hlogd("%s",data.c_str());
                body = nlohmann::json::parse(data);
            }
            catch(const std::exception& e)
            {
                hloge("讯飞交互透传信令send内容为非json格式");
                return -1;
            }
            int ret = signalControlService->send("INTERACTION_MULTIMODAL_XF","transparent_device_auth",true,body);
            int len = data.size();
            if(ret>len)
                ret = len;
            return ret;
        }
        virtual int registerCmdCallback(const SpeechCmdCallback_t &fun) override
        {
            if(!_isBound) return -1;
            auto signalControlService = _signalControlService.lock();
            if(signalControlService==nullptr) return -1;
            return signalControlService->registerCallback("INTERACTION_MULTIMODAL_XF","transparent_device_auth",ISignalControlService::ThreadPoolNum::Speech,
            [fun](const SIGCCmd&cmd,const ISignalControlService::SendCallback_t &send){
                try
                {
                    std::string data =cmd.body.dump();
                    fun(data.c_str(),data.size());
                }
                catch(const std::exception& e)
                {
                    hlogw("%s",e.what());
                }
            });
        }    
    private:
        std::weak_ptr<ISignalControlService> _signalControlService;
        std::shared_ptr<hv::WebSocketClient> _websocketClientPtr;
        std::shared_ptr<hv::EventLoopThread> _eventLoopThreadPtr;
        std::shared_ptr<hv::EventLoop> _eventLoopPtr;
        std::weak_ptr<framework::Redis> _redisPtr;
        std::shared_ptr<framework::ThreadPool> _threadPoolMediaPtr;
        SpeechMediaCallback_t _speechMediaCallback;
        SpeechMediaCallback_t _speechMediaAsrCallback;
        SpeechMediaCallback_t _speechMediaAsrEndCallback;
        SpeechMediaStatusCallback_t _speechMediaStatusCallback;
        framework::StateMachine<State> _stateMachine;
        std::string _url;
        std::string _lastEventId;
        std::string _deviceId;
        bool _isBound;
        int _retryConnecting1;
        std::mutex _connectMutex;
        std::condition_variable _connectCV;
        std::string _token;
    };
    std::shared_ptr<ISpeechService> speechServiceFactory()
    {
        return framework::BeanFactory<ISpeechService,ImplSpeechService>("");
    }


}