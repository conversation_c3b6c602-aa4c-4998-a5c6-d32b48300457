project(homi_app)

target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/OpenAPI.cpp
)

if(HOMI_CONFIG_AI_SPEECH)
target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/speech.cpp
    )
endif(HOMI_CONFIG_AI_SPEECH)

if(HOMI_CONFIG_ABILITY)
target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/Ability.cpp
    )
endif(HOMI_CONFIG_ABILITY)

if(HOMI_CONFIG_TINGS)
target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/Things.cpp
    )
endif(HOMI_CONFIG_TINGS)

if(HOMI_CONFIG_CLEANROBOT)
target_sources(homi 
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/CleanRobotApp.cpp
    )    
endif(HOMI_CONFIG_CLEANROBOT)
