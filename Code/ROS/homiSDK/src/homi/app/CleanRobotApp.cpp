#include <memory>
#include <mutex>
#include "homi/CleanRobotApp.h"
#include "service/ThingsService.h"
#include "hv/AsyncHttpClient.h"
#include "hv/hlog.h"
namespace homi::app{
    class ImplCleanRobotApp :public homi::ICleanRobot
    {
    public:
        virtual ~ImplCleanRobotApp(){}
        int bind(void)
        {
            auto bc = homi::framework::BeanContainer::getInstance();
            auto h = bc->getBean<homi::service::IThingsService>();
            if(h==nullptr) return -1;
            auto asyncHttpClientPtr = std::make_shared<hv::AsyncHttpClient>();
            if(asyncHttpClientPtr==nullptr) return -1;
            _asyncHttpClientPtr = asyncHttpClientPtr;
            _thingsServicePtr = h;
            return 0;
        }
        virtual int registerPropertiesReadCallback(const std::map<std::string,ThingsCallback_t> &conf) override
        {
            auto h = _thingsServicePtr.lock();
            if(h==nullptr) return -1;
            return h->registerPropertiesReadCallback(conf);
        }
        virtual int registerPropertiesWriteCallback(const std::map<std::string,ThingsCallback_t> &conf) override
        {
            auto h = _thingsServicePtr.lock();
            if(h==nullptr) return -1;
            return h->registerPropertiesWriteCallback(conf);
        }
        virtual int registerServiceCallback(const std::map<std::string,ThingsCallback_t> &conf) override
        {
           auto h = _thingsServicePtr.lock();
            if(h==nullptr) return -1;
            return h->registerServiceCallback(conf); 
        }
        virtual int sendEvent(const std::string &event,const std::string &output) override
        {
            auto h = _thingsServicePtr.lock();
            if(h==nullptr) return -1;
            return h->sendEvent(event,output);
        }
        virtual void updateOSS(const std::string &url,const std::string &data,const std::string &remark="") override
        {
            if(url.empty())return ;
            HttpRequestPtr req = std::make_shared<HttpRequest>();
            nlohmann::json obj;
            req->method = HTTP_PUT;
            req->url = std::move(url);
            req->timeout = 5;
            req->String(data);
            HttpResponseCallback respcallback = [remark](const HttpResponsePtr& resp)
            {
                if(resp!=nullptr){
                    hlogi("updateOSS %s %d %s\r\n",remark.c_str(),(int)resp->status_code, resp->status_message());
                    hlogi("%s\n", resp->body.c_str());
                }else{
                    hlogi("resp==nullptr");
                }
            };
            _asyncHttpClientPtr->send(req,respcallback);            
        }
    private:
        std::weak_ptr<service::IThingsService> _thingsServicePtr;
        std::shared_ptr<hv::AsyncHttpClient> _asyncHttpClientPtr;
    };
}

namespace homi{
    ICleanRobot * getCleanRobot(void)
    {
        static app::ImplCleanRobotApp _sImplCleanRobot;
        static std::mutex _smutex;
        static bool _sIsBound = false;
        if(!_sIsBound)
        {
            std::lock_guard<std::mutex> lock(_smutex);
            if(!_sIsBound)
            {
                if(_sImplCleanRobot.bind()==0)
                    _sIsBound=true;
            }
        }
        return &_sImplCleanRobot;
    }
}