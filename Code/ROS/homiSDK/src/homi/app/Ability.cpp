#include <string>
#include "homi/Ability.h"
#include "framework/Framework.h"
#include "service/SignalControlService.h"
#if 0
#define OPERATEION(x) x

#define SIGC_REG_CALLBACK(domain,event,op) do{ \
    auto bc = homi::framework::BeanContainer::getInstance();    \
    auto h = bc->getBean<homi::service::ISignalControlService>();   \
    if(h==nullptr) return -1;   \
    return h->registerCallback(domain,event,homi::service::ISignalControlService::ability,[func](const homi::service::SIGCCmd&cmd,  \
        const homi::service::ISignalControlService::SendCallback_t &send){  \
            try \
            {   \
                auto phoneNumber = cmd.body["phoneNumber"].get<std::string>();  \
                func(phoneNumber);  \
            }   \
            catch(const std::exception& e)  \
            {   \
                hloge("%s",e.what());   \
            }   \
    }); }while(0)
#endif
namespace homi
{
    int registerAbilityPhoneCall(const std::function<void(const std::string &phoneNumber)> &func)
    {
        auto bc = homi::framework::BeanContainer::getInstance();
        auto h = bc->getBean<homi::service::ISignalControlService>();
        if(h==nullptr) return -1;
        return h->registerCallback("DEVICE_ABILITY","phone_call",homi::service::ISignalControlService::Ability,[func](const homi::service::SIGCCmd&cmd,
            const homi::service::ISignalControlService::SendCallback_t &send){
                try
                {
                    auto phoneNumber = cmd.body["phoneNumber"].get<std::string>();
                    func(phoneNumber);
                }
                catch(const std::exception& e)
                {
                    hloge("%s",e.what());
                }
        });
    }

    int registerAbilityVoicePlay(const std::function<void(const std::string &url)> &func)
    {
        auto bc = homi::framework::BeanContainer::getInstance();
        auto h = bc->getBean<homi::service::ISignalControlService>();
        if(h==nullptr) return -1;
        return h->registerCallback("DEVICE_ABILITY","voice_play",homi::service::ISignalControlService::Ability,[func](const homi::service::SIGCCmd&cmd,
            const homi::service::ISignalControlService::SendCallback_t &send){
                try
                {
                    auto url = cmd.body["url"].get<std::string>();
                    func(url);
                }
                catch(const std::exception& e)
                {
                    hloge("%s",e.what());
                }
        });
    }
}
