#include <mutex>
#include <string>
#include "inner/systemdef.h"
#include "framework/Framework.h"
#include "framework/Redis.h"
#include "service/SignalControlService.h"
#include "service/SpeechService.h"
#include "service/ThingsService.h"
#include "homi/OpenAPI.h"
#include "homi/version.h"
#include "hv/hlog.h"
namespace homi{
    void loggerInit(void);
}

namespace homi::app{

    class ImplOpenAPI: public homi::IOpenAPI
    {
    public:
        ImplOpenAPI(){_isInit = false;};
        virtual int Init(const std::string &params,homi::LogLevel level,const std::string &filepath="") override
        {
            if(_isInit) return 0;
            std::lock_guard<std::mutex> lock(_mutex);
            if(!_isInit)
            {
                nlohmann::json jsonParams;
                if(filepath.empty())
                    hlog_set_handler(stdout_logger);
                else
                    hlog_set_file(filepath.c_str());
                hlog_set_format("[%t] " DEFAULT_LOG_FORMAT);
                logger_enable_color(hlog,1);
                hlog_set_level(loggerLevel2Int(level));
                homi::loggerInit();
                auto redis = homi::framework::redisFactory();
                if(redis==nullptr) return -1;
                hlogi("init sys param. Version:%s",getVersionInfo().c_str());
                try
                {
                    jsonParams = nlohmann::json::parse(params);
                    redis->set(SYS_DEVICE_ID,jsonParams["config"]["deviceId"].get<std::string>());
                    redis->set(SYS_SIGC_ENTRY_URL,jsonParams["url"].get<std::string>());
                    redis->set(SYS_INIT_PARAMS,jsonParams["config"].dump());
                }
                catch(const std::exception& e)
                {
                    hloge("params 格式异常: %s",e.what());
                    return -1;
                }
                auto signalControlServicePtr = service::signalControlServiceFactory();
                if(signalControlServicePtr==nullptr) return -1;
#ifdef HOMI_CONFIG_AI_SPEECH
                auto speechServicePtr = service::speechServiceFactory();
                if(speechServicePtr==nullptr) return -1;
#endif //HOMI_CONFIG_AI_SPEECH
#ifdef HOMI_CONFIG_TINGS
                auto thingsServicePtr = service::thingsServiceFactory();
                if(thingsServicePtr==nullptr) return -1;
#endif //HOMI_CONFIG_TINGS
                _isInit= true;
            }                
            return 0;
        }
        virtual int registerOTACallback(std::function<void(const std::string &fromVersion,const std::string &toVersion,const std::string &url,const std::string &MD5)> func) override
        {
            if(func==nullptr)
            {
                hlogi("func 为空");
                return -1;
            }
            hlogi("注册OTA回调");
            if(!_isInit) 
            {
                hlogw("调用registerOTACallback前,未初始化SDK");
                return -1;
            }
            auto signalControlServicePtr = service::signalControlServiceFactory();
            if(signalControlServicePtr==nullptr) 
            {
                hloge("无法获取主信令模块资源");
                return -1;
            }
            return signalControlServicePtr->registerCallback(
                "OTA",
                "upgrade",
                homi::service::ISignalControlService::ThreadPoolNum::User,
                [func](const homi::service::SIGCCmd &cmd,const homi::service::ISignalControlService::SendCallback_t &send){
                    try
                    {
                        std::string fromVersion = cmd.body["fromVersion"].get<std::string>();
                        std::string toVersion = cmd.body["toVersion"].get<std::string>();
                        std::string MD5 = cmd.body["md5"].get<std::string>();
                        std::string url = cmd.body["url"].get<std::string>();
                        func(fromVersion,toVersion,url,MD5);
                    }
                    catch(const std::exception& e)
                    {
                        hloge("json 格式解析错误 %s",e.what());
                    }
                }
            );
        }
        virtual std::string getVersionInfo(void) override
        {
            return PROJECT_VERSION_MAJOR "." PROJECT_VERSION_MINOR "." PROJECT_VERSION_PATCH "." PROJECT_VERSION_COMMIT_ID; 
        }
    private:
        bool _isInit;
        std::mutex _mutex;
    };
}

namespace homi{
    
    IOpenAPI *getOpenAPI(void)
    {
        static app::ImplOpenAPI _sImplOpenAPI;
        return &_sImplOpenAPI;
    }
}