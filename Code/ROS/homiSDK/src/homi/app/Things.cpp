#include "homi/Things.h"
#include "service/ThingsService.h"
namespace homi{
    int registerPropertiesReadCallback(const std::map<std::string,ThingsCallback_t> &conf)
    {
        auto bc = homi::framework::BeanContainer::getInstance();
        auto h = bc->getBean<homi::service::IThingsService>();
        if(h==nullptr) return -1;
        return h->registerPropertiesReadCallback(conf);
    }
    int registerPropertiesWriteCallback(const std::map<std::string,ThingsCallback_t> &conf)
    {
        auto bc = homi::framework::BeanContainer::getInstance();
        auto h = bc->getBean<homi::service::IThingsService>();
        if(h==nullptr) return -1;
        return h->registerPropertiesWriteCallback(conf);
    }
    int registerServiceCallback(const std::map<std::string,ThingsCallback_t> &conf)
    {
        auto bc = homi::framework::BeanContainer::getInstance();
        auto h = bc->getBean<homi::service::IThingsService>();
        if(h==nullptr) return -1;
        return h->registerServiceCallback(conf);   
    }
    int sendEvent(const std::string &event,const std::string &output)
    {
        auto bc = homi::framework::BeanContainer::getInstance();
        auto h = bc->getBean<homi::service::IThingsService>();
        if(h==nullptr) return -1;
        return h->sendEvent(event,output);
    }
}