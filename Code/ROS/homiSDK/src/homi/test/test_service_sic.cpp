#include <iostream>
#include "inner/systemdef.h"
#include "framework/Framework.h"
#include "framework/Redis.h"
#include "framework/Mediator.h"
#include "service/WebSocketClientSIGC.h"
#include "service/EventLoopThread.h"
#include "service/SignalControlService.h"
#include "hv/EventLoopThread.h"
#include "hv/hlog.h"
using namespace std;
using namespace homi;
static mutex _gMutex;
static void disp(int loglevel, const char* buf, int len)
{
    lock_guard<mutex> lock(_gMutex);
    cout << "["<<    hv_gettid()  <<"] " << buf <<endl;
}

void myprint(const char *format,...) __attribute__((format(printf,1,2)));
void myprint(const char *format,...) 
{

}

int main(int argc,char*argv[])
{
    cout << "--------------homi_test----------- " << endl;

    hlog_set_handler(disp);

    logger_enable_color(hlog,1);

    hlog_set_level(LOG_LEVEL_DEBUG);
    auto bc = homi::framework::BeanContainer::getInstance();
    
    auto redis = homi::framework::redisFactory();
    if(redis!=nullptr){
        hlogi("init sys param");
        redis->set(SYS_DEVICE_ID,"1008611");
        // redis->set(SYS_DEVICE_SECRET,"1008611");
        redis->set(SYS_SIGC_ENTRY_URL,"http://36.138.107.136:10000/robot/business/api/user/client/device/connect/url");
    }
    sleep(60);
    bc->clear();
    sleep(2);
    hlogi("end ..................");
    
    return 0;
}
