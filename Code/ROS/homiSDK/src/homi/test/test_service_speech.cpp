#include <thread>
#include <memory>
#include "inner/systemdef.h"
#include "framework/Framework.h"
#include "framework/Redis.h"
#include "framework/Mediator.h"
#include "service/WebSocketClientSIGC.h"
#include "service/EventLoopThread.h"
#include "service/SignalControlService.h"
#include "service/SpeechService.h"
#include "hv/EventLoopThread.h"
#include "hv/hlog.h"
using namespace homi;
void test1(void)
{
    auto redis = homi::framework::redisFactory();
    if(redis!=nullptr){
        hlogi("init sys param");
        redis->set(SYS_DEVICE_ID,"1008612");
        // redis->set(SYS_DEVICE_SECRET,"1008612");
        redis->set(SYS_SIGC_ENTRY_URL,"http://36.138.107.136:10000/robot/business/api/user/client/device/connect/url");
    }
    auto speechServicePtr = service::speechServiceFactory();
    for(int i=0;i<4;i++)
    {
        std::thread ([speechServicePtr,i](){
            hlogd("connect------%d------->",i);
            auto ret = speechServicePtr->connect(i*1000,"aaaa");
            hlogd("connect %d ret =%d",i,ret);
        }).detach();
    }


    std::thread ([speechServicePtr](){
        std::this_thread::sleep_for(std::chrono::seconds(5));
        hlogd("connect------AA------->");
        auto ret = speechServicePtr->connect(1000,"aaaa");
        hlogd("connect AA ret =%d",ret);
    }).detach();  
    int cnt=0;
    while (true)
    {
        std::this_thread::sleep_for(std::chrono::seconds(2));
        int ret;
        do{
            ret = speechServicePtr->connect(1000,"aaaa");
            hlogd("connect AA ret =%d",ret);
        }while(ret!=0);
        for(int i=0;i<120;i++)
        {
            ret = speechServicePtr->sendMedia(std::make_shared<std::string>("123"));
            hlogd("send AA ret =%d %d",ret,i);
        }
        hlogi("idle... %d",cnt++);
    }  
}


int main(int argc,char*argv[])
{
    hlog_set_handler(stdout_logger);
    hlog_set_format("[%t] " DEFAULT_LOG_FORMAT);
    logger_enable_color(hlog,1);
    hlog_set_level(LOG_LEVEL_DEBUG);
    test1();
    return 0;
}