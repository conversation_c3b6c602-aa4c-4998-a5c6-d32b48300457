
#include <iostream>
#include <memory>

#include "framework/Framework.h"
#include "framework/Mediator.h"
#include "framework/Redis.h"

#include "hv/hlog.h"
#include "hv/hthread.h"

using namespace std;
using namespace homi::framework;
void disp(int loglevel, const char* buf, int len)
{
    cout << "["<<    hv_gettid()  <<"] " << buf <<endl;
}




class Server:public Bean,public IColleague
{
public:
    virtual void accept(const BeanVisitorPtr_t &visitor) override
    {
        visitor->visit(shared_from_this());
    }
    virtual bool onBind(void) override
    {
        auto bc = BeanContainer::getInstance();
        auto pp = bc->getBean<Mediator>();
        _mediatorPtr = pp;
        auto pp2 = dynamic_pointer_cast<IColleague>(shared_from_this());
        if(_mediatorPtr!=nullptr)
        {
            _mediatorPtr->addColleague("server",pp2);
            return true;
        } 
        else
            return false;
    }
    virtual void colleagueSend(const BeanVisitorPtr_t &beanVisitorPtr , const std::string & to) override
    {
        if(_mediatorPtr!=nullptr)  _mediatorPtr->send(beanVisitorPtr,"server","ak");
    }
    virtual void colleagueReceive(const BeanVisitorPtr_t &beanVisitorPtr , const std::string & from) override
    {
        hlogi("receive %s",from.c_str());
        beanVisitorPtr->visit(shared_from_this());
    }
    void doSameThings(void)
    {
        hlogi("server do same things");
    }
private:
    MediatorPtr_t _mediatorPtr;
};

class Ak:public Bean,public IColleague
{
public:
    virtual void accept(const BeanVisitorPtr_t &visitor) override
    {
        visitor->visit(shared_from_this());
    }
    virtual bool onBind(void) override
    {
        auto bc = BeanContainer::getInstance();
        auto pp = bc->getBean<Mediator>();
        _mediatorPtr = pp;
        auto pp2 = dynamic_pointer_cast<IColleague>(shared_from_this());
        if(_mediatorPtr!=nullptr)
        {
            _mediatorPtr->addColleague("ak",pp2);
            return true;
        } 
        else
            return false;
    }
    virtual void colleagueSend(const BeanVisitorPtr_t &beanVisitorPtr , const std::string & to="") override
    {
        if(_mediatorPtr!=nullptr) _mediatorPtr->send(beanVisitorPtr,"ak",to);
    }
    virtual void colleagueReceive(const BeanVisitorPtr_t &beanVisitorPtr , const std::string & from) override
    {
        beanVisitorPtr->visit(shared_from_this());
    }
    void doSameThings(void)
    {
        hlogi("ak do same things");
        class opt:public BeanVisitor
        {
        public:
            opt(){
                registerVisit<Server>([](const std::shared_ptr<Server> &serverPtr){
                    serverPtr->doSameThings();
                });
            }
        };
        colleagueSend(std::make_shared<opt>(),"server");
    }
private:
    MediatorPtr_t _mediatorPtr;
};

int main(int argc,char*argv[])
{
    cout << "--------------homi_test----------- " << endl;

    hlog_set_handler(disp);

    logger_enable_color(hlog,1);

    hlog_set_level(LOG_LEVEL_DEBUG);

    hlogd("log disp");
     shared_ptr<Ak> b1 = make_shared<Ak>();
    shared_ptr<Server> b2 = make_shared<Server>();

    auto mediatorPtr = mediatorFactory();
    auto bc = BeanContainer::getInstance();
    bc->registerBean<Bean>(b2);
    bc->registerBean<Ak>(b1);
    bc->registerBean<Mediator>(mediatorPtr);
    int ret = bc->onBind();
    cout << "------------onBind = " << ret << endl;
    b1->doSameThings();
    cout << "---------------end ss-------"<<b2.use_count()<<"-------" << endl;

    return 0;
}