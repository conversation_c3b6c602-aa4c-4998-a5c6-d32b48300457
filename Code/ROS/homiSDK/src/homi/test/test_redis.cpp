#include "framework/Redis.h"
#include "gtest/gtest.h"
#include <thread>
std::shared_ptr<homi::framework::IRedis> _g;
TEST(redis, set) {
    _g->set("h1","ss",100);
    _g->set("h1","ss2",1000);
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    ASSERT_TRUE(_g->get("h1")=="ss2");
}

int main(int argc, char *argv[])
{
    testing::InitGoogleTest(&argc, argv);
    _g = homi::framework::redisFactory();
    return RUN_ALL_TESTS();
}
