project(homi_test)

function(homi_set_test_env target)
    message(STATUS "target set test env to ${target}" )
    target_include_directories(
        ${target}
        PRIVATE    
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_SOURCE_DIR}/../
        )
    target_link_libraries(
        ${target}
        PRIVATE
        homi 
        hv_static
        nlohmann_json
        gtest
        gmock
    )
endfunction()

add_executable(homi_test_framework test_framework.cpp)
homi_set_test_env(homi_test_framework)
add_test(NAME homi_test_framework COMMAND homi_test_framework)

add_executable(homi_test_service_sic test_service_sic.cpp)
homi_set_test_env(homi_test_service_sic)
add_test(NAME homi_test_service_sic COMMAND homi_test_service_sic)

if(HOMI_CONFIG_TINGS)
add_executable(homi_test_service_things test_service_things.cpp)
homi_set_test_env(homi_test_service_things)
add_test(NAME homi_test_service_things COMMAND homi_test_service_things)    
endif(HOMI_CONFIG_TINGS)

if(HOMI_CONFIG_AI_SPEECH)
add_executable(homi_test_service_speech test_service_speech.cpp)
homi_set_test_env(homi_test_service_speech)
add_test(NAME homi_test_service_speech COMMAND homi_test_service_speech)    
endif(HOMI_CONFIG_AI_SPEECH)


# add_executable(homi_test_redis test_redis.cpp)
# homi_set_test_env(homi_test_redis)
# add_test(NAME homi_test_redis COMMAND homi_test_redis)