#include <sstream>
#include "inner/systemdef.h"
#include "framework/Framework.h"
#include "framework/Redis.h"
#include "service/SignalControlService.h"
#include "service/ThingsService.h"
#include "framework/StateMachine.h"
#include "util/util.h"
#include "hv/hlog.h"
using namespace std;
using namespace homi;

void test1(void)
{
    std::string data = R"({"body":{"deviceId":"10086","service":{"cleanPause":{"inputs":""}}},"deviceId":"10086","domain":"CLEAN_DEVICE","event":"clean_service","eventId":"f93a8c3d013840fbb4d78044753de1b5","response":true,"seq":1695352094242})";
    auto json = nlohmann::json::parse(data);
    service::SIGCCmd cmd = json.get<service::SIGCCmd>();
    auto service = cmd.body["service"];
    hlogd("%s",service.items().begin().key().c_str());
    for(auto &e:service.items())
    {
        hlogd("----%s  %s",e.key().c_str(),e.value().dump().c_str());
    }
}

class Impls : public service::ISignalControlService
{
public:
    Impls(){
        _send=[](const std::shared_ptr<std::string> &dataPtr,const bool isBinary){ 
            hlogd("_send %s  size=%d ,isBinary=%d",dataPtr->c_str(),(int)dataPtr->size(),isBinary);
            return dataPtr->size();
        };
    }
    virtual bool onBind(void){return true;}
    virtual void accept(const framework::BeanVisitorPtr_t &visitor){}
    virtual int registerCallback(const std::string &domain,const std::string &event,ThreadPoolNum num,const ProcessCallback_t &fun)
    {
        SaveCallbackType_t element = std::pair<ThreadPoolNum,ProcessCallback_t>(num,fun);
        _callbacks[domain][event] = element;
        return 0;
    }
    virtual int send(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body)
    {
        service::SIGCCmd mySIGCmd;
        std::string deviceId("10086");
        if(deviceId.empty()) return -1;
        mySIGCmd.deviceId = deviceId;
        mySIGCmd.domain = domain;
        mySIGCmd.event = event;
        mySIGCmd.eventId = std::to_string(util::getGlobalID());
        mySIGCmd.seq = util::getTimestamp();
        mySIGCmd.response = response;
        mySIGCmd.body = body;
        std::string data;
        try
        {
            data = nlohmann::json(mySIGCmd).dump();
        }
        catch(const std::exception& e)
        {
            hlogw("%s",e.what());
            return -1;
        }        
        hlogd("%s",data.c_str());
        return 0;
    }
    virtual int send(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false)
    {
        return 0;
    }
    virtual int sendOOB(const std::shared_ptr<std::string> &dataPtr,const bool isBinary=false,unsigned int milliseconds = 0)
    {
        return 0;
    }
    virtual int sendOOB(const std::string &domain,const std::string &event,const bool response,const nlohmann::json &body,unsigned int milliseconds = 0)
    {
        return 0;
    }
    void runCallback(const service::SIGCCmd& myStruct)
    {
        auto callbackIter = _callbacks.find(myStruct.domain);
        if(callbackIter!=_callbacks.end())
        {
            auto &saveCallbackMap = callbackIter->second;
            auto eventIter = saveCallbackMap.find(myStruct.event);
            if(eventIter!=saveCallbackMap.end())
            {
                auto element = eventIter->second;
                hloge("(%s)-(%s) no thread pool match",myStruct.domain.c_str(),myStruct.event.c_str());
                element.second(myStruct,_send);
            }
        }
    }
    using SaveCallbackType_t = std::pair<ThreadPoolNum,ProcessCallback_t>;
    std::unordered_map<std::string,std::unordered_map<std::string, SaveCallbackType_t>> _callbacks;
    SendCallback_t _send;
};

void test2()
{
    auto bc = framework::BeanContainer::getInstance();
    auto s = std::make_shared<Impls>();
    bc->registerBean<service::ISignalControlService>(s);
    auto p = service::thingsServiceFactory();
    std::string data = R"({"body":{"deviceId":"10086","service":{"cleanPause":{"inputs":{"ss":1}}}},"deviceId":"10086","domain":"CLEAN_DEVICE","event":"clean_service","eventId":"f93a8c3d013840fbb4d78044753de1b5","response":true,"seq":1695352094242})";
    auto json = nlohmann::json::parse(data);
    service::SIGCCmd cmd = json.get<service::SIGCCmd>();
    p->registerServiceCallback("cleanPause",[](const std::string &input,std::string &output){
        hlogd("cleanPause ->input=%s ",input.c_str());
        //output["runingMode"] = to_string(2);
        std::stringstream ss;
        ss << R"({ ")" << "runingMode" << R"(":")" << to_string(2) << R"("})";
        //output = ss.str();
    });
    s->runCallback(cmd);
    data = R"({"body":{"properties":["runningMode","power"]},"deviceId":"10086","domain":"CLEAN_DEVICE","event":"properties_read","eventId":"bd8f1c4184c440cf84331a5356069920","response":true,"seq":1695352097756})";
    json = nlohmann::json::parse(data);
    cmd = json.get<service::SIGCCmd>();
    p->registerPropertiesReadCallback("runningMode",[](const std::string &input,std::string &output){
        hlogd("runningMode ->input=%s ",input.c_str());
        //output["runingMode"] = to_string(3);
        std::stringstream ss;
        ss << R"({ ")" << "runingMode" << R"(":")" << to_string(3) << R"("})";
        output = ss.str();
    });
    p->registerPropertiesReadCallback("power",[](const std::string &input,std::string &output){
        hlogd("power ->input=%s ",input.c_str());
        //output["power"] = to_string(30);
        std::stringstream ss;
        ss << R"({ ")" << "power" << R"(":)" << 3 << R"(})";
        output = ss.str();
    });
    s->runCallback(cmd);
    p->sendEvent("cleanRecord",R"({"cleaningArea":200,"costTime":3})");
}

int main(int argc,char*argv[])
{
    hlog_set_handler(stdout_logger);
    hlog_set_format("[%t] " DEFAULT_LOG_FORMAT);
    logger_enable_color(hlog,1);
    hlog_set_level(LOG_LEVEL_DEBUG);
    test2();
    return 0;
}