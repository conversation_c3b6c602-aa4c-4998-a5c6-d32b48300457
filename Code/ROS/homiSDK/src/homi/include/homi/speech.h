#ifndef __SRC_HOMI_INCLUDE_HOMI_SPEECH_H_
#define __SRC_HOMI_INCLUDE_HOMI_SPEECH_H_

#ifdef __cplusplus
extern "C" {
#endif

//语音媒体通道的回调函数
typedef void (*speechMediumCallback_t)(const char *buf,unsigned int len);

//语音媒体通道的状态回调函数，status = -1 表示断网，speechMediumConnect之后，如果调用speechMediumClose 或者中途断网，会触发该回调
typedef void (*speechMediumStatusCallback_t)(const int status);

//语音信令通道的回调函数
typedef void (*speechCmdCallback_t)(const char *buf,unsigned int len);

//建立语音媒体通道连接，阻塞等待到通道建立或超时，
//timeout:以毫秒为单位, 0:表示成功，-1表示未知异常导致的失败
//token：token内容
int speechMediumConnect(int timeout,const char *token);

//关闭语音媒体通道连接，非阻塞，0:表示成功，-1表示未知异常导致的失败
int speechMediumClose(void);

// -1 表示发送失败，有可能通道未建立。大于0表示发送的长度
int speechMediumSend(const char *buf,unsigned int len);

// 在媒体通道上，发送VAD信号。返回值-1表示发送异常，0表示成功
int speechMediumVAD(void);

// 注册语音媒体通道回调函数，0：表示成功，-1表示未知异常导致的失败
int speechMediumCallback(speechMediumCallback_t func);

// 注册语音媒体通道asr回调函数，0：表示成功，-1表示未知异常导致的失败
int speechMediumAsrCallback(speechMediumCallback_t func);

// 注册语音媒体通道asr end回调函数，0：表示成功，-1表示未知异常导致的失败
int speechMediumAsrEndCallback(speechMediumCallback_t func);

// 注册语音媒体通道的状态回调函数，当前仅支持 status = -1
// status = -1 表示断网，speechMediumConnect之后，如果调用speechMediumClose 或者中途断网，会触发该回调
int speechMediumStatusCallback(speechMediumStatusCallback_t func);

// -1 表示发送失败，网络影响或缓冲区满没有取出等异常情况。大于0表示发送的长度
int speechCmdSend(const char *buf,unsigned int len);

// 注册语音信令通道回调函数，0：表示成功，-1表示未知异常导致的失败
int speechCmdCallback(speechCmdCallback_t func);

#ifdef __cplusplus
}
#endif

#endif  // __SRC_HOMI_INCLUDE_HOMI_SPEECH_H_
