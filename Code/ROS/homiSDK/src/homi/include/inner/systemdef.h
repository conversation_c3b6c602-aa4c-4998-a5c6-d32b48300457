#ifndef __SRC_HOMI_INCLUDE_INNER_SYSTEMDEF_H_
#define __SRC_HOMI_INCLUDE_INNER_SYSTEMDEF_H_

#ifdef __cplusplus
extern "C" {
#endif

#define SYS_DEVICE_ID       "sys_device_id"
//#define SYS_DEVICE_SECRET   "sys_device_secret"
//#define SYS_DEVICE_MACID    "sys_device_macId"
//#define SYS_DEVICE_TYPE     "sys_device_type"
#define SYS_SIGC_ENTRY_URL  "sys_sigc_entry_url"
//#define SYS_FIRMWARE        "sys_firmware"
#define SYS_INIT_PARAMS     "sys_init_params"


#define BEAN_REDIS_MAIN     "bean_redis_main"
#define BEAN_EVENTLOOPTHREAD_MAIN     "bean_eventloopthread_main"

#define SERVICE_DEF         "service_def_"

#define MEDIATOR_SERVICE_SIGC        "mediator_service_sigc"
#define MEDIATOR_SERVICE_SPEECH      "mediator_service_speech"
#ifdef __cplusplus
}
#endif

#endif  // __SRC_HOMI_INCLUDE_INNER_SYSTEMDEF_H_
