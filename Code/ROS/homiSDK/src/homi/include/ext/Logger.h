#ifndef __SRC_HOMI_INCLUDE_HOMI_LOGGER_H_
#define __SRC_HOMI_INCLUDE_HOMI_LOGGER_H_

#include <string.h>

extern "C" {
}
namespace homi
{
    enum class LogLevel{
        LOG_LEVEL_TRACE,
        LOG_LEVEL_DEBUG,
        LOG_LEVEL_INFO,
        LOG_LEVEL_WARN,
        LOG_LEVEL_ERROR,
        LOG_LEVEL_FATAL
    } ;
}


#define loggerD(fmt, ...) 
#define loggerI(fmt, ...) 
#define loggerW(fmt, ...) 
#define loggerE(fmt, ...) 
#define loggerF(fmt, ...) 


#endif  // __SRC_HOMI_INCLUDE_HOMI_LOGGER_H_