function(homi_git_checkout repository tag)
    message(STATUS "checkout ${repository} : ${tag}")
    if(NOT EXISTS ${repository}/.git)
        message(FATAL_ERROR "${repository}.git is not exists !")        
    endif()
    execute_process(
        COMMAND git clean -dfx
        WORKING_DIRECTORY ${repository}
    )
    execute_process(
        COMMAND git checkout .
        WORKING_DIRECTORY ${repository}
    )
    execute_process(
        COMMAND git checkout ${tag}
        WORKING_DIRECTORY ${repository}
    )
endfunction()