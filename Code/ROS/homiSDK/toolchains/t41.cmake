set(CMAKE_SYSTEM_NAME Linux)

set(TOOLCHAIN_PATH /home/<USER>/opt/toolchains/mips-linux-gnu-ingenic-gcc7.2.0-uclibc0.9.33.2-fp64-r5.1.8)
set(CMAKE_C_COMPILER ${TOOLCHAIN_PATH}/bin/mips-linux-uclibc-gcc)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_PATH}/bin/mips-linux-uclibc-g++)
# add_compile_options("-Wno-psabi")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -muclibc -latomic ")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -muclibc -latomic ")