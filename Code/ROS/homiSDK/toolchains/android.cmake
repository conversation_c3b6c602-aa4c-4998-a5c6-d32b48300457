set(ANDROID_NDK /home/<USER>/opt/android-ndk-r26b) # 指定 NDK 路径
set(CMAKE_ANDROID_NDK /home/<USER>/opt/android-ndk-r26b) # 指定 NDK 路径
set(CMAKE_SYSTEM_NAME Android) # 设置系统名称为 Android
set(CMAKE_SYSTEM_VERSION 21) # 设置系统版本为 Android API level 21
set(CMAKE_ANDROID_ARCH_ABI arm64-v8a) # 设置 ABI 架构为 arm64-v8a
set(CMAKE_ANDROID_STL_TYPE c++_static) # 设置 STL 类型为 c++_static


set(CMAKE_C_COMPILER ${ANDROID_NDK}/toolchains/llvm/prebuilt/linux-x86_64/bin/aarch64-linux-android21-clang)
set(CMAKE_CXX_COMPILER ${ANDROID_NDK}/toolchains/llvm/prebuilt/linux-x86_64/bin/aarch64-linux-android21-clang++)
