set(CMAKE_SYSTEM_NAME Linux)

set(TOOLCHAIN_PATH /home/<USER>/opt/toolchains/gcc-buildroot-9.3.0-2020.03-x86_64_arm-linux-gnueabihf)
set(CMAKE_C_COMPILER ${TOOLCHAIN_PATH}/bin/arm-linux-gcc)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_PATH}/bin/arm-linux-g++)
add_compile_options("-Wno-psabi")
# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -muclibc -latomic ")
# set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -muclibc -latomic ")