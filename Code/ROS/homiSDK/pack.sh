#!/bin/bash

WORK_DIR=$(cd `dirname $0`; pwd)
# 读取version.h文件内容
version_file=${WORK_DIR}/build/homi/include/homi/version.h
version_info=$(cat $version_file)
# 使用正则表达式匹配版本号信息
major_version=$(echo "$version_info" | grep -oP '(?<=PROJECT_VERSION_MAJOR ")[^"]+')
minor_version=$(echo "$version_info" | grep -oP '(?<=PROJECT_VERSION_MINOR ")[^"]+')
patch_version=$(echo "$version_info" | grep -oP '(?<=PROJECT_VERSION_PATCH ")[^"]+')
commit_id=$(echo "$version_info" | grep -oP '(?<=PROJECT_VERSION_COMMIT_ID ")[^"]+')

# 输出版本号
version=${major_version}_${minor_version}_${patch_version}_${commit_id}
echo "Version: $version"

cd ${WORK_DIR}/out &&
rm -f ./homiSDK_${version}.tar 
tar -cvf homiSDK_${version}.tar ./include/homi/* ./example/* ./lib/libhomi.a ./lib/libhv_static.a ./lib/libmbedcrypto.a ./lib/libmbedtls.a ./lib/libmbedx509.a 
# mkdir ./out &&
# mkdir ./out/include &&
# mkdir ./out/lib &&
# cp  -r ./include/homi ./out/include &&
# cp ./lib/libhomi.a ./out/lib/ &&
# cp ./lib/libhv_static.a ./out/lib/ &&
# cp ./lib/libmbedcrypto.a ./out/lib/ &&
# cp ./lib/libmbedtls.a ./out/lib/ &&
# cp ./lib/libmbedx509.a ./out/lib/ &&
# cp  -r ./example ./out/ 