#!/bin/bash

WORK_DIR=$(cd `dirname $0`; pwd)
toolchain=""
operation="config"
def=""
cmakeoption="-DHOMI_SDK_OUT_DIR=${WORK_DIR}/out "
while getopts ":d:t:e:" opt; do
  case ${opt} in
    d ) # process option t
        def=$OPTARG
      ;;
    t ) # process option t
        toolchain=$OPTARG
      ;;
    e ) # process option e
        operation=$OPTARG
      ;;    
    \? ) echo "Usage: cmd [-e] [-t]"
      ;;
  esac
done
shift $((OPTIND -1))

config(){
    if [ -f "$toolchain" ];
    then 
    cmakeoption="$cmakeoption -DCMAKE_TOOLCHAIN_FILE=$(realpath "$toolchain") $def"
    echo "使用 $toolchain 工具链进行编译,添加cmake选项."  
    fi 
    echo $cmakeoption
    cd ${WORK_DIR} &&
    rm -rf ./build &&
    rm -rf ./out &&
    mkdir out &&
    mkdir build &&
    cd build &&
    cmake $cmakeoption $def ../src
}

main(){
    case $1 in
    "config")
        echo "执行 $operation 操作" 
        config;;
    *)
	    echo "未定义操作"
    ;;
    esac
}

main $operation