<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Using JIT Debug Info Readers</title>

<meta name="description" content="Debugging with GDB: Using JIT Debug Info Readers">
<meta name="keywords" content="Debugging with GDB: Using JIT Debug Info Readers">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Custom-Debug-Info.html#Custom-Debug-Info" rel="up" title="Custom Debug Info">
<link href="Writing-JIT-Debug-Info-Readers.html#Writing-JIT-Debug-Info-Readers" rel="next" title="Writing JIT Debug Info Readers">
<link href="Custom-Debug-Info.html#Custom-Debug-Info" rel="previous" title="Custom Debug Info">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Using-JIT-Debug-Info-Readers"></a>
<div class="header">
<p>
Next: <a href="Writing-JIT-Debug-Info-Readers.html#Writing-JIT-Debug-Info-Readers" accesskey="n" rel="next">Writing JIT Debug Info Readers</a>, Up: <a href="Custom-Debug-Info.html#Custom-Debug-Info" accesskey="u" rel="up">Custom Debug Info</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Using-JIT-Debug-Info-Readers-1"></a>
<h4 class="subsection">29.4.1 Using JIT Debug Info Readers</h4>
<a name="index-jit_002dreader_002dload"></a>
<a name="index-jit_002dreader_002dunload"></a>

<p>Readers can be loaded and unloaded using the <code>jit-reader-load</code>
and <code>jit-reader-unload</code> commands.
</p>
<dl compact="compact">
<dt><code>jit-reader-load <var>reader</var></code></dt>
<dd><p>Load the JIT reader named <var>reader</var>, which is a shared
object specified as either an absolute or a relative file name.  In
the latter case, <small>GDB</small> will try to load the reader from a
pre-configured directory, usually <samp><var>libdir</var>/gdb/</samp> on a UNIX
system (here <var>libdir</var> is the system library directory, often
<samp>/usr/local/lib</samp>).
</p>
<p>Only one reader can be active at a time; trying to load a second
reader when one is already loaded will result in <small>GDB</small>
reporting an error.  A new JIT reader can be loaded by first unloading
the current one using <code>jit-reader-unload</code> and then invoking
<code>jit-reader-load</code>.
</p>
</dd>
<dt><code>jit-reader-unload</code></dt>
<dd><p>Unload the currently loaded JIT reader.
</p>
</dd>
</dl>




</body>
</html>
