<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Writing a Guile Pretty-Printer</title>

<meta name="description" content="Debugging with GDB: Writing a Guile Pretty-Printer">
<meta name="keywords" content="Debugging with GDB: Writing a Guile Pretty-Printer">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Guile-API.html#Guile-API" rel="up" title="Guile API">
<link href="Commands-In-Guile.html#Commands-In-Guile" rel="next" title="Commands In Guile">
<link href="Selecting-Guile-Pretty_002dPrinters.html#Selecting-Guile-Pretty_002dPrinters" rel="previous" title="Selecting Guile Pretty-Printers">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Writing-a-Guile-Pretty_002dPrinter"></a>
<div class="header">
<p>
Next: <a href="Commands-In-Guile.html#Commands-In-Guile" accesskey="n" rel="next">Commands In Guile</a>, Previous: <a href="Selecting-Guile-Pretty_002dPrinters.html#Selecting-Guile-Pretty_002dPrinters" accesskey="p" rel="previous">Selecting Guile Pretty-Printers</a>, Up: <a href="Guile-API.html#Guile-API" accesskey="u" rel="up">Guile API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Writing-a-Guile-Pretty_002dPrinter-1"></a>
<h4 class="subsubsection">23.3.3.10 Writing a Guile Pretty-Printer</h4>
<a name="index-writing-a-Guile-pretty_002dprinter"></a>

<p>A pretty-printer consists of two basic parts: a lookup function to determine
if the type is supported, and the printer itself.
</p>
<p>Here is an example showing how a <code>std::string</code> printer might be
written.  See <a href="Guile-Pretty-Printing-API.html#Guile-Pretty-Printing-API">Guile Pretty Printing API</a>, for details.
</p>
<div class="smallexample">
<pre class="smallexample">(define (make-my-string-printer value)
  &quot;Print a my::string string&quot;
  (make-pretty-printer-worker
   &quot;string&quot;
   (lambda (printer)
     (value-field value &quot;_data&quot;))
   #f))
</pre></div>

<p>And here is an example showing how a lookup function for the printer
example above might be written.
</p>
<div class="smallexample">
<pre class="smallexample">(define (str-lookup-function pretty-printer value)
  (let ((tag (type-tag (value-type value))))
    (and tag
         (string-prefix? &quot;std::string&lt;&quot; tag)
         (make-my-string-printer value))))
</pre></div>

<p>Then to register this printer in the global printer list:
</p>
<div class="smallexample">
<pre class="smallexample">(append-pretty-printer!
 (make-pretty-printer &quot;my-string&quot; str-lookup-function))
</pre></div>

<p>The example lookup function extracts the value&rsquo;s type, and attempts to
match it to a type that it can pretty-print.  If it is a type the
printer can pretty-print, it will return a &lt;gdb:pretty-printer-worker&gt; object.
If not, it returns <code>#f</code>.
</p>
<p>We recommend that you put your core pretty-printers into a Guile
package.  If your pretty-printers are for use with a library, we
further recommend embedding a version number into the package name.
This practice will enable <small>GDB</small> to load multiple versions of
your pretty-printers at the same time, because they will have
different names.
</p>
<p>You should write auto-loaded code (see <a href="Guile-Auto_002dloading.html#Guile-Auto_002dloading">Guile Auto-loading</a>) such that it
can be evaluated multiple times without changing its meaning.  An
ideal auto-load file will consist solely of <code>import</code>s of your
printer modules, followed by a call to a register pretty-printers with
the current objfile.
</p>
<p>Taken as a whole, this approach will scale nicely to multiple
inferiors, each potentially using a different library version.
Embedding a version number in the Guile package name will ensure that
<small>GDB</small> is able to load both sets of printers simultaneously.
Then, because the search for pretty-printers is done by objfile, and
because your auto-loaded code took care to register your library&rsquo;s
printers with a specific objfile, <small>GDB</small> will find the correct
printers for the specific version of the library used by each
inferior.
</p>
<p>To continue the <code>my::string</code> example,
this code might appear in <code>(my-project my-library v1)</code>:
</p>
<div class="smallexample">
<pre class="smallexample">(use-modules (gdb))
(define (register-printers objfile)
  (append-objfile-pretty-printer!
   (make-pretty-printer &quot;my-string&quot; str-lookup-function)))
</pre></div>

<p>And then the corresponding contents of the auto-load file would be:
</p>
<div class="smallexample">
<pre class="smallexample">(use-modules (gdb) (my-project my-library v1))
(register-printers (current-objfile))
</pre></div>

<p>The previous example illustrates a basic pretty-printer.
There are a few things that can be improved on.
The printer only handles one type, whereas a library typically has
several types.  One could install a lookup function for each desired type
in the library, but one could also have a single lookup function recognize
several types.  The latter is the conventional way this is handled.
If a pretty-printer can handle multiple data types, then its
<em>subprinters</em> are the printers for the individual data types.
</p>
<p>The <code>(gdb printing)</code> module provides a formal way of solving this
problem (see <a href="Guile-Printing-Module.html#Guile-Printing-Module">Guile Printing Module</a>).
Here is another example that handles multiple types.
</p>
<p>These are the types we are going to pretty-print:
</p>
<div class="smallexample">
<pre class="smallexample">struct foo { int a, b; };
struct bar { struct foo x, y; };
</pre></div>

<p>Here are the printers:
</p>
<div class="smallexample">
<pre class="smallexample">(define (make-foo-printer value)
  &quot;Print a foo object&quot;
  (make-pretty-printer-worker
   &quot;foo&quot;
   (lambda (printer)
     (format #f &quot;a=&lt;~a&gt; b=&lt;~a&gt;&quot;
             (value-field value &quot;a&quot;) (value-field value &quot;a&quot;)))
   #f))

(define (make-bar-printer value)
  &quot;Print a bar object&quot;
  (make-pretty-printer-worker
   &quot;foo&quot;
   (lambda (printer)
     (format #f &quot;x=&lt;~a&gt; y=&lt;~a&gt;&quot;
             (value-field value &quot;x&quot;) (value-field value &quot;y&quot;)))
   #f))
</pre></div>

<p>This example doesn&rsquo;t need a lookup function, that is handled by the
<code>(gdb printing)</code> module.  Instead a function is provided to build up
the object that handles the lookup.
</p>
<div class="smallexample">
<pre class="smallexample">(use-modules (gdb printing))

(define (build-pretty-printer)
  (let ((pp (make-pretty-printer-collection &quot;my-library&quot;)))
    (pp-collection-add-tag-printer &quot;foo&quot; make-foo-printer)
    (pp-collection-add-tag-printer &quot;bar&quot; make-bar-printer)
    pp))
</pre></div>

<p>And here is the autoload support:
</p>
<div class="smallexample">
<pre class="smallexample">(use-modules (gdb) (my-library))
(append-objfile-pretty-printer! (current-objfile) (build-pretty-printer))
</pre></div>

<p>Finally, when this printer is loaded into <small>GDB</small>, here is the
corresponding output of &lsquo;<samp>info pretty-printer</samp>&rsquo;:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) info pretty-printer
my_library.so:
  my-library
    foo
    bar
</pre></div>

<hr>
<div class="header">
<p>
Next: <a href="Commands-In-Guile.html#Commands-In-Guile" accesskey="n" rel="next">Commands In Guile</a>, Previous: <a href="Selecting-Guile-Pretty_002dPrinters.html#Selecting-Guile-Pretty_002dPrinters" accesskey="p" rel="previous">Selecting Guile Pretty-Printers</a>, Up: <a href="Guile-API.html#Guile-API" accesskey="u" rel="up">Guile API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
