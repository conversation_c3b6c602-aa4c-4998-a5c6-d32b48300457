<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Target Descriptions</title>

<meta name="description" content="Debugging with GDB: Target Descriptions">
<meta name="keywords" content="Debugging with GDB: Target Descriptions">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Retrieving-Descriptions.html#Retrieving-Descriptions" rel="next" title="Retrieving Descriptions">
<link href="Rationale.html#Rationale" rel="previous" title="Rationale">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Target-Descriptions"></a>
<div class="header">
<p>
Next: <a href="Operating-System-Information.html#Operating-System-Information" accesskey="n" rel="next">Operating System Information</a>, Previous: <a href="Agent-Expressions.html#Agent-Expressions" accesskey="p" rel="previous">Agent Expressions</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Target-Descriptions-1"></a>
<h2 class="appendix">Appendix G Target Descriptions</h2>
<a name="index-target-descriptions"></a>

<p>One of the challenges of using <small>GDB</small> to debug embedded systems
is that there are so many minor variants of each processor
architecture in use.  It is common practice for vendors to start with
a standard processor core &mdash; ARM, PowerPC, or <acronym>MIPS</acronym>, for example &mdash;
and then make changes to adapt it to a particular market niche.  Some
architectures have hundreds of variants, available from dozens of
vendors.  This leads to a number of problems:
</p>
<ul>
<li> With so many different customized processors, it is difficult for
the <small>GDB</small> maintainers to keep up with the changes.
</li><li> Since individual variants may have short lifetimes or limited
audiences, it may not be worthwhile to carry information about every
variant in the <small>GDB</small> source tree.
</li><li> When <small>GDB</small> does support the architecture of the embedded system
at hand, the task of finding the correct architecture name to give the
<code>set architecture</code> command can be error-prone.
</li></ul>

<p>To address these problems, the <small>GDB</small> remote protocol allows a
target system to not only identify itself to <small>GDB</small>, but to
actually describe its own features.  This lets <small>GDB</small> support
processor variants it has never seen before &mdash; to the extent that the
descriptions are accurate, and that <small>GDB</small> understands them.
</p>
<p><small>GDB</small> must be linked with the Expat library to support XML
target descriptions.  See <a href="Requirements.html#Expat">Expat</a>.
</p>
<table class="menu" border="0" cellspacing="0">
<tr><td align="left" valign="top">&bull; <a href="Retrieving-Descriptions.html#Retrieving-Descriptions" accesskey="1">Retrieving Descriptions</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">How descriptions are fetched from a target.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Target-Description-Format.html#Target-Description-Format" accesskey="2">Target Description Format</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">The contents of a target description.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Predefined-Target-Types.html#Predefined-Target-Types" accesskey="3">Predefined Target Types</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Standard types available for target
                                    descriptions.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Enum-Target-Types.html#Enum-Target-Types" accesskey="4">Enum Target Types</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">How to define enum target types.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Standard-Target-Features.html#Standard-Target-Features" accesskey="5">Standard Target Features</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Features <small>GDB</small> knows about.
</td></tr>
</table>

<hr>
<div class="header">
<p>
Next: <a href="Operating-System-Information.html#Operating-System-Information" accesskey="n" rel="next">Operating System Information</a>, Previous: <a href="Agent-Expressions.html#Agent-Expressions" accesskey="p" rel="previous">Agent Expressions</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
