<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: errno Values</title>

<meta name="description" content="Debugging with GDB: errno Values">
<meta name="keywords" content="Debugging with GDB: errno Values">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Constants.html#Constants" rel="up" title="Constants">
<link href="Lseek-Flags.html#Lseek-Flags" rel="next" title="Lseek Flags">
<link href="mode_005ft-Values.html#mode_005ft-Values" rel="previous" title="mode_t Values">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="errno-Values"></a>
<div class="header">
<p>
Next: <a href="Lseek-Flags.html#Lseek-Flags" accesskey="n" rel="next">Lseek Flags</a>, Previous: <a href="mode_005ft-Values.html#mode_005ft-Values" accesskey="p" rel="previous">mode_t Values</a>, Up: <a href="Constants.html#Constants" accesskey="u" rel="up">Constants</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="errno-Values-1"></a>
<h4 class="unnumberedsubsubsec">errno Values</h4>
<a name="index-errno-values_002c-in-file_002di_002fo-protocol"></a>

<p>All values are given in decimal representation.
</p>
<div class="smallexample">
<pre class="smallexample">  EPERM           1
  ENOENT          2
  EINTR           4
  EBADF           9
  EACCES         13
  EFAULT         14
  EBUSY          16
  EEXIST         17
  ENODEV         19
  ENOTDIR        20
  EISDIR         21
  EINVAL         22
  ENFILE         23
  EMFILE         24
  EFBIG          27
  ENOSPC         28
  ESPIPE         29
  EROFS          30
  ENAMETOOLONG   91
  EUNKNOWN       9999
</pre></div>

<p><code>EUNKNOWN</code> is used as a fallback error value if a host system returns
  any error value not in the list of supported error numbers.
</p>



</body>
</html>
