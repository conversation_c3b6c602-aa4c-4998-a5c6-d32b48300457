<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Top</title>

<meta name="description" content="Debugging with GDB: Top">
<meta name="keywords" content="Debugging with GDB: Top">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Summary.html#Summary" rel="next" title="Summary">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<h1 class="settitle" align="center">Debugging with <small>GDB</small></h1>









<p>This file documents the <small>GNU</small> debugger <small>GDB</small>.
</p>
<p>This is the Tenth Edition, of <cite>Debugging with
<small>GDB</small>: the <small>GNU</small> Source-Level Debugger</cite> for <small>GDB</small>
(GNU Toolchain for the A-profile Architecture 10.3-2021.07 (arm-10.29))
Version 10.2.90.20210621-git.
</p>
<p>Copyright &copy; 1988-2021 Free Software Foundation, Inc.
</p>
<p>Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being &ldquo;Free Software&rdquo; and &ldquo;Free Software Needs
Free Documentation&rdquo;, with the Front-Cover Texts being &ldquo;A GNU Manual,&rdquo;
and with the Back-Cover Texts as in (a) below.
</p>
<p>(a) The FSF&rsquo;s Back-Cover Text is: &ldquo;You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom.&rdquo;
</p>


<a name="Top"></a>
<div class="header">
<p>
Next: <a href="Summary.html#Summary" accesskey="n" rel="next">Summary</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>

<a name="Debugging-with-GDB"></a>
<h1 class="top">Debugging with <small>GDB</small></h1>

<p>This file describes <small>GDB</small>, the <small>GNU</small> symbolic debugger.
</p>
<p>This is the Tenth Edition, for <small>GDB</small>
(GNU Toolchain for the A-profile Architecture 10.3-2021.07 (arm-10.29))
Version 10.2.90.20210621-git.
</p>
<p>Copyright (C) 1988-2021 Free Software Foundation, Inc.
</p>
<p>This edition of the GDB manual is dedicated to the memory of Fred
Fish.  Fred was a long-standing contributor to GDB and to Free
software in general.  We will miss him.
</p>
<table class="menu" border="0" cellspacing="0">
<tr><td align="left" valign="top">&bull; <a href="Summary.html#Summary" accesskey="1">Summary</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Summary of <small>GDB</small>
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Sample-Session.html#Sample-Session" accesskey="2">Sample Session</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">A sample <small>GDB</small> session
</td></tr>
<tr><th colspan="3" align="left" valign="top"><pre class="menu-comment">
</pre></th></tr><tr><td align="left" valign="top">&bull; <a href="Invocation.html#Invocation" accesskey="3">Invocation</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Getting in and out of <small>GDB</small>
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Commands.html#Commands" accesskey="4">Commands</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top"><small>GDB</small> commands
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Running.html#Running" accesskey="5">Running</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Running programs under <small>GDB</small>
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Stopping.html#Stopping" accesskey="6">Stopping</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Stopping and continuing
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Reverse-Execution.html#Reverse-Execution" accesskey="7">Reverse Execution</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Running programs backward
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Process-Record-and-Replay.html#Process-Record-and-Replay" accesskey="8">Process Record and Replay</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Recording inferior&rsquo;s execution and replaying it
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Stack.html#Stack" accesskey="9">Stack</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Examining the stack
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Source.html#Source">Source</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Examining source files
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Data.html#Data">Data</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Examining data
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Optimized-Code.html#Optimized-Code">Optimized Code</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Debugging optimized code
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Macros.html#Macros">Macros</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Preprocessor Macros
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Tracepoints.html#Tracepoints">Tracepoints</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Debugging remote targets non-intrusively
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Overlays.html#Overlays">Overlays</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Debugging programs that use overlays
</td></tr>
<tr><th colspan="3" align="left" valign="top"><pre class="menu-comment">
</pre></th></tr><tr><td align="left" valign="top">&bull; <a href="Languages.html#Languages">Languages</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Using <small>GDB</small> with different languages
</td></tr>
<tr><th colspan="3" align="left" valign="top"><pre class="menu-comment">
</pre></th></tr><tr><td align="left" valign="top">&bull; <a href="Symbols.html#Symbols">Symbols</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Examining the symbol table
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Altering.html#Altering">Altering</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Altering execution
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="GDB-Files.html#GDB-Files">GDB Files</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top"><small>GDB</small> files
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Targets.html#Targets">Targets</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Specifying a debugging target
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Remote-Debugging.html#Remote-Debugging">Remote Debugging</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Debugging remote programs
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Configurations.html#Configurations">Configurations</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Configuration-specific information
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Controlling-GDB.html#Controlling-GDB">Controlling GDB</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Controlling <small>GDB</small>
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Extending-GDB.html#Extending-GDB">Extending GDB</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Extending <small>GDB</small>
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Interpreters.html#Interpreters">Interpreters</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Command Interpreters
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="TUI.html#TUI">TUI</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top"><small>GDB</small> Text User Interface
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Emacs.html#Emacs">Emacs</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Using <small>GDB</small> under <small>GNU</small> Emacs
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="GDB_002fMI.html#GDB_002fMI">GDB/MI</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top"><small>GDB</small>&rsquo;s Machine Interface.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Annotations.html#Annotations">Annotations</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top"><small>GDB</small>&rsquo;s annotation interface.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="JIT-Interface.html#JIT-Interface">JIT Interface</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Using the JIT debugging interface.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="In_002dprocess-Agent.html#In_002dprocess-Agent">In-process Agent</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">In-process Agent
</td></tr>
<tr><th colspan="3" align="left" valign="top"><pre class="menu-comment">
</pre></th></tr><tr><td align="left" valign="top">&bull; <a href="GDB-Bugs.html#GDB-Bugs">GDB Bugs</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Reporting bugs in <small>GDB</small>
</td></tr>
<tr><th colspan="3" align="left" valign="top"><pre class="menu-comment">
</pre></th></tr><tr><td align="left" valign="top">&bull; <a href="Command-Line-Editing.html#Command-Line-Editing">Command Line Editing</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Command Line Editing
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Using-History-Interactively.html#Using-History-Interactively">Using History Interactively</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Using History Interactively
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="In-Memoriam.html#In-Memoriam">In Memoriam</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">In Memoriam
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Formatting-Documentation.html#Formatting-Documentation">Formatting Documentation</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">How to format and print <small>GDB</small> documentation
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Installing-GDB.html#Installing-GDB">Installing GDB</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Installing GDB
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Maintenance-Commands.html#Maintenance-Commands">Maintenance Commands</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Maintenance Commands
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Remote-Protocol.html#Remote-Protocol">Remote Protocol</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">GDB Remote Serial Protocol
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Agent-Expressions.html#Agent-Expressions">Agent Expressions</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">The GDB Agent Expression Mechanism
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Target-Descriptions.html#Target-Descriptions">Target Descriptions</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">How targets can describe themselves to
                                <small>GDB</small>
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Operating-System-Information.html#Operating-System-Information">Operating System Information</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Getting additional information from
                                 the operating system
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Trace-File-Format.html#Trace-File-Format">Trace File Format</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">GDB trace file format
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Index-Section-Format.html#Index-Section-Format">Index Section Format</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">.gdb_index section format
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Man-Pages.html#Man-Pages">Man Pages</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Manual pages
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Copying.html#Copying">Copying</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">GNU General Public License says
                                how you can copy and share GDB
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="GNU-Free-Documentation-License.html#GNU-Free-Documentation-License">GNU Free Documentation License</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">The license for this documentation
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Concept-Index.html#Concept-Index">Concept Index</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Index of <small>GDB</small> concepts
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Command-and-Variable-Index.html#Command-and-Variable-Index">Command and Variable Index</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Index of <small>GDB</small> commands, variables,
                                  functions, and Python data types
</td></tr>
</table>


<a name="SEC_Contents"></a>
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a name="toc-Summary-of-GDB" href="Summary.html#Summary">Summary of <small>GDB</small></a>
  <ul class="no-bullet">
    <li><a name="toc-Free-Software-1" href="Free-Software.html#Free-Software">Free Software</a></li>
    <li><a name="toc-Free-Software-Needs-Free-Documentation" href="Free-Documentation.html#Free-Documentation">Free Software Needs Free Documentation</a></li>
    <li><a name="toc-Contributors-to-GDB" href="Contributors.html#Contributors">Contributors to <small>GDB</small></a></li>
  </ul></li>
  <li><a name="toc-A-Sample-GDB-Session" href="Sample-Session.html#Sample-Session">1 A Sample <small>GDB</small> Session</a></li>
  <li><a name="toc-Getting-In-and-Out-of-GDB" href="Invocation.html#Invocation">2 Getting In and Out of <small>GDB</small></a>
  <ul class="no-bullet">
    <li><a name="toc-Invoking-GDB-1" href="Invoking-GDB.html#Invoking-GDB">2.1 Invoking <small>GDB</small></a>
    <ul class="no-bullet">
      <li><a name="toc-Choosing-Files" href="File-Options.html#File-Options">2.1.1 Choosing Files</a></li>
      <li><a name="toc-Choosing-Modes" href="Mode-Options.html#Mode-Options">2.1.2 Choosing Modes</a></li>
      <li><a name="toc-What-GDB-Does-during-Startup" href="Startup.html#Startup">2.1.3 What <small>GDB</small> Does during Startup</a></li>
    </ul></li>
    <li><a name="toc-Quitting-GDB-1" href="Quitting-GDB.html#Quitting-GDB">2.2 Quitting <small>GDB</small></a></li>
    <li><a name="toc-Shell-Commands-1" href="Shell-Commands.html#Shell-Commands">2.3 Shell Commands</a></li>
    <li><a name="toc-Logging-Output-1" href="Logging-Output.html#Logging-Output">2.4 Logging Output</a></li>
  </ul></li>
  <li><a name="toc-GDB-Commands" href="Commands.html#Commands">3 <small>GDB</small> Commands</a>
  <ul class="no-bullet">
    <li><a name="toc-Command-Syntax-1" href="Command-Syntax.html#Command-Syntax">3.1 Command Syntax</a></li>
    <li><a name="toc-Command-Settings-1" href="Command-Settings.html#Command-Settings">3.2 Command Settings</a></li>
    <li><a name="toc-Command-Completion" href="Completion.html#Completion">3.3 Command Completion</a></li>
    <li><a name="toc-Command-Options-1" href="Command-Options.html#Command-Options">3.4 Command Options</a></li>
    <li><a name="toc-Automatically-Prepend-Default-Arguments-to-User_002dDefined-Aliases" href="Command-aliases-default-args.html#Command-aliases-default-args">3.5 Automatically Prepend Default Arguments to User-Defined Aliases</a></li>
    <li><a name="toc-Getting-Help" href="Help.html#Help">3.6 Getting Help</a></li>
  </ul></li>
  <li><a name="toc-Running-Programs-under-GDB" href="Running.html#Running">4 Running Programs under <small>GDB</small></a>
  <ul class="no-bullet">
    <li><a name="toc-Compiling-for-Debugging" href="Compilation.html#Compilation">4.1 Compiling for Debugging</a></li>
    <li><a name="toc-Starting-Your-Program" href="Starting.html#Starting">4.2 Starting Your Program</a></li>
    <li><a name="toc-Your-Program_0027s-Arguments" href="Arguments.html#Arguments">4.3 Your Program&rsquo;s Arguments</a></li>
    <li><a name="toc-Your-Program_0027s-Environment" href="Environment.html#Environment">4.4 Your Program&rsquo;s Environment</a></li>
    <li><a name="toc-Your-Program_0027s-Working-Directory" href="Working-Directory.html#Working-Directory">4.5 Your Program&rsquo;s Working Directory</a></li>
    <li><a name="toc-Your-Program_0027s-Input-and-Output" href="Input_002fOutput.html#Input_002fOutput">4.6 Your Program&rsquo;s Input and Output</a></li>
    <li><a name="toc-Debugging-an-Already_002dRunning-Process" href="Attach.html#Attach">4.7 Debugging an Already-Running Process</a></li>
    <li><a name="toc-Killing-the-Child-Process" href="Kill-Process.html#Kill-Process">4.8 Killing the Child Process</a></li>
    <li><a name="toc-Debugging-Multiple-Inferiors-Connections-and-Programs" href="Inferiors-Connections-and-Programs.html#Inferiors-Connections-and-Programs">4.9 Debugging Multiple Inferiors Connections and Programs</a></li>
    <li><a name="toc-Debugging-Programs-with-Multiple-Threads" href="Threads.html#Threads">4.10 Debugging Programs with Multiple Threads</a></li>
    <li><a name="toc-Debugging-Forks" href="Forks.html#Forks">4.11 Debugging Forks</a></li>
    <li><a name="toc-Setting-a-Bookmark-to-Return-to-Later" href="Checkpoint_002fRestart.html#Checkpoint_002fRestart">4.12 Setting a <em>Bookmark</em> to Return to Later</a>
    <ul class="no-bullet">
      <li><a name="toc-A-Non_002dobvious-Benefit-of-Using-Checkpoints" href="Checkpoint_002fRestart.html#A-Non_002dobvious-Benefit-of-Using-Checkpoints">4.12.1 A Non-obvious Benefit of Using Checkpoints</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Stopping-and-Continuing" href="Stopping.html#Stopping">5 Stopping and Continuing</a>
  <ul class="no-bullet">
    <li><a name="toc-Breakpoints_002c-Watchpoints_002c-and-Catchpoints" href="Breakpoints.html#Breakpoints">5.1 Breakpoints, Watchpoints, and Catchpoints</a>
    <ul class="no-bullet">
      <li><a name="toc-Setting-Breakpoints" href="Set-Breaks.html#Set-Breaks">5.1.1 Setting Breakpoints</a></li>
      <li><a name="toc-Setting-Watchpoints" href="Set-Watchpoints.html#Set-Watchpoints">5.1.2 Setting Watchpoints</a></li>
      <li><a name="toc-Setting-Catchpoints" href="Set-Catchpoints.html#Set-Catchpoints">5.1.3 Setting Catchpoints</a></li>
      <li><a name="toc-Deleting-Breakpoints" href="Delete-Breaks.html#Delete-Breaks">5.1.4 Deleting Breakpoints</a></li>
      <li><a name="toc-Disabling-Breakpoints" href="Disabling.html#Disabling">5.1.5 Disabling Breakpoints</a></li>
      <li><a name="toc-Break-Conditions" href="Conditions.html#Conditions">5.1.6 Break Conditions</a></li>
      <li><a name="toc-Breakpoint-Command-Lists" href="Break-Commands.html#Break-Commands">5.1.7 Breakpoint Command Lists</a></li>
      <li><a name="toc-Dynamic-printf-1" href="Dynamic-printf.html#Dynamic-printf">5.1.8 Dynamic printf</a></li>
      <li><a name="toc-How-to-Save-Breakpoints-to-a-File" href="Save-Breakpoints.html#Save-Breakpoints">5.1.9 How to Save Breakpoints to a File</a></li>
      <li><a name="toc-Static-Probe-Points-1" href="Static-Probe-Points.html#Static-Probe-Points">5.1.10 Static Probe Points</a></li>
      <li><a name="toc-_0060_0060Cannot-insert-breakpoints_0027_0027" href="Error-in-Breakpoints.html#Error-in-Breakpoints">5.1.11 &ldquo;Cannot insert breakpoints&rdquo;</a></li>
      <li><a name="toc-_0060_0060Breakpoint-address-adjusted_002e_002e_002e_0027_0027" href="Breakpoint_002drelated-Warnings.html#Breakpoint_002drelated-Warnings">5.1.12 &ldquo;Breakpoint address adjusted...&rdquo;</a></li>
    </ul></li>
    <li><a name="toc-Continuing-and-Stepping-1" href="Continuing-and-Stepping.html#Continuing-and-Stepping">5.2 Continuing and Stepping</a></li>
    <li><a name="toc-Skipping-over-Functions-and-Files" href="Skipping-Over-Functions-and-Files.html#Skipping-Over-Functions-and-Files">5.3 Skipping over Functions and Files</a></li>
    <li><a name="toc-Signals-1" href="Signals.html#Signals">5.4 Signals</a></li>
    <li><a name="toc-Stopping-and-Starting-Multi_002dThread-Programs" href="Thread-Stops.html#Thread-Stops">5.5 Stopping and Starting Multi-Thread Programs</a>
    <ul class="no-bullet">
      <li><a name="toc-All_002dStop-Mode-1" href="All_002dStop-Mode.html#All_002dStop-Mode">5.5.1 All-Stop Mode</a></li>
      <li><a name="toc-Non_002dStop-Mode-1" href="Non_002dStop-Mode.html#Non_002dStop-Mode">5.5.2 Non-Stop Mode</a></li>
      <li><a name="toc-Background-Execution-1" href="Background-Execution.html#Background-Execution">5.5.3 Background Execution</a></li>
      <li><a name="toc-Thread_002dSpecific-Breakpoints-1" href="Thread_002dSpecific-Breakpoints.html#Thread_002dSpecific-Breakpoints">5.5.4 Thread-Specific Breakpoints</a></li>
      <li><a name="toc-Interrupted-System-Calls-1" href="Interrupted-System-Calls.html#Interrupted-System-Calls">5.5.5 Interrupted System Calls</a></li>
      <li><a name="toc-Observer-Mode-1" href="Observer-Mode.html#Observer-Mode">5.5.6 Observer Mode</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Running-Programs-Backward" href="Reverse-Execution.html#Reverse-Execution">6 Running Programs Backward</a></li>
  <li><a name="toc-Recording-Inferior_0027s-Execution-and-Replaying-It" href="Process-Record-and-Replay.html#Process-Record-and-Replay">7 Recording Inferior&rsquo;s Execution and Replaying It</a></li>
  <li><a name="toc-Examining-the-Stack" href="Stack.html#Stack">8 Examining the Stack</a>
  <ul class="no-bullet">
    <li><a name="toc-Stack-Frames" href="Frames.html#Frames">8.1 Stack Frames</a></li>
    <li><a name="toc-Backtraces" href="Backtrace.html#Backtrace">8.2 Backtraces</a></li>
    <li><a name="toc-Selecting-a-Frame" href="Selection.html#Selection">8.3 Selecting a Frame</a></li>
    <li><a name="toc-Information-about-a-Frame" href="Frame-Info.html#Frame-Info">8.4 Information about a Frame</a></li>
    <li><a name="toc-Applying-a-Command-to-Several-Frames" href="Frame-Apply.html#Frame-Apply">8.5 Applying a Command to Several Frames</a></li>
    <li><a name="toc-Management-of-Frame-Filters" href="Frame-Filter-Management.html#Frame-Filter-Management">8.6 Management of Frame Filters</a></li>
  </ul></li>
  <li><a name="toc-Examining-Source-Files" href="Source.html#Source">9 Examining Source Files</a>
  <ul class="no-bullet">
    <li><a name="toc-Printing-Source-Lines" href="List.html#List">9.1 Printing Source Lines</a></li>
    <li><a name="toc-Specifying-a-Location" href="Specify-Location.html#Specify-Location">9.2 Specifying a Location</a>
    <ul class="no-bullet">
      <li><a name="toc-Linespec-Locations-1" href="Linespec-Locations.html#Linespec-Locations">9.2.1 Linespec Locations</a></li>
      <li><a name="toc-Explicit-Locations-1" href="Explicit-Locations.html#Explicit-Locations">9.2.2 Explicit Locations</a></li>
      <li><a name="toc-Address-Locations-1" href="Address-Locations.html#Address-Locations">9.2.3 Address Locations</a></li>
    </ul></li>
    <li><a name="toc-Editing-Source-Files" href="Edit.html#Edit">9.3 Editing Source Files</a>
    <ul class="no-bullet">
      <li><a name="toc-Choosing-Your-Editor" href="Edit.html#Choosing-Your-Editor">9.3.1 Choosing Your Editor</a></li>
    </ul></li>
    <li><a name="toc-Searching-Source-Files" href="Search.html#Search">9.4 Searching Source Files</a></li>
    <li><a name="toc-Specifying-Source-Directories" href="Source-Path.html#Source-Path">9.5 Specifying Source Directories</a></li>
    <li><a name="toc-Source-and-Machine-Code" href="Machine-Code.html#Machine-Code">9.6 Source and Machine Code</a></li>
  </ul></li>
  <li><a name="toc-Examining-Data" href="Data.html#Data">10 Examining Data</a>
  <ul class="no-bullet">
    <li><a name="toc-Expressions-1" href="Expressions.html#Expressions">10.1 Expressions</a></li>
    <li><a name="toc-Ambiguous-Expressions-1" href="Ambiguous-Expressions.html#Ambiguous-Expressions">10.2 Ambiguous Expressions</a></li>
    <li><a name="toc-Program-Variables" href="Variables.html#Variables">10.3 Program Variables</a></li>
    <li><a name="toc-Artificial-Arrays" href="Arrays.html#Arrays">10.4 Artificial Arrays</a></li>
    <li><a name="toc-Output-Formats-1" href="Output-Formats.html#Output-Formats">10.5 Output Formats</a></li>
    <li><a name="toc-Examining-Memory" href="Memory.html#Memory">10.6 Examining Memory</a></li>
    <li><a name="toc-Automatic-Display" href="Auto-Display.html#Auto-Display">10.7 Automatic Display</a></li>
    <li><a name="toc-Print-Settings-1" href="Print-Settings.html#Print-Settings">10.8 Print Settings</a></li>
    <li><a name="toc-Pretty-Printing-1" href="Pretty-Printing.html#Pretty-Printing">10.9 Pretty Printing</a>
    <ul class="no-bullet">
      <li><a name="toc-Pretty_002dPrinter-Introduction-1" href="Pretty_002dPrinter-Introduction.html#Pretty_002dPrinter-Introduction">10.9.1 Pretty-Printer Introduction</a></li>
      <li><a name="toc-Pretty_002dPrinter-Example-1" href="Pretty_002dPrinter-Example.html#Pretty_002dPrinter-Example">10.9.2 Pretty-Printer Example</a></li>
      <li><a name="toc-Pretty_002dPrinter-Commands-1" href="Pretty_002dPrinter-Commands.html#Pretty_002dPrinter-Commands">10.9.3 Pretty-Printer Commands</a></li>
    </ul></li>
    <li><a name="toc-Value-History-1" href="Value-History.html#Value-History">10.10 Value History</a></li>
    <li><a name="toc-Convenience-Variables" href="Convenience-Vars.html#Convenience-Vars">10.11 Convenience Variables</a></li>
    <li><a name="toc-Convenience-Functions" href="Convenience-Funs.html#Convenience-Funs">10.12 Convenience Functions</a></li>
    <li><a name="toc-Registers-1" href="Registers.html#Registers">10.13 Registers</a></li>
    <li><a name="toc-Floating-Point-Hardware-1" href="Floating-Point-Hardware.html#Floating-Point-Hardware">10.14 Floating Point Hardware</a></li>
    <li><a name="toc-Vector-Unit-1" href="Vector-Unit.html#Vector-Unit">10.15 Vector Unit</a></li>
    <li><a name="toc-Operating-System-Auxiliary-Information" href="OS-Information.html#OS-Information">10.16 Operating System Auxiliary Information</a></li>
    <li><a name="toc-Memory-Region-Attributes-1" href="Memory-Region-Attributes.html#Memory-Region-Attributes">10.17 Memory Region Attributes</a>
    <ul class="no-bullet">
      <li><a name="toc-Attributes" href="Memory-Region-Attributes.html#Attributes">10.17.1 Attributes</a>
      <ul class="no-bullet">
        <li><a name="toc-Memory-Access-Mode" href="Memory-Region-Attributes.html#Memory-Access-Mode">10.17.1.1 Memory Access Mode</a></li>
        <li><a name="toc-Memory-Access-Size" href="Memory-Region-Attributes.html#Memory-Access-Size">10.17.1.2 Memory Access Size</a></li>
        <li><a name="toc-Data-Cache" href="Memory-Region-Attributes.html#Data-Cache">10.17.1.3 Data Cache</a></li>
      </ul></li>
      <li><a name="toc-Memory-Access-Checking" href="Memory-Region-Attributes.html#Memory-Access-Checking">10.17.2 Memory Access Checking</a></li>
    </ul></li>
    <li><a name="toc-Copy-between-Memory-and-a-File" href="Dump_002fRestore-Files.html#Dump_002fRestore-Files">10.18 Copy between Memory and a File</a></li>
    <li><a name="toc-How-to-Produce-a-Core-File-from-Your-Program" href="Core-File-Generation.html#Core-File-Generation">10.19 How to Produce a Core File from Your Program</a></li>
    <li><a name="toc-Character-Sets-1" href="Character-Sets.html#Character-Sets">10.20 Character Sets</a></li>
    <li><a name="toc-Caching-Data-of-Targets" href="Caching-Target-Data.html#Caching-Target-Data">10.21 Caching Data of Targets</a></li>
    <li><a name="toc-Search-Memory" href="Searching-Memory.html#Searching-Memory">10.22 Search Memory</a></li>
    <li><a name="toc-Value-Sizes-1" href="Value-Sizes.html#Value-Sizes">10.23 Value Sizes</a></li>
  </ul></li>
  <li><a name="toc-Debugging-Optimized-Code" href="Optimized-Code.html#Optimized-Code">11 Debugging Optimized Code</a>
  <ul class="no-bullet">
    <li><a name="toc-Inline-Functions-1" href="Inline-Functions.html#Inline-Functions">11.1 Inline Functions</a></li>
    <li><a name="toc-Tail-Call-Frames-1" href="Tail-Call-Frames.html#Tail-Call-Frames">11.2 Tail Call Frames</a></li>
  </ul></li>
  <li><a name="toc-C-Preprocessor-Macros" href="Macros.html#Macros">12 C Preprocessor Macros</a></li>
  <li><a name="toc-Tracepoints-1" href="Tracepoints.html#Tracepoints">13 Tracepoints</a>
  <ul class="no-bullet">
    <li><a name="toc-Commands-to-Set-Tracepoints" href="Set-Tracepoints.html#Set-Tracepoints">13.1 Commands to Set Tracepoints</a>
    <ul class="no-bullet">
      <li><a name="toc-Create-and-Delete-Tracepoints-1" href="Create-and-Delete-Tracepoints.html#Create-and-Delete-Tracepoints">13.1.1 Create and Delete Tracepoints</a></li>
      <li><a name="toc-Enable-and-Disable-Tracepoints-1" href="Enable-and-Disable-Tracepoints.html#Enable-and-Disable-Tracepoints">13.1.2 Enable and Disable Tracepoints</a></li>
      <li><a name="toc-Tracepoint-Passcounts-1" href="Tracepoint-Passcounts.html#Tracepoint-Passcounts">13.1.3 Tracepoint Passcounts</a></li>
      <li><a name="toc-Tracepoint-Conditions-1" href="Tracepoint-Conditions.html#Tracepoint-Conditions">13.1.4 Tracepoint Conditions</a></li>
      <li><a name="toc-Trace-State-Variables-1" href="Trace-State-Variables.html#Trace-State-Variables">13.1.5 Trace State Variables</a></li>
      <li><a name="toc-Tracepoint-Action-Lists" href="Tracepoint-Actions.html#Tracepoint-Actions">13.1.6 Tracepoint Action Lists</a></li>
      <li><a name="toc-Listing-Tracepoints-1" href="Listing-Tracepoints.html#Listing-Tracepoints">13.1.7 Listing Tracepoints</a></li>
      <li><a name="toc-Listing-Static-Tracepoint-Markers-1" href="Listing-Static-Tracepoint-Markers.html#Listing-Static-Tracepoint-Markers">13.1.8 Listing Static Tracepoint Markers</a></li>
      <li><a name="toc-Starting-and-Stopping-Trace-Experiments-1" href="Starting-and-Stopping-Trace-Experiments.html#Starting-and-Stopping-Trace-Experiments">13.1.9 Starting and Stopping Trace Experiments</a></li>
      <li><a name="toc-Tracepoint-Restrictions-1" href="Tracepoint-Restrictions.html#Tracepoint-Restrictions">13.1.10 Tracepoint Restrictions</a></li>
    </ul></li>
    <li><a name="toc-Using-the-Collected-Data" href="Analyze-Collected-Data.html#Analyze-Collected-Data">13.2 Using the Collected Data</a>
    <ul class="no-bullet">
      <li><a name="toc-tfind-n" href="tfind.html#tfind">13.2.1 <code>tfind <var>n</var></code></a></li>
      <li><a name="toc-tdump-1" href="tdump.html#tdump">13.2.2 <code>tdump</code></a></li>
      <li><a name="toc-save-tracepoints-filename" href="save-tracepoints.html#save-tracepoints">13.2.3 <code>save tracepoints <var>filename</var></code></a></li>
    </ul></li>
    <li><a name="toc-Convenience-Variables-for-Tracepoints" href="Tracepoint-Variables.html#Tracepoint-Variables">13.3 Convenience Variables for Tracepoints</a></li>
    <li><a name="toc-Using-Trace-Files" href="Trace-Files.html#Trace-Files">13.4 Using Trace Files</a></li>
  </ul></li>
  <li><a name="toc-Debugging-Programs-That-Use-Overlays" href="Overlays.html#Overlays">14 Debugging Programs That Use Overlays</a>
  <ul class="no-bullet">
    <li><a name="toc-How-Overlays-Work-1" href="How-Overlays-Work.html#How-Overlays-Work">14.1 How Overlays Work</a></li>
    <li><a name="toc-Overlay-Commands-1" href="Overlay-Commands.html#Overlay-Commands">14.2 Overlay Commands</a></li>
    <li><a name="toc-Automatic-Overlay-Debugging-1" href="Automatic-Overlay-Debugging.html#Automatic-Overlay-Debugging">14.3 Automatic Overlay Debugging</a></li>
    <li><a name="toc-Overlay-Sample-Program-1" href="Overlay-Sample-Program.html#Overlay-Sample-Program">14.4 Overlay Sample Program</a></li>
  </ul></li>
  <li><a name="toc-Using-GDB-with-Different-Languages" href="Languages.html#Languages">15 Using <small>GDB</small> with Different Languages</a>
  <ul class="no-bullet">
    <li><a name="toc-Switching-between-Source-Languages" href="Setting.html#Setting">15.1 Switching between Source Languages</a>
    <ul class="no-bullet">
      <li><a name="toc-List-of-Filename-Extensions-and-Languages" href="Filenames.html#Filenames">15.1.1 List of Filename Extensions and Languages</a></li>
      <li><a name="toc-Setting-the-Working-Language" href="Manually.html#Manually">15.1.2 Setting the Working Language</a></li>
      <li><a name="toc-Having-GDB-Infer-the-Source-Language" href="Automatically.html#Automatically">15.1.3 Having <small>GDB</small> Infer the Source Language</a></li>
    </ul></li>
    <li><a name="toc-Displaying-the-Language" href="Show.html#Show">15.2 Displaying the Language</a></li>
    <li><a name="toc-Type-and-Range-Checking" href="Checks.html#Checks">15.3 Type and Range Checking</a>
    <ul class="no-bullet">
      <li><a name="toc-An-Overview-of-Type-Checking" href="Type-Checking.html#Type-Checking">15.3.1 An Overview of Type Checking</a></li>
      <li><a name="toc-An-Overview-of-Range-Checking" href="Range-Checking.html#Range-Checking">15.3.2 An Overview of Range Checking</a></li>
    </ul></li>
    <li><a name="toc-Supported-Languages-1" href="Supported-Languages.html#Supported-Languages">15.4 Supported Languages</a>
    <ul class="no-bullet">
      <li><a name="toc-C-and-C_002b_002b" href="C.html#C">15.4.1 C and C<tt>++</tt></a>
      <ul class="no-bullet">
        <li><a name="toc-C-and-C_002b_002b-Operators" href="C-Operators.html#C-Operators">15.4.1.1 C and C<tt>++</tt> Operators</a></li>
        <li><a name="toc-C-and-C_002b_002b-Constants" href="C-Constants.html#C-Constants">15.4.1.2 C and C<tt>++</tt> Constants</a></li>
        <li><a name="toc-C_002b_002b-Expressions" href="C-Plus-Plus-Expressions.html#C-Plus-Plus-Expressions">15.4.1.3 C<tt>++</tt> Expressions</a></li>
        <li><a name="toc-C-and-C_002b_002b-Defaults" href="C-Defaults.html#C-Defaults">15.4.1.4 C and C<tt>++</tt> Defaults</a></li>
        <li><a name="toc-C-and-C_002b_002b-Type-and-Range-Checks" href="C-Checks.html#C-Checks">15.4.1.5 C and C<tt>++</tt> Type and Range Checks</a></li>
        <li><a name="toc-GDB-and-C" href="Debugging-C.html#Debugging-C">15.4.1.6 <small>GDB</small> and C</a></li>
        <li><a name="toc-GDB-Features-for-C_002b_002b" href="Debugging-C-Plus-Plus.html#Debugging-C-Plus-Plus">15.4.1.7 <small>GDB</small> Features for C<tt>++</tt></a></li>
        <li><a name="toc-Decimal-Floating-Point-Format" href="Decimal-Floating-Point.html#Decimal-Floating-Point">15.4.1.8 Decimal Floating Point Format</a></li>
      </ul></li>
      <li><a name="toc-D-1" href="D.html#D">15.4.2 D</a></li>
      <li><a name="toc-Go-1" href="Go.html#Go">15.4.3 Go</a></li>
      <li><a name="toc-Objective_002dC-1" href="Objective_002dC.html#Objective_002dC">15.4.4 Objective-C</a>
      <ul class="no-bullet">
        <li><a name="toc-Method-Names-in-Commands-1" href="Method-Names-in-Commands.html#Method-Names-in-Commands">15.4.4.1 Method Names in Commands</a></li>
        <li><a name="toc-The-Print-Command-with-Objective_002dC-1" href="The-Print-Command-with-Objective_002dC.html#The-Print-Command-with-Objective_002dC">15.4.4.2 The Print Command with Objective-C</a></li>
      </ul></li>
      <li><a name="toc-OpenCL-C-1" href="OpenCL-C.html#OpenCL-C">15.4.5 OpenCL C</a>
      <ul class="no-bullet">
        <li><a name="toc-OpenCL-C-Datatypes-1" href="OpenCL-C-Datatypes.html#OpenCL-C-Datatypes">15.4.5.1 OpenCL C Datatypes</a></li>
        <li><a name="toc-OpenCL-C-Expressions-1" href="OpenCL-C-Expressions.html#OpenCL-C-Expressions">15.4.5.2 OpenCL C Expressions</a></li>
        <li><a name="toc-OpenCL-C-Operators-1" href="OpenCL-C-Operators.html#OpenCL-C-Operators">15.4.5.3 OpenCL C Operators</a></li>
      </ul></li>
      <li><a name="toc-Fortran-1" href="Fortran.html#Fortran">15.4.6 Fortran</a>
      <ul class="no-bullet">
        <li><a name="toc-Fortran-Operators-and-Expressions" href="Fortran-Operators.html#Fortran-Operators">15.4.6.1 Fortran Operators and Expressions</a></li>
        <li><a name="toc-Fortran-Defaults-1" href="Fortran-Defaults.html#Fortran-Defaults">15.4.6.2 Fortran Defaults</a></li>
        <li><a name="toc-Special-Fortran-Commands-1" href="Special-Fortran-Commands.html#Special-Fortran-Commands">15.4.6.3 Special Fortran Commands</a></li>
      </ul></li>
      <li><a name="toc-Pascal-1" href="Pascal.html#Pascal">15.4.7 Pascal</a></li>
      <li><a name="toc-Rust-1" href="Rust.html#Rust">15.4.8 Rust</a></li>
      <li><a name="toc-Modula_002d2-1" href="Modula_002d2.html#Modula_002d2">15.4.9 Modula-2</a>
      <ul class="no-bullet">
        <li><a name="toc-Operators" href="M2-Operators.html#M2-Operators">15.4.9.1 Operators</a></li>
        <li><a name="toc-Built_002din-Functions-and-Procedures" href="Built_002dIn-Func_002fProc.html#Built_002dIn-Func_002fProc">15.4.9.2 Built-in Functions and Procedures</a></li>
        <li><a name="toc-Constants-1" href="M2-Constants.html#M2-Constants">15.4.9.3 Constants</a></li>
        <li><a name="toc-Modula_002d2-Types" href="M2-Types.html#M2-Types">15.4.9.4 Modula-2 Types</a></li>
        <li><a name="toc-Modula_002d2-Defaults" href="M2-Defaults.html#M2-Defaults">15.4.9.5 Modula-2 Defaults</a></li>
        <li><a name="toc-Deviations-from-Standard-Modula_002d2" href="Deviations.html#Deviations">15.4.9.6 Deviations from Standard Modula-2</a></li>
        <li><a name="toc-Modula_002d2-Type-and-Range-Checks" href="M2-Checks.html#M2-Checks">15.4.9.7 Modula-2 Type and Range Checks</a></li>
        <li><a name="toc-The-Scope-Operators-_003a_003a-and-_002e" href="M2-Scope.html#M2-Scope">15.4.9.8 The Scope Operators <code>::</code> and <code>.</code></a></li>
        <li><a name="toc-GDB-and-Modula_002d2" href="GDB_002fM2.html#GDB_002fM2">15.4.9.9 <small>GDB</small> and Modula-2</a></li>
      </ul></li>
      <li><a name="toc-Ada-1" href="Ada.html#Ada">15.4.10 Ada</a>
      <ul class="no-bullet">
        <li><a name="toc-Introduction" href="Ada-Mode-Intro.html#Ada-Mode-Intro">15.4.10.1 Introduction</a></li>
        <li><a name="toc-Omissions-from-Ada-1" href="Omissions-from-Ada.html#Omissions-from-Ada">15.4.10.2 Omissions from Ada</a></li>
        <li><a name="toc-Additions-to-Ada-1" href="Additions-to-Ada.html#Additions-to-Ada">15.4.10.3 Additions to Ada</a></li>
        <li><a name="toc-Overloading-Support-for-Ada" href="Overloading-support-for-Ada.html#Overloading-support-for-Ada">15.4.10.4 Overloading Support for Ada</a></li>
        <li><a name="toc-Stopping-at-the-Very-Beginning" href="Stopping-Before-Main-Program.html#Stopping-Before-Main-Program">15.4.10.5 Stopping at the Very Beginning</a></li>
        <li><a name="toc-Ada-Exceptions-1" href="Ada-Exceptions.html#Ada-Exceptions">15.4.10.6 Ada Exceptions</a></li>
        <li><a name="toc-Extensions-for-Ada-Tasks" href="Ada-Tasks.html#Ada-Tasks">15.4.10.7 Extensions for Ada Tasks</a></li>
        <li><a name="toc-Tasking-Support-When-Debugging-Core-Files" href="Ada-Tasks-and-Core-Files.html#Ada-Tasks-and-Core-Files">15.4.10.8 Tasking Support When Debugging Core Files</a></li>
        <li><a name="toc-Tasking-Support-When-Using-the-Ravenscar-Profile" href="Ravenscar-Profile.html#Ravenscar-Profile">15.4.10.9 Tasking Support When Using the Ravenscar Profile</a></li>
        <li><a name="toc-Ada-Settings-1" href="Ada-Settings.html#Ada-Settings">15.4.10.10 Ada Settings</a></li>
        <li><a name="toc-Known-Peculiarities-of-Ada-Mode" href="Ada-Glitches.html#Ada-Glitches">15.4.10.11 Known Peculiarities of Ada Mode</a></li>
      </ul></li>
    </ul></li>
    <li><a name="toc-Unsupported-Languages-1" href="Unsupported-Languages.html#Unsupported-Languages">15.5 Unsupported Languages</a></li>
  </ul></li>
  <li><a name="toc-Examining-the-Symbol-Table" href="Symbols.html#Symbols">16 Examining the Symbol Table</a></li>
  <li><a name="toc-Altering-Execution" href="Altering.html#Altering">17 Altering Execution</a>
  <ul class="no-bullet">
    <li><a name="toc-Assignment-to-Variables" href="Assignment.html#Assignment">17.1 Assignment to Variables</a></li>
    <li><a name="toc-Continuing-at-a-Different-Address" href="Jumping.html#Jumping">17.2 Continuing at a Different Address</a></li>
    <li><a name="toc-Giving-Your-Program-a-Signal" href="Signaling.html#Signaling">17.3 Giving Your Program a Signal</a></li>
    <li><a name="toc-Returning-from-a-Function" href="Returning.html#Returning">17.4 Returning from a Function</a></li>
    <li><a name="toc-Calling-Program-Functions" href="Calling.html#Calling">17.5 Calling Program Functions</a>
    <ul class="no-bullet">
      <li><a name="toc-Calling-Functions-with-No-Debug-Info" href="Calling.html#Calling-Functions-with-No-Debug-Info">17.5.1 Calling Functions with No Debug Info</a></li>
    </ul></li>
    <li><a name="toc-Patching-Programs" href="Patching.html#Patching">17.6 Patching Programs</a></li>
    <li><a name="toc-Compiling-and-Injecting-Code-in-GDB" href="Compiling-and-Injecting-Code.html#Compiling-and-Injecting-Code">17.7 Compiling and Injecting Code in <small>GDB</small></a>
    <ul class="no-bullet">
      <li><a name="toc-Compilation-Options-for-the-compile-Command" href="Compiling-and-Injecting-Code.html#Compilation-Options-for-the-compile-Command">17.7.1 Compilation Options for the <code>compile</code> Command</a></li>
      <li><a name="toc-Caveats-When-Using-the-compile-Command" href="Compiling-and-Injecting-Code.html#Caveats-When-Using-the-compile-Command">17.7.2 Caveats When Using the <code>compile</code> Command</a></li>
      <li><a name="toc-Compiler-Search-for-the-compile-Command" href="Compiling-and-Injecting-Code.html#Compiler-Search-for-the-compile-Command">17.7.3 Compiler Search for the <code>compile</code> Command</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-GDB-Files-1" href="GDB-Files.html#GDB-Files">18 <small>GDB</small> Files</a>
  <ul class="no-bullet">
    <li><a name="toc-Commands-to-Specify-Files" href="Files.html#Files">18.1 Commands to Specify Files</a></li>
    <li><a name="toc-File-Caching-1" href="File-Caching.html#File-Caching">18.2 File Caching</a></li>
    <li><a name="toc-Debugging-Information-in-Separate-Files" href="Separate-Debug-Files.html#Separate-Debug-Files">18.3 Debugging Information in Separate Files</a></li>
    <li><a name="toc-Debugging-Information-in-a-Special-Section" href="MiniDebugInfo.html#MiniDebugInfo">18.4 Debugging Information in a Special Section</a></li>
    <li><a name="toc-Index-Files-Speed-Up-GDB" href="Index-Files.html#Index-Files">18.5 Index Files Speed Up <small>GDB</small></a>
    <ul class="no-bullet">
      <li><a name="toc-Automatic-Symbol-Index-Cache" href="Index-Files.html#Automatic-Symbol-Index-Cache">18.5.1 Automatic Symbol Index Cache</a></li>
    </ul></li>
    <li><a name="toc-Errors-Reading-Symbol-Files" href="Symbol-Errors.html#Symbol-Errors">18.6 Errors Reading Symbol Files</a></li>
    <li><a name="toc-GDB-Data-Files" href="Data-Files.html#Data-Files">18.7 GDB Data Files</a></li>
  </ul></li>
  <li><a name="toc-Specifying-a-Debugging-Target" href="Targets.html#Targets">19 Specifying a Debugging Target</a>
  <ul class="no-bullet">
    <li><a name="toc-Active-Targets-1" href="Active-Targets.html#Active-Targets">19.1 Active Targets</a></li>
    <li><a name="toc-Commands-for-Managing-Targets" href="Target-Commands.html#Target-Commands">19.2 Commands for Managing Targets</a></li>
    <li><a name="toc-Choosing-Target-Byte-Order" href="Byte-Order.html#Byte-Order">19.3 Choosing Target Byte Order</a></li>
  </ul></li>
  <li><a name="toc-Debugging-Remote-Programs" href="Remote-Debugging.html#Remote-Debugging">20 Debugging Remote Programs</a>
  <ul class="no-bullet">
    <li><a name="toc-Connecting-to-a-Remote-Target" href="Connecting.html#Connecting">20.1 Connecting to a Remote Target</a>
    <ul class="no-bullet">
      <li><a name="toc-Types-of-Remote-Connections" href="Connecting.html#Types-of-Remote-Connections">20.1.1 Types of Remote Connections</a></li>
      <li><a name="toc-Host-and-Target-Files" href="Connecting.html#Host-and-Target-Files">20.1.2 Host and Target Files</a></li>
      <li><a name="toc-Remote-Connection-Commands" href="Connecting.html#Remote-Connection-Commands">20.1.3 Remote Connection Commands</a></li>
    </ul></li>
    <li><a name="toc-Sending-Files-to-a-Remote-System" href="File-Transfer.html#File-Transfer">20.2 Sending Files to a Remote System</a></li>
    <li><a name="toc-Using-the-gdbserver-Program" href="Server.html#Server">20.3 Using the <code>gdbserver</code> Program</a>
    <ul class="no-bullet">
      <li><a name="toc-Running-gdbserver-1" href="Server.html#Running-gdbserver-1">20.3.1 Running <code>gdbserver</code></a>
      <ul class="no-bullet">
        <li><a name="toc-Attaching-to-a-Running-Program" href="Server.html#Attaching-to-a-Running-Program">******** Attaching to a Running Program</a></li>
        <li><a name="toc-TCP-Port-Allocation-Lifecycle-of-gdbserver" href="Server.html#TCP-Port-Allocation-Lifecycle-of-gdbserver">******** TCP Port Allocation Lifecycle of <code>gdbserver</code></a></li>
        <li><a name="toc-Other-Command_002dLine-Arguments-for-gdbserver-1" href="Server.html#Other-Command_002dLine-Arguments-for-gdbserver-1">20.3.1.3 Other Command-Line Arguments for <code>gdbserver</code></a></li>
      </ul></li>
      <li><a name="toc-Connecting-to-gdbserver" href="Server.html#Connecting-to-gdbserver">20.3.2 Connecting to <code>gdbserver</code></a></li>
      <li><a name="toc-Monitor-Commands-for-gdbserver-1" href="Server.html#Monitor-Commands-for-gdbserver-1">20.3.3 Monitor Commands for <code>gdbserver</code></a></li>
      <li><a name="toc-Tracepoints-Support-in-gdbserver" href="Server.html#Tracepoints-Support-in-gdbserver">20.3.4 Tracepoints Support in <code>gdbserver</code></a></li>
    </ul></li>
    <li><a name="toc-Remote-Configuration-1" href="Remote-Configuration.html#Remote-Configuration">20.4 Remote Configuration</a></li>
    <li><a name="toc-Implementing-a-Remote-Stub" href="Remote-Stub.html#Remote-Stub">20.5 Implementing a Remote Stub</a>
    <ul class="no-bullet">
      <li><a name="toc-What-the-Stub-Can-Do-for-You" href="Stub-Contents.html#Stub-Contents">20.5.1 What the Stub Can Do for You</a></li>
      <li><a name="toc-What-You-Must-Do-for-the-Stub" href="Bootstrapping.html#Bootstrapping">20.5.2 What You Must Do for the Stub</a></li>
      <li><a name="toc-Putting-It-All-Together" href="Debug-Session.html#Debug-Session">20.5.3 Putting It All Together</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Configuration_002dSpecific-Information" href="Configurations.html#Configurations">21 Configuration-Specific Information</a>
  <ul class="no-bullet">
    <li><a name="toc-Native-1" href="Native.html#Native">21.1 Native</a>
    <ul class="no-bullet">
      <li><a name="toc-BSD-libkvm-Interface-1" href="BSD-libkvm-Interface.html#BSD-libkvm-Interface">21.1.1 BSD libkvm Interface</a></li>
      <li><a name="toc-Process-Information-1" href="Process-Information.html#Process-Information">21.1.2 Process Information</a></li>
      <li><a name="toc-Features-for-Debugging-DJGPP-Programs" href="DJGPP-Native.html#DJGPP-Native">21.1.3 Features for Debugging <small>DJGPP</small> Programs</a></li>
      <li><a name="toc-Features-for-Debugging-MS-Windows-PE-Executables" href="Cygwin-Native.html#Cygwin-Native">21.1.4 Features for Debugging MS Windows PE Executables</a>
      <ul class="no-bullet">
        <li><a name="toc-Support-for-DLLs-without-Debugging-Symbols" href="Non_002ddebug-DLL-Symbols.html#Non_002ddebug-DLL-Symbols">21.1.4.1 Support for DLLs without Debugging Symbols</a></li>
        <li><a name="toc-DLL-Name-Prefixes" href="Non_002ddebug-DLL-Symbols.html#DLL-Name-Prefixes">21.1.4.2 DLL Name Prefixes</a></li>
        <li><a name="toc-Working-with-Minimal-Symbols" href="Non_002ddebug-DLL-Symbols.html#Working-with-Minimal-Symbols">21.1.4.3 Working with Minimal Symbols</a></li>
      </ul></li>
      <li><a name="toc-Commands-Specific-to-GNU-Hurd-Systems" href="Hurd-Native.html#Hurd-Native">21.1.5 Commands Specific to <small>GNU</small> Hurd Systems</a></li>
      <li><a name="toc-Darwin-1" href="Darwin.html#Darwin">21.1.6 Darwin</a></li>
      <li><a name="toc-FreeBSD-1" href="FreeBSD.html#FreeBSD">21.1.7 FreeBSD</a></li>
    </ul></li>
    <li><a name="toc-Embedded-Operating-Systems" href="Embedded-OS.html#Embedded-OS">21.2 Embedded Operating Systems</a></li>
    <li><a name="toc-Embedded-Processors-1" href="Embedded-Processors.html#Embedded-Processors">21.3 Embedded Processors</a>
    <ul class="no-bullet">
      <li><a name="toc-Synopsys-ARC" href="ARC.html#ARC">21.3.1 Synopsys ARC</a></li>
      <li><a name="toc-ARM-1" href="ARM.html#ARM">21.3.2 ARM</a></li>
      <li><a name="toc-BPF-1" href="BPF.html#BPF">21.3.3 BPF</a></li>
      <li><a name="toc-M68k" href="M68K.html#M68K">21.3.4 M68k</a></li>
      <li><a name="toc-MicroBlaze-1" href="MicroBlaze.html#MicroBlaze">21.3.5 MicroBlaze</a></li>
      <li><a name="toc-MIPS-Embedded-1" href="MIPS-Embedded.html#MIPS-Embedded">21.3.6 <acronym>MIPS</acronym> Embedded</a></li>
      <li><a name="toc-OpenRISC-1000-1" href="OpenRISC-1000.html#OpenRISC-1000">21.3.7 OpenRISC 1000</a></li>
      <li><a name="toc-PowerPC-Embedded-1" href="PowerPC-Embedded.html#PowerPC-Embedded">21.3.8 PowerPC Embedded</a></li>
      <li><a name="toc-Atmel-AVR" href="AVR.html#AVR">21.3.9 Atmel AVR</a></li>
      <li><a name="toc-CRIS-1" href="CRIS.html#CRIS">21.3.10 CRIS</a></li>
      <li><a name="toc-Renesas-Super_002dH" href="Super_002dH.html#Super_002dH">21.3.11 Renesas Super-H</a></li>
    </ul></li>
    <li><a name="toc-Architectures-1" href="Architectures.html#Architectures">21.4 Architectures</a>
    <ul class="no-bullet">
      <li><a name="toc-AArch64-1" href="AArch64.html#AArch64">21.4.1 AArch64</a>
      <ul class="no-bullet">
        <li><a name="toc-AArch64-SVE" href="AArch64.html#AArch64-SVE">******** AArch64 SVE</a></li>
        <li><a name="toc-AArch64-Pointer-Authentication" href="AArch64.html#AArch64-Pointer-Authentication">21.4.1.2 AArch64 Pointer Authentication</a></li>
      </ul></li>
      <li><a name="toc-x86-Architecture_002dSpecific-Issues" href="i386.html#i386">21.4.2 x86 Architecture-Specific Issues</a>
      <ul class="no-bullet">
        <li><a name="toc-Intel-Memory-Protection-Extensions-_0028MPX_0029_002e" href="i386.html#Intel-Memory-Protection-Extensions-_0028MPX_0029_002e">******** Intel <em>Memory Protection Extensions</em> (MPX).</a></li>
      </ul></li>
      <li><a name="toc-Alpha-1" href="Alpha.html#Alpha">21.4.3 Alpha</a></li>
      <li><a name="toc-MIPS-1" href="MIPS.html#MIPS">21.4.4 <acronym>MIPS</acronym></a></li>
      <li><a name="toc-HPPA-1" href="HPPA.html#HPPA">21.4.5 HPPA</a></li>
      <li><a name="toc-PowerPC-1" href="PowerPC.html#PowerPC">21.4.6 PowerPC</a></li>
      <li><a name="toc-Nios-II-1" href="Nios-II.html#Nios-II">21.4.7 Nios II</a></li>
      <li><a name="toc-Sparc64-1" href="Sparc64.html#Sparc64">21.4.8 Sparc64</a>
      <ul class="no-bullet">
        <li><a name="toc-ADI-Support" href="Sparc64.html#ADI-Support">******** ADI Support</a></li>
      </ul></li>
      <li><a name="toc-S12Z-1" href="S12Z.html#S12Z">21.4.9 S12Z</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Controlling-GDB-1" href="Controlling-GDB.html#Controlling-GDB">22 Controlling <small>GDB</small></a>
  <ul class="no-bullet">
    <li><a name="toc-Prompt-1" href="Prompt.html#Prompt">22.1 Prompt</a></li>
    <li><a name="toc-Command-Editing" href="Editing.html#Editing">22.2 Command Editing</a></li>
    <li><a name="toc-Command-History-1" href="Command-History.html#Command-History">22.3 Command History</a></li>
    <li><a name="toc-Screen-Size-1" href="Screen-Size.html#Screen-Size">22.4 Screen Size</a></li>
    <li><a name="toc-Output-Styling-1" href="Output-Styling.html#Output-Styling">22.5 Output Styling</a></li>
    <li><a name="toc-Numbers-1" href="Numbers.html#Numbers">22.6 Numbers</a></li>
    <li><a name="toc-Configuring-the-Current-ABI" href="ABI.html#ABI">22.7 Configuring the Current ABI</a></li>
    <li><a name="toc-Automatically-Loading-Associated-Files" href="Auto_002dloading.html#Auto_002dloading">22.8 Automatically Loading Associated Files</a>
    <ul class="no-bullet">
      <li><a name="toc-Automatically-Loading-Init-File-in-the-Current-Directory" href="Init-File-in-the-Current-Directory.html#Init-File-in-the-Current-Directory">22.8.1 Automatically Loading Init File in the Current Directory</a></li>
      <li><a name="toc-Automatically-Loading-Thread-Debugging-Library" href="libthread_005fdb_002eso_002e1-file.html#libthread_005fdb_002eso_002e1-file">22.8.2 Automatically Loading Thread Debugging Library</a></li>
      <li><a name="toc-Security-Restriction-for-Auto_002dLoading" href="Auto_002dloading-safe-path.html#Auto_002dloading-safe-path">22.8.3 Security Restriction for Auto-Loading</a></li>
      <li><a name="toc-Displaying-Files-Tried-for-Auto_002dLoad" href="Auto_002dloading-verbose-mode.html#Auto_002dloading-verbose-mode">22.8.4 Displaying Files Tried for Auto-Load</a></li>
    </ul></li>
    <li><a name="toc-Optional-Warnings-and-Messages" href="Messages_002fWarnings.html#Messages_002fWarnings">22.9 Optional Warnings and Messages</a></li>
    <li><a name="toc-Optional-Messages-about-Internal-Happenings" href="Debugging-Output.html#Debugging-Output">22.10 Optional Messages about Internal Happenings</a></li>
    <li><a name="toc-Other-Miscellaneous-Settings" href="Other-Misc-Settings.html#Other-Misc-Settings">22.11 Other Miscellaneous Settings</a></li>
  </ul></li>
  <li><a name="toc-Extending-GDB-1" href="Extending-GDB.html#Extending-GDB">23 Extending <small>GDB</small></a>
  <ul class="no-bullet">
    <li><a name="toc-Canned-Sequences-of-Commands" href="Sequences.html#Sequences">23.1 Canned Sequences of Commands</a>
    <ul class="no-bullet">
      <li><a name="toc-User_002dDefined-Commands" href="Define.html#Define">23.1.1 User-Defined Commands</a></li>
      <li><a name="toc-User_002dDefined-Command-Hooks" href="Hooks.html#Hooks">23.1.2 User-Defined Command Hooks</a></li>
      <li><a name="toc-Command-Files-1" href="Command-Files.html#Command-Files">23.1.3 Command Files</a></li>
      <li><a name="toc-Commands-for-Controlled-Output" href="Output.html#Output">23.1.4 Commands for Controlled Output</a></li>
      <li><a name="toc-Controlling-Auto_002dLoading-Native-GDB-Scripts" href="Auto_002dloading-sequences.html#Auto_002dloading-sequences">23.1.5 Controlling Auto-Loading Native <small>GDB</small> Scripts</a></li>
    </ul></li>
    <li><a name="toc-Extending-GDB-using-Python" href="Python.html#Python">23.2 Extending <small>GDB</small> using Python</a>
    <ul class="no-bullet">
      <li><a name="toc-Python-Commands-1" href="Python-Commands.html#Python-Commands">23.2.1 Python Commands</a></li>
      <li><a name="toc-Python-API-1" href="Python-API.html#Python-API">23.2.2 Python API</a>
      <ul class="no-bullet">
        <li><a name="toc-Basic-Python-1" href="Basic-Python.html#Basic-Python">******** Basic Python</a></li>
        <li><a name="toc-Exception-Handling-1" href="Exception-Handling.html#Exception-Handling">******** Exception Handling</a></li>
        <li><a name="toc-Values-From-Inferior-1" href="Values-From-Inferior.html#Values-From-Inferior">******** Values From Inferior</a></li>
        <li><a name="toc-Types-In-Python-1" href="Types-In-Python.html#Types-In-Python">******** Types In Python</a></li>
        <li><a name="toc-Pretty-Printing-API-1" href="Pretty-Printing-API.html#Pretty-Printing-API">23.2.2.5 Pretty Printing API</a></li>
        <li><a name="toc-Selecting-Pretty_002dPrinters-1" href="Selecting-Pretty_002dPrinters.html#Selecting-Pretty_002dPrinters">23.2.2.6 Selecting Pretty-Printers</a></li>
        <li><a name="toc-Writing-a-Pretty_002dPrinter-1" href="Writing-a-Pretty_002dPrinter.html#Writing-a-Pretty_002dPrinter">23.2.2.7 Writing a Pretty-Printer</a></li>
        <li><a name="toc-Type-Printing-API-1" href="Type-Printing-API.html#Type-Printing-API">23.2.2.8 Type Printing API</a></li>
        <li><a name="toc-Filtering-Frames" href="Frame-Filter-API.html#Frame-Filter-API">23.2.2.9 Filtering Frames</a></li>
        <li><a name="toc-Decorating-Frames" href="Frame-Decorator-API.html#Frame-Decorator-API">********0 Decorating Frames</a></li>
        <li><a name="toc-Writing-a-Frame-Filter-1" href="Writing-a-Frame-Filter.html#Writing-a-Frame-Filter">********1 Writing a Frame Filter</a></li>
        <li><a name="toc-Unwinding-Frames-in-Python-1" href="Unwinding-Frames-in-Python.html#Unwinding-Frames-in-Python">********2 Unwinding Frames in Python</a></li>
        <li><a name="toc-Xmethods-In-Python-1" href="Xmethods-In-Python.html#Xmethods-In-Python">********3 Xmethods In Python</a></li>
        <li><a name="toc-Xmethod-API-1" href="Xmethod-API.html#Xmethod-API">********4 Xmethod API</a></li>
        <li><a name="toc-Writing-an-Xmethod-1" href="Writing-an-Xmethod.html#Writing-an-Xmethod">********5 Writing an Xmethod</a></li>
        <li><a name="toc-Inferiors-In-Python-1" href="Inferiors-In-Python.html#Inferiors-In-Python">********6 Inferiors In Python</a></li>
        <li><a name="toc-Events-In-Python-1" href="Events-In-Python.html#Events-In-Python">********7 Events In Python</a></li>
        <li><a name="toc-Threads-In-Python-1" href="Threads-In-Python.html#Threads-In-Python">********8 Threads In Python</a></li>
        <li><a name="toc-Recordings-In-Python-1" href="Recordings-In-Python.html#Recordings-In-Python">********9 Recordings In Python</a></li>
        <li><a name="toc-Commands-In-Python-1" href="Commands-In-Python.html#Commands-In-Python">********0 Commands In Python</a></li>
        <li><a name="toc-Parameters-In-Python-1" href="Parameters-In-Python.html#Parameters-In-Python">********1 Parameters In Python</a></li>
        <li><a name="toc-Writing-new-convenience-functions" href="Functions-In-Python.html#Functions-In-Python">********2 Writing new convenience functions</a></li>
        <li><a name="toc-Program-Spaces-In-Python" href="Progspaces-In-Python.html#Progspaces-In-Python">********3 Program Spaces In Python</a></li>
        <li><a name="toc-Objfiles-In-Python-1" href="Objfiles-In-Python.html#Objfiles-In-Python">********4 Objfiles In Python</a></li>
        <li><a name="toc-Accessing-inferior-stack-frames-from-Python" href="Frames-In-Python.html#Frames-In-Python">********5 Accessing inferior stack frames from Python</a></li>
        <li><a name="toc-Accessing-blocks-from-Python" href="Blocks-In-Python.html#Blocks-In-Python">********6 Accessing blocks from Python</a></li>
        <li><a name="toc-Python-representation-of-Symbols" href="Symbols-In-Python.html#Symbols-In-Python">********7 Python representation of Symbols</a></li>
        <li><a name="toc-Symbol-table-representation-in-Python" href="Symbol-Tables-In-Python.html#Symbol-Tables-In-Python">********8 Symbol table representation in Python</a></li>
        <li><a name="toc-Manipulating-line-tables-using-Python" href="Line-Tables-In-Python.html#Line-Tables-In-Python">********* Manipulating line tables using Python</a></li>
        <li><a name="toc-Manipulating-breakpoints-using-Python" href="Breakpoints-In-Python.html#Breakpoints-In-Python">********* Manipulating breakpoints using Python</a></li>
        <li><a name="toc-Finish-Breakpoints" href="Finish-Breakpoints-in-Python.html#Finish-Breakpoints-in-Python">********* Finish Breakpoints</a></li>
        <li><a name="toc-Python-representation-of-lazy-strings" href="Lazy-Strings-In-Python.html#Lazy-Strings-In-Python">********* Python representation of lazy strings</a></li>
        <li><a name="toc-Python-representation-of-architectures" href="Architectures-In-Python.html#Architectures-In-Python">********* Python representation of architectures</a></li>
        <li><a name="toc-Registers-In-Python-1" href="Registers-In-Python.html#Registers-In-Python">********* Registers In Python</a></li>
        <li><a name="toc-Implementing-new-TUI-windows" href="TUI-Windows-In-Python.html#TUI-Windows-In-Python">********* Implementing new TUI windows</a></li>
      </ul></li>
      <li><a name="toc-Python-Auto_002dloading-1" href="Python-Auto_002dloading.html#Python-Auto_002dloading">23.2.3 Python Auto-loading</a></li>
      <li><a name="toc-Python-modules-1" href="Python-modules.html#Python-modules">23.2.4 Python modules</a>
      <ul class="no-bullet">
        <li><a name="toc-gdb_002eprinting-1" href="gdb_002eprinting.html#gdb_002eprinting">23.2.4.1 gdb.printing</a></li>
        <li><a name="toc-gdb_002etypes-1" href="gdb_002etypes.html#gdb_002etypes">23.2.4.2 gdb.types</a></li>
        <li><a name="toc-gdb_002eprompt-1" href="gdb_002eprompt.html#gdb_002eprompt">23.2.4.3 gdb.prompt</a></li>
      </ul></li>
    </ul></li>
    <li><a name="toc-Extending-GDB-using-Guile" href="Guile.html#Guile">23.3 Extending <small>GDB</small> using Guile</a>
    <ul class="no-bullet">
      <li><a name="toc-Guile-Introduction-1" href="Guile-Introduction.html#Guile-Introduction">23.3.1 Guile Introduction</a></li>
      <li><a name="toc-Guile-Commands-1" href="Guile-Commands.html#Guile-Commands">23.3.2 Guile Commands</a></li>
      <li><a name="toc-Guile-API-1" href="Guile-API.html#Guile-API">23.3.3 Guile API</a>
      <ul class="no-bullet">
        <li><a name="toc-Basic-Guile-1" href="Basic-Guile.html#Basic-Guile">23.3.3.1 Basic Guile</a></li>
        <li><a name="toc-Guile-Configuration-1" href="Guile-Configuration.html#Guile-Configuration">23.3.3.2 Guile Configuration</a></li>
        <li><a name="toc-GDB-Scheme-Data-Types-1" href="GDB-Scheme-Data-Types.html#GDB-Scheme-Data-Types">23.3.3.3 GDB Scheme Data Types</a></li>
        <li><a name="toc-Guile-Exception-Handling-1" href="Guile-Exception-Handling.html#Guile-Exception-Handling">23.3.3.4 Guile Exception Handling</a></li>
        <li><a name="toc-Values-From-Inferior-In-Guile-1" href="Values-From-Inferior-In-Guile.html#Values-From-Inferior-In-Guile">23.3.3.5 Values From Inferior In Guile</a></li>
        <li><a name="toc-Arithmetic-In-Guile-1" href="Arithmetic-In-Guile.html#Arithmetic-In-Guile">23.3.3.6 Arithmetic In Guile</a></li>
        <li><a name="toc-Types-In-Guile-1" href="Types-In-Guile.html#Types-In-Guile">23.3.3.7 Types In Guile</a></li>
        <li><a name="toc-Guile-Pretty-Printing-API-1" href="Guile-Pretty-Printing-API.html#Guile-Pretty-Printing-API">23.3.3.8 Guile Pretty Printing API</a></li>
        <li><a name="toc-Selecting-Guile-Pretty_002dPrinters-1" href="Selecting-Guile-Pretty_002dPrinters.html#Selecting-Guile-Pretty_002dPrinters">23.3.3.9 Selecting Guile Pretty-Printers</a></li>
        <li><a name="toc-Writing-a-Guile-Pretty_002dPrinter-1" href="Writing-a-Guile-Pretty_002dPrinter.html#Writing-a-Guile-Pretty_002dPrinter">23.3.3.10 Writing a Guile Pretty-Printer</a></li>
        <li><a name="toc-Commands-In-Guile-1" href="Commands-In-Guile.html#Commands-In-Guile">23.3.3.11 Commands In Guile</a></li>
        <li><a name="toc-Parameters-In-Guile-1" href="Parameters-In-Guile.html#Parameters-In-Guile">23.3.3.12 Parameters In Guile</a></li>
        <li><a name="toc-Program-Spaces-In-Guile" href="Progspaces-In-Guile.html#Progspaces-In-Guile">23.3.3.13 Program Spaces In Guile</a></li>
        <li><a name="toc-Objfiles-In-Guile-1" href="Objfiles-In-Guile.html#Objfiles-In-Guile">23.3.3.14 Objfiles In Guile</a></li>
        <li><a name="toc-Accessing-inferior-stack-frames-from-Guile_002e" href="Frames-In-Guile.html#Frames-In-Guile">23.3.3.15 Accessing inferior stack frames from Guile.</a></li>
        <li><a name="toc-Accessing-blocks-from-Guile_002e" href="Blocks-In-Guile.html#Blocks-In-Guile">********* Accessing blocks from Guile.</a></li>
        <li><a name="toc-Guile-representation-of-Symbols_002e" href="Symbols-In-Guile.html#Symbols-In-Guile">********* Guile representation of Symbols.</a></li>
        <li><a name="toc-Symbol-table-representation-in-Guile_002e" href="Symbol-Tables-In-Guile.html#Symbol-Tables-In-Guile">********* Symbol table representation in Guile.</a></li>
        <li><a name="toc-Manipulating-breakpoints-using-Guile" href="Breakpoints-In-Guile.html#Breakpoints-In-Guile">********* Manipulating breakpoints using Guile</a></li>
        <li><a name="toc-Guile-representation-of-lazy-strings_002e" href="Lazy-Strings-In-Guile.html#Lazy-Strings-In-Guile">********* Guile representation of lazy strings.</a></li>
        <li><a name="toc-Guile-representation-of-architectures" href="Architectures-In-Guile.html#Architectures-In-Guile">********* Guile representation of architectures</a></li>
        <li><a name="toc-Disassembly-In-Guile-1" href="Disassembly-In-Guile.html#Disassembly-In-Guile">********* Disassembly In Guile</a></li>
        <li><a name="toc-I_002fO-Ports-in-Guile-1" href="I_002fO-Ports-in-Guile.html#I_002fO-Ports-in-Guile">********* I/O Ports in Guile</a></li>
        <li><a name="toc-Memory-Ports-in-Guile-1" href="Memory-Ports-in-Guile.html#Memory-Ports-in-Guile">********* Memory Ports in Guile</a></li>
        <li><a name="toc-Iterators-In-Guile-1" href="Iterators-In-Guile.html#Iterators-In-Guile">********* Iterators In Guile</a></li>
      </ul></li>
      <li><a name="toc-Guile-Auto_002dloading-1" href="Guile-Auto_002dloading.html#Guile-Auto_002dloading">23.3.4 Guile Auto-loading</a></li>
      <li><a name="toc-Guile-Modules-1" href="Guile-Modules.html#Guile-Modules">23.3.5 Guile Modules</a>
      <ul class="no-bullet">
        <li><a name="toc-Guile-Printing-Module-1" href="Guile-Printing-Module.html#Guile-Printing-Module">23.3.5.1 Guile Printing Module</a></li>
        <li><a name="toc-Guile-Types-Module-1" href="Guile-Types-Module.html#Guile-Types-Module">******** Guile Types Module</a></li>
      </ul></li>
    </ul></li>
    <li><a name="toc-Auto_002dLoading-Extensions" href="Auto_002dloading-extensions.html#Auto_002dloading-extensions">23.4 Auto-Loading Extensions</a>
    <ul class="no-bullet">
      <li><a name="toc-The-objfile_002dgdb_002eext-File" href="objfile_002dgdbdotext-file.html#objfile_002dgdbdotext-file">23.4.1 The <samp><var>objfile</var>-gdb.<var>ext</var></samp> File</a></li>
      <li><a name="toc-The-_002edebug_005fgdb_005fscripts-Section" href="dotdebug_005fgdb_005fscripts-section.html#dotdebug_005fgdb_005fscripts-section">23.4.2 The <code>.debug_gdb_scripts</code> Section</a>
      <ul class="no-bullet">
        <li><a name="toc-Script-File-Entries" href="dotdebug_005fgdb_005fscripts-section.html#Script-File-Entries">******** Script File Entries</a></li>
        <li><a name="toc-Script-Text-Entries" href="dotdebug_005fgdb_005fscripts-section.html#Script-Text-Entries">******** Script Text Entries</a></li>
      </ul></li>
      <li><a name="toc-Which-Flavor-to-Choose_003f" href="Which-flavor-to-choose_003f.html#Which-flavor-to-choose_003f">23.4.3 Which Flavor to Choose?</a></li>
    </ul></li>
    <li><a name="toc-Multiple-Extension-Languages-1" href="Multiple-Extension-Languages.html#Multiple-Extension-Languages">23.5 Multiple Extension Languages</a>
    <ul class="no-bullet">
      <li><a name="toc-Python-Comes-First" href="Multiple-Extension-Languages.html#Python-Comes-First">23.5.1 Python Comes First</a></li>
    </ul></li>
    <li><a name="toc-Creating-New-Spellings-of-Existing-Commands" href="Aliases.html#Aliases">23.6 Creating New Spellings of Existing Commands</a></li>
  </ul></li>
  <li><a name="toc-Command-Interpreters" href="Interpreters.html#Interpreters">24 Command Interpreters</a></li>
  <li><a name="toc-GDB-Text-User-Interface" href="TUI.html#TUI">25 <small>GDB</small> Text User Interface</a>
  <ul class="no-bullet">
    <li><a name="toc-TUI-Overview-1" href="TUI-Overview.html#TUI-Overview">25.1 TUI Overview</a></li>
    <li><a name="toc-TUI-Key-Bindings" href="TUI-Keys.html#TUI-Keys">25.2 TUI Key Bindings</a></li>
    <li><a name="toc-TUI-Single-Key-Mode-1" href="TUI-Single-Key-Mode.html#TUI-Single-Key-Mode">25.3 TUI Single Key Mode</a></li>
    <li><a name="toc-TUI_002dSpecific-Commands" href="TUI-Commands.html#TUI-Commands">25.4 TUI-Specific Commands</a></li>
    <li><a name="toc-TUI-Configuration-Variables" href="TUI-Configuration.html#TUI-Configuration">25.5 TUI Configuration Variables</a></li>
  </ul></li>
  <li><a name="toc-Using-GDB-under-GNU-Emacs" href="Emacs.html#Emacs">26 Using <small>GDB</small> under <small>GNU</small> Emacs</a></li>
  <li><a name="toc-The-GDB_002fMI-Interface" href="GDB_002fMI.html#GDB_002fMI">27 The <small>GDB/MI</small> Interface</a>
  <ul class="no-bullet">
    <li><a name="toc-Function-and-Purpose" href="GDB_002fMI.html#Function-and-Purpose">Function and Purpose</a></li>
    <li><a name="toc-Notation-and-Terminology" href="GDB_002fMI.html#Notation-and-Terminology">Notation and Terminology</a></li>
    <li><a name="toc-GDB_002fMI-General-Design-1" href="GDB_002fMI-General-Design.html#GDB_002fMI-General-Design">27.1 <small>GDB/MI</small> General Design</a>
    <ul class="no-bullet">
      <li><a name="toc-Context-Management" href="Context-management.html#Context-management">27.1.1 Context Management</a>
      <ul class="no-bullet">
        <li><a name="toc-Threads-and-Frames" href="Context-management.html#Threads-and-Frames">27.1.1.1 Threads and Frames</a></li>
        <li><a name="toc-Language" href="Context-management.html#Language">27.1.1.2 Language</a></li>
      </ul></li>
      <li><a name="toc-Asynchronous-Command-Execution-and-Non_002dStop-Mode" href="Asynchronous-and-non_002dstop-modes.html#Asynchronous-and-non_002dstop-modes">27.1.2 Asynchronous Command Execution and Non-Stop Mode</a></li>
      <li><a name="toc-Thread-Groups" href="Thread-groups.html#Thread-groups">27.1.3 Thread Groups</a></li>
    </ul></li>
    <li><a name="toc-GDB_002fMI-Command-Syntax-1" href="GDB_002fMI-Command-Syntax.html#GDB_002fMI-Command-Syntax">27.2 <small>GDB/MI</small> Command Syntax</a>
    <ul class="no-bullet">
      <li><a name="toc-GDB_002fMI-Input-Syntax-1" href="GDB_002fMI-Input-Syntax.html#GDB_002fMI-Input-Syntax">27.2.1 <small>GDB/MI</small> Input Syntax</a></li>
      <li><a name="toc-GDB_002fMI-Output-Syntax-1" href="GDB_002fMI-Output-Syntax.html#GDB_002fMI-Output-Syntax">27.2.2 <small>GDB/MI</small> Output Syntax</a></li>
    </ul></li>
    <li><a name="toc-GDB_002fMI-Compatibility-with-CLI-1" href="GDB_002fMI-Compatibility-with-CLI.html#GDB_002fMI-Compatibility-with-CLI">27.3 <small>GDB/MI</small> Compatibility with CLI</a></li>
    <li><a name="toc-GDB_002fMI-Development-and-Front-Ends-1" href="GDB_002fMI-Development-and-Front-Ends.html#GDB_002fMI-Development-and-Front-Ends">27.4 <small>GDB/MI</small> Development and Front Ends</a></li>
    <li><a name="toc-GDB_002fMI-Output-Records-1" href="GDB_002fMI-Output-Records.html#GDB_002fMI-Output-Records">27.5 <small>GDB/MI</small> Output Records</a>
    <ul class="no-bullet">
      <li><a name="toc-GDB_002fMI-Result-Records-1" href="GDB_002fMI-Result-Records.html#GDB_002fMI-Result-Records">27.5.1 <small>GDB/MI</small> Result Records</a></li>
      <li><a name="toc-GDB_002fMI-Stream-Records-1" href="GDB_002fMI-Stream-Records.html#GDB_002fMI-Stream-Records">27.5.2 <small>GDB/MI</small> Stream Records</a></li>
      <li><a name="toc-GDB_002fMI-Async-Records-1" href="GDB_002fMI-Async-Records.html#GDB_002fMI-Async-Records">27.5.3 <small>GDB/MI</small> Async Records</a></li>
      <li><a name="toc-GDB_002fMI-Breakpoint-Information-1" href="GDB_002fMI-Breakpoint-Information.html#GDB_002fMI-Breakpoint-Information">27.5.4 <small>GDB/MI</small> Breakpoint Information</a></li>
      <li><a name="toc-GDB_002fMI-Frame-Information-1" href="GDB_002fMI-Frame-Information.html#GDB_002fMI-Frame-Information">27.5.5 <small>GDB/MI</small> Frame Information</a></li>
      <li><a name="toc-GDB_002fMI-Thread-Information-1" href="GDB_002fMI-Thread-Information.html#GDB_002fMI-Thread-Information">27.5.6 <small>GDB/MI</small> Thread Information</a></li>
      <li><a name="toc-GDB_002fMI-Ada-Exception-Information-1" href="GDB_002fMI-Ada-Exception-Information.html#GDB_002fMI-Ada-Exception-Information">27.5.7 <small>GDB/MI</small> Ada Exception Information</a></li>
    </ul></li>
    <li><a name="toc-Simple-Examples-of-GDB_002fMI-Interaction" href="GDB_002fMI-Simple-Examples.html#GDB_002fMI-Simple-Examples">27.6 Simple Examples of <small>GDB/MI</small> Interaction</a></li>
    <li><a name="toc-GDB_002fMI-Command-Description-Format-1" href="GDB_002fMI-Command-Description-Format.html#GDB_002fMI-Command-Description-Format">27.7 <small>GDB/MI</small> Command Description Format</a></li>
    <li><a name="toc-GDB_002fMI-Breakpoint-Commands-1" href="GDB_002fMI-Breakpoint-Commands.html#GDB_002fMI-Breakpoint-Commands">27.8 <small>GDB/MI</small> Breakpoint Commands</a></li>
    <li><a name="toc-GDB_002fMI-Catchpoint-Commands-1" href="GDB_002fMI-Catchpoint-Commands.html#GDB_002fMI-Catchpoint-Commands">27.9 <small>GDB/MI</small> Catchpoint Commands</a>
    <ul class="no-bullet">
      <li><a name="toc-Shared-Library-GDB_002fMI-Catchpoints" href="Shared-Library-GDB_002fMI-Catchpoint-Commands.html#Shared-Library-GDB_002fMI-Catchpoint-Commands">27.9.1 Shared Library <small>GDB/MI</small> Catchpoints</a></li>
      <li><a name="toc-Ada-Exception-GDB_002fMI-Catchpoints" href="Ada-Exception-GDB_002fMI-Catchpoint-Commands.html#Ada-Exception-GDB_002fMI-Catchpoint-Commands">27.9.2 Ada Exception <small>GDB/MI</small> Catchpoints</a></li>
      <li><a name="toc-C_002b_002b-Exception-GDB_002fMI-Catchpoints" href="C_002b_002b-Exception-GDB_002fMI-Catchpoint-Commands.html#C_002b_002b-Exception-GDB_002fMI-Catchpoint-Commands">27.9.3 C<tt>++</tt> Exception <small>GDB/MI</small> Catchpoints</a></li>
    </ul></li>
    <li><a name="toc-GDB_002fMI-Program-Context-1" href="GDB_002fMI-Program-Context.html#GDB_002fMI-Program-Context">27.10 <small>GDB/MI</small> Program Context</a></li>
    <li><a name="toc-GDB_002fMI-Thread-Commands-1" href="GDB_002fMI-Thread-Commands.html#GDB_002fMI-Thread-Commands">27.11 <small>GDB/MI</small> Thread Commands</a></li>
    <li><a name="toc-GDB_002fMI-Ada-Tasking-Commands-1" href="GDB_002fMI-Ada-Tasking-Commands.html#GDB_002fMI-Ada-Tasking-Commands">27.12 <small>GDB/MI</small> Ada Tasking Commands</a></li>
    <li><a name="toc-GDB_002fMI-Program-Execution-1" href="GDB_002fMI-Program-Execution.html#GDB_002fMI-Program-Execution">27.13 <small>GDB/MI</small> Program Execution</a></li>
    <li><a name="toc-GDB_002fMI-Stack-Manipulation-Commands" href="GDB_002fMI-Stack-Manipulation.html#GDB_002fMI-Stack-Manipulation">27.14 <small>GDB/MI</small> Stack Manipulation Commands</a></li>
    <li><a name="toc-GDB_002fMI-Variable-Objects-1" href="GDB_002fMI-Variable-Objects.html#GDB_002fMI-Variable-Objects">27.15 <small>GDB/MI</small> Variable Objects</a></li>
    <li><a name="toc-GDB_002fMI-Data-Manipulation-1" href="GDB_002fMI-Data-Manipulation.html#GDB_002fMI-Data-Manipulation">27.16 <small>GDB/MI</small> Data Manipulation</a></li>
    <li><a name="toc-GDB_002fMI-Tracepoint-Commands-1" href="GDB_002fMI-Tracepoint-Commands.html#GDB_002fMI-Tracepoint-Commands">27.17 <small>GDB/MI</small> Tracepoint Commands</a></li>
    <li><a name="toc-GDB_002fMI-Symbol-Query-Commands" href="GDB_002fMI-Symbol-Query.html#GDB_002fMI-Symbol-Query">27.18 <small>GDB/MI</small> Symbol Query Commands</a></li>
    <li><a name="toc-GDB_002fMI-File-Commands-1" href="GDB_002fMI-File-Commands.html#GDB_002fMI-File-Commands">27.19 <small>GDB/MI</small> File Commands</a></li>
    <li><a name="toc-GDB_002fMI-Target-Manipulation-Commands" href="GDB_002fMI-Target-Manipulation.html#GDB_002fMI-Target-Manipulation">27.20 <small>GDB/MI</small> Target Manipulation Commands</a></li>
    <li><a name="toc-GDB_002fMI-File-Transfer-Commands-1" href="GDB_002fMI-File-Transfer-Commands.html#GDB_002fMI-File-Transfer-Commands">27.21 <small>GDB/MI</small> File Transfer Commands</a></li>
    <li><a name="toc-Ada-Exceptions-GDB_002fMI-Commands" href="GDB_002fMI-Ada-Exceptions-Commands.html#GDB_002fMI-Ada-Exceptions-Commands">27.22 Ada Exceptions <small>GDB/MI</small> Commands</a></li>
    <li><a name="toc-GDB_002fMI-Support-Commands-1" href="GDB_002fMI-Support-Commands.html#GDB_002fMI-Support-Commands">27.23 <small>GDB/MI</small> Support Commands</a></li>
    <li><a name="toc-Miscellaneous-GDB_002fMI-Commands" href="GDB_002fMI-Miscellaneous-Commands.html#GDB_002fMI-Miscellaneous-Commands">27.24 Miscellaneous <small>GDB/MI</small> Commands</a></li>
  </ul></li>
  <li><a name="toc-GDB-Annotations" href="Annotations.html#Annotations">28 <small>GDB</small> Annotations</a>
  <ul class="no-bullet">
    <li><a name="toc-What-Is-an-Annotation_003f" href="Annotations-Overview.html#Annotations-Overview">28.1 What Is an Annotation?</a></li>
    <li><a name="toc-The-Server-Prefix" href="Server-Prefix.html#Server-Prefix">28.2 The Server Prefix</a></li>
    <li><a name="toc-Annotation-for-GDB-Input" href="Prompting.html#Prompting">28.3 Annotation for <small>GDB</small> Input</a></li>
    <li><a name="toc-Errors-1" href="Errors.html#Errors">28.4 Errors</a></li>
    <li><a name="toc-Invalidation-Notices" href="Invalidation.html#Invalidation">28.5 Invalidation Notices</a></li>
    <li><a name="toc-Running-the-Program" href="Annotations-for-Running.html#Annotations-for-Running">28.6 Running the Program</a></li>
    <li><a name="toc-Displaying-Source" href="Source-Annotations.html#Source-Annotations">28.7 Displaying Source</a></li>
  </ul></li>
  <li><a name="toc-JIT-Compilation-Interface" href="JIT-Interface.html#JIT-Interface">29 JIT Compilation Interface</a>
  <ul class="no-bullet">
    <li><a name="toc-JIT-Declarations" href="Declarations.html#Declarations">29.1 JIT Declarations</a></li>
    <li><a name="toc-Registering-Code-1" href="Registering-Code.html#Registering-Code">29.2 Registering Code</a></li>
    <li><a name="toc-Unregistering-Code-1" href="Unregistering-Code.html#Unregistering-Code">29.3 Unregistering Code</a></li>
    <li><a name="toc-Custom-Debug-Info-1" href="Custom-Debug-Info.html#Custom-Debug-Info">29.4 Custom Debug Info</a>
    <ul class="no-bullet">
      <li><a name="toc-Using-JIT-Debug-Info-Readers-1" href="Using-JIT-Debug-Info-Readers.html#Using-JIT-Debug-Info-Readers">29.4.1 Using JIT Debug Info Readers</a></li>
      <li><a name="toc-Writing-JIT-Debug-Info-Readers-1" href="Writing-JIT-Debug-Info-Readers.html#Writing-JIT-Debug-Info-Readers">29.4.2 Writing JIT Debug Info Readers</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-In_002dprocess-Agent-1" href="In_002dprocess-Agent.html#In_002dprocess-Agent">30 In-process Agent</a>
  <ul class="no-bullet">
    <li><a name="toc-In_002dprocess-Agent-Protocol-1" href="In_002dprocess-Agent-Protocol.html#In_002dprocess-Agent-Protocol">30.1 In-process Agent Protocol</a>
    <ul class="no-bullet">
      <li><a name="toc-IPA-Protocol-Objects-1" href="IPA-Protocol-Objects.html#IPA-Protocol-Objects">30.1.1 IPA Protocol Objects</a></li>
      <li><a name="toc-IPA-Protocol-Commands-1" href="IPA-Protocol-Commands.html#IPA-Protocol-Commands">30.1.2 IPA Protocol Commands</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Reporting-Bugs-in-GDB" href="GDB-Bugs.html#GDB-Bugs">31 Reporting Bugs in <small>GDB</small></a>
  <ul class="no-bullet">
    <li><a name="toc-Have-You-Found-a-Bug_003f" href="Bug-Criteria.html#Bug-Criteria">31.1 Have You Found a Bug?</a></li>
    <li><a name="toc-How-to-Report-Bugs" href="Bug-Reporting.html#Bug-Reporting">31.2 How to Report Bugs</a></li>
  </ul></li>
  <li><a name="toc-Command-Line-Editing-1" href="Command-Line-Editing.html#Command-Line-Editing">32 Command Line Editing</a>
  <ul class="no-bullet">
    <li><a name="toc-Introduction-to-Line-Editing" href="Introduction-and-Notation.html#Introduction-and-Notation">32.1 Introduction to Line Editing</a></li>
    <li><a name="toc-Readline-Interaction-1" href="Readline-Interaction.html#Readline-Interaction">32.2 Readline Interaction</a>
    <ul class="no-bullet">
      <li><a name="toc-Readline-Bare-Essentials-1" href="Readline-Bare-Essentials.html#Readline-Bare-Essentials">32.2.1 Readline Bare Essentials</a></li>
      <li><a name="toc-Readline-Movement-Commands-1" href="Readline-Movement-Commands.html#Readline-Movement-Commands">32.2.2 Readline Movement Commands</a></li>
      <li><a name="toc-Readline-Killing-Commands-1" href="Readline-Killing-Commands.html#Readline-Killing-Commands">32.2.3 Readline Killing Commands</a></li>
      <li><a name="toc-Readline-Arguments-1" href="Readline-Arguments.html#Readline-Arguments">32.2.4 Readline Arguments</a></li>
      <li><a name="toc-Searching-for-Commands-in-the-History" href="Searching.html#Searching">32.2.5 Searching for Commands in the History</a></li>
    </ul></li>
    <li><a name="toc-Readline-Init-File-1" href="Readline-Init-File.html#Readline-Init-File">32.3 Readline Init File</a>
    <ul class="no-bullet">
      <li><a name="toc-Readline-Init-File-Syntax-1" href="Readline-Init-File-Syntax.html#Readline-Init-File-Syntax">32.3.1 Readline Init File Syntax</a></li>
      <li><a name="toc-Conditional-Init-Constructs-1" href="Conditional-Init-Constructs.html#Conditional-Init-Constructs">32.3.2 Conditional Init Constructs</a></li>
      <li><a name="toc-Sample-Init-File-1" href="Sample-Init-File.html#Sample-Init-File">32.3.3 Sample Init File</a></li>
    </ul></li>
    <li><a name="toc-Bindable-Readline-Commands-1" href="Bindable-Readline-Commands.html#Bindable-Readline-Commands">32.4 Bindable Readline Commands</a>
    <ul class="no-bullet">
      <li><a name="toc-Commands-For-Moving-1" href="Commands-For-Moving.html#Commands-For-Moving">32.4.1 Commands For Moving</a></li>
      <li><a name="toc-Commands-For-Manipulating-The-History" href="Commands-For-History.html#Commands-For-History">32.4.2 Commands For Manipulating The History</a></li>
      <li><a name="toc-Commands-For-Changing-Text" href="Commands-For-Text.html#Commands-For-Text">32.4.3 Commands For Changing Text</a></li>
      <li><a name="toc-Killing-And-Yanking" href="Commands-For-Killing.html#Commands-For-Killing">32.4.4 Killing And Yanking</a></li>
      <li><a name="toc-Specifying-Numeric-Arguments" href="Numeric-Arguments.html#Numeric-Arguments">32.4.5 Specifying Numeric Arguments</a></li>
      <li><a name="toc-Letting-Readline-Type-For-You" href="Commands-For-Completion.html#Commands-For-Completion">32.4.6 Letting Readline Type For You</a></li>
      <li><a name="toc-Keyboard-Macros-1" href="Keyboard-Macros.html#Keyboard-Macros">32.4.7 Keyboard Macros</a></li>
      <li><a name="toc-Some-Miscellaneous-Commands" href="Miscellaneous-Commands.html#Miscellaneous-Commands">32.4.8 Some Miscellaneous Commands</a></li>
    </ul></li>
    <li><a name="toc-Readline-vi-Mode-1" href="Readline-vi-Mode.html#Readline-vi-Mode">32.5 Readline vi Mode</a></li>
  </ul></li>
  <li><a name="toc-Using-History-Interactively-1" href="Using-History-Interactively.html#Using-History-Interactively">33 Using History Interactively</a>
  <ul class="no-bullet">
    <li><a name="toc-History-Expansion" href="History-Interaction.html#History-Interaction">33.1 History Expansion</a>
    <ul class="no-bullet">
      <li><a name="toc-Event-Designators-1" href="Event-Designators.html#Event-Designators">33.1.1 Event Designators</a></li>
      <li><a name="toc-Word-Designators-1" href="Word-Designators.html#Word-Designators">33.1.2 Word Designators</a></li>
      <li><a name="toc-Modifiers-1" href="Modifiers.html#Modifiers">33.1.3 Modifiers</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-In-Memoriam-1" href="In-Memoriam.html#In-Memoriam">Appendix A In Memoriam</a></li>
  <li><a name="toc-Formatting-Documentation-1" href="Formatting-Documentation.html#Formatting-Documentation">Appendix B Formatting Documentation</a></li>
  <li><a name="toc-Installing-GDB-1" href="Installing-GDB.html#Installing-GDB">Appendix C Installing <small>GDB</small></a>
  <ul class="no-bullet">
    <li><a name="toc-Requirements-for-Building-GDB" href="Requirements.html#Requirements">C.1 Requirements for Building <small>GDB</small></a></li>
    <li><a name="toc-Invoking-the-GDB-configure-Script" href="Running-Configure.html#Running-Configure">C.2 Invoking the <small>GDB</small> <samp>configure</samp> Script</a></li>
    <li><a name="toc-Compiling-GDB-in-Another-Directory" href="Separate-Objdir.html#Separate-Objdir">C.3 Compiling <small>GDB</small> in Another Directory</a></li>
    <li><a name="toc-Specifying-Names-for-Hosts-and-Targets" href="Config-Names.html#Config-Names">C.4 Specifying Names for Hosts and Targets</a></li>
    <li><a name="toc-configure-Options" href="Configure-Options.html#Configure-Options">C.5 <samp>configure</samp> Options</a></li>
    <li><a name="toc-System_002dWide-Configuration-and-Settings" href="System_002dwide-configuration.html#System_002dwide-configuration">C.6 System-Wide Configuration and Settings</a>
    <ul class="no-bullet">
      <li><a name="toc-Installed-System_002dWide-Configuration-Scripts" href="System_002dwide-Configuration-Scripts.html#System_002dwide-Configuration-Scripts">C.6.1 Installed System-Wide Configuration Scripts</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Maintenance-Commands-1" href="Maintenance-Commands.html#Maintenance-Commands">Appendix D Maintenance Commands</a></li>
  <li><a name="toc-GDB-Remote-Serial-Protocol" href="Remote-Protocol.html#Remote-Protocol">Appendix E <small>GDB</small> Remote Serial Protocol</a>
  <ul class="no-bullet">
    <li><a name="toc-Overview-1" href="Overview.html#Overview">E.1 Overview</a></li>
    <li><a name="toc-Packets-1" href="Packets.html#Packets">E.2 Packets</a></li>
    <li><a name="toc-Stop-Reply-Packets-1" href="Stop-Reply-Packets.html#Stop-Reply-Packets">E.3 Stop Reply Packets</a></li>
    <li><a name="toc-General-Query-Packets-1" href="General-Query-Packets.html#General-Query-Packets">E.4 General Query Packets</a></li>
    <li><a name="toc-Architecture_002dSpecific-Protocol-Details-1" href="Architecture_002dSpecific-Protocol-Details.html#Architecture_002dSpecific-Protocol-Details">E.5 Architecture-Specific Protocol Details</a>
    <ul class="no-bullet">
      <li><a name="toc-ARM_002dSpecific-Protocol-Details-1" href="ARM_002dSpecific-Protocol-Details.html#ARM_002dSpecific-Protocol-Details">E.5.1 <acronym>ARM</acronym>-Specific Protocol Details</a>
      <ul class="no-bullet">
        <li><a name="toc-ARM-Breakpoint-Kinds-1" href="ARM-Breakpoint-Kinds.html#ARM-Breakpoint-Kinds">E.5.1.1 <acronym>ARM</acronym> Breakpoint Kinds</a></li>
      </ul></li>
      <li><a name="toc-MIPS_002dSpecific-Protocol-Details-1" href="MIPS_002dSpecific-Protocol-Details.html#MIPS_002dSpecific-Protocol-Details">E.5.2 <acronym>MIPS</acronym>-Specific Protocol Details</a>
      <ul class="no-bullet">
        <li><a name="toc-MIPS-Register-Packet-Format" href="MIPS-Register-packet-Format.html#MIPS-Register-packet-Format">E.5.2.1 <acronym>MIPS</acronym> Register Packet Format</a></li>
        <li><a name="toc-MIPS-Breakpoint-Kinds-1" href="MIPS-Breakpoint-Kinds.html#MIPS-Breakpoint-Kinds">E.5.2.2 <acronym>MIPS</acronym> Breakpoint Kinds</a></li>
      </ul></li>
    </ul></li>
    <li><a name="toc-Tracepoint-Packets-1" href="Tracepoint-Packets.html#Tracepoint-Packets">E.6 Tracepoint Packets</a>
    <ul class="no-bullet">
      <li><a name="toc-Relocate-Instruction-Reply-Packet" href="Tracepoint-Packets.html#Relocate-Instruction-Reply-Packet">E.6.1 Relocate Instruction Reply Packet</a></li>
    </ul></li>
    <li><a name="toc-Host-I_002fO-Packets-1" href="Host-I_002fO-Packets.html#Host-I_002fO-Packets">E.7 Host I/O Packets</a></li>
    <li><a name="toc-Interrupts-1" href="Interrupts.html#Interrupts">E.8 Interrupts</a></li>
    <li><a name="toc-Notification-Packets-1" href="Notification-Packets.html#Notification-Packets">E.9 Notification Packets</a></li>
    <li><a name="toc-Remote-Protocol-Support-for-Non_002dStop-Mode" href="Remote-Non_002dStop.html#Remote-Non_002dStop">E.10 Remote Protocol Support for Non-Stop Mode</a></li>
    <li><a name="toc-Packet-Acknowledgment-1" href="Packet-Acknowledgment.html#Packet-Acknowledgment">E.11 Packet Acknowledgment</a></li>
    <li><a name="toc-Examples-1" href="Examples.html#Examples">E.12 Examples</a></li>
    <li><a name="toc-File_002dI_002fO-Remote-Protocol-Extension-1" href="File_002dI_002fO-Remote-Protocol-Extension.html#File_002dI_002fO-Remote-Protocol-Extension">E.13 File-I/O Remote Protocol Extension</a>
    <ul class="no-bullet">
      <li><a name="toc-File_002dI_002fO-Overview-1" href="File_002dI_002fO-Overview.html#File_002dI_002fO-Overview">E.13.1 File-I/O Overview</a></li>
      <li><a name="toc-Protocol-Basics-1" href="Protocol-Basics.html#Protocol-Basics">E.13.2 Protocol Basics</a></li>
      <li><a name="toc-The-F-Request-Packet-1" href="The-F-Request-Packet.html#The-F-Request-Packet">E.13.3 The <code>F</code> Request Packet</a></li>
      <li><a name="toc-The-F-Reply-Packet-1" href="The-F-Reply-Packet.html#The-F-Reply-Packet">E.13.4 The <code>F</code> Reply Packet</a></li>
      <li><a name="toc-The-Ctrl_002dC-Message-1" href="The-Ctrl_002dC-Message.html#The-Ctrl_002dC-Message">E.13.5 The &lsquo;<samp>Ctrl-C</samp>&rsquo; Message</a></li>
      <li><a name="toc-Console-I_002fO-1" href="Console-I_002fO.html#Console-I_002fO">E.13.6 Console I/O</a></li>
      <li><a name="toc-List-of-Supported-Calls-1" href="List-of-Supported-Calls.html#List-of-Supported-Calls">E.13.7 List of Supported Calls</a>
      <ul class="no-bullet">
        <li><a name="toc-open-1" href="open.html#open">open</a></li>
        <li><a name="toc-close-1" href="close.html#close">close</a></li>
        <li><a name="toc-read-1" href="read.html#read">read</a></li>
        <li><a name="toc-write-1" href="write.html#write">write</a></li>
        <li><a name="toc-lseek-1" href="lseek.html#lseek">lseek</a></li>
        <li><a name="toc-rename-1" href="rename.html#rename">rename</a></li>
        <li><a name="toc-unlink-1" href="unlink.html#unlink">unlink</a></li>
        <li><a name="toc-stat_002ffstat-1" href="stat_002ffstat.html#stat_002ffstat">stat/fstat</a></li>
        <li><a name="toc-gettimeofday-1" href="gettimeofday.html#gettimeofday">gettimeofday</a></li>
        <li><a name="toc-isatty-1" href="isatty.html#isatty">isatty</a></li>
        <li><a name="toc-system-1" href="system.html#system">system</a></li>
      </ul></li>
      <li><a name="toc-Protocol_002dSpecific-Representation-of-Datatypes" href="Protocol_002dspecific-Representation-of-Datatypes.html#Protocol_002dspecific-Representation-of-Datatypes">E.13.8 Protocol-Specific Representation of Datatypes</a>
      <ul class="no-bullet">
        <li><a name="toc-Integral-Datatypes-1" href="Integral-Datatypes.html#Integral-Datatypes">Integral Datatypes</a></li>
        <li><a name="toc-Pointer-Values-1" href="Pointer-Values.html#Pointer-Values">Pointer Values</a></li>
        <li><a name="toc-Memory-Transfer-1" href="Memory-Transfer.html#Memory-Transfer">Memory Transfer</a></li>
        <li><a name="toc-struct-stat-1" href="struct-stat.html#struct-stat">struct stat</a></li>
        <li><a name="toc-struct-timeval-1" href="struct-timeval.html#struct-timeval">struct timeval</a></li>
      </ul></li>
      <li><a name="toc-Constants-2" href="Constants.html#Constants">E.13.9 Constants</a>
      <ul class="no-bullet">
        <li><a name="toc-Open-Flags-1" href="Open-Flags.html#Open-Flags">Open Flags</a></li>
        <li><a name="toc-mode_005ft-Values-1" href="mode_005ft-Values.html#mode_005ft-Values">mode_t Values</a></li>
        <li><a name="toc-errno-Values-1" href="errno-Values.html#errno-Values">errno Values</a></li>
        <li><a name="toc-Lseek-Flags-1" href="Lseek-Flags.html#Lseek-Flags">Lseek Flags</a></li>
        <li><a name="toc-Limits-1" href="Limits.html#Limits">Limits</a></li>
      </ul></li>
      <li><a name="toc-File_002dI_002fO-Examples-1" href="File_002dI_002fO-Examples.html#File_002dI_002fO-Examples">E.13.10 File-I/O Examples</a></li>
    </ul></li>
    <li><a name="toc-Library-List-Format-1" href="Library-List-Format.html#Library-List-Format">E.14 Library List Format</a></li>
    <li><a name="toc-Library-List-Format-for-SVR4-Targets-1" href="Library-List-Format-for-SVR4-Targets.html#Library-List-Format-for-SVR4-Targets">E.15 Library List Format for SVR4 Targets</a></li>
    <li><a name="toc-Memory-Map-Format-1" href="Memory-Map-Format.html#Memory-Map-Format">E.16 Memory Map Format</a></li>
    <li><a name="toc-Thread-List-Format-1" href="Thread-List-Format.html#Thread-List-Format">E.17 Thread List Format</a></li>
    <li><a name="toc-Traceframe-Info-Format-1" href="Traceframe-Info-Format.html#Traceframe-Info-Format">E.18 Traceframe Info Format</a></li>
    <li><a name="toc-Branch-Trace-Format-1" href="Branch-Trace-Format.html#Branch-Trace-Format">E.19 Branch Trace Format</a></li>
    <li><a name="toc-Branch-Trace-Configuration-Format-1" href="Branch-Trace-Configuration-Format.html#Branch-Trace-Configuration-Format">E.20 Branch Trace Configuration Format</a></li>
  </ul></li>
  <li><a name="toc-The-GDB-Agent-Expression-Mechanism" href="Agent-Expressions.html#Agent-Expressions">Appendix F The GDB Agent Expression Mechanism</a>
  <ul class="no-bullet">
    <li><a name="toc-General-Bytecode-Design-1" href="General-Bytecode-Design.html#General-Bytecode-Design">F.1 General Bytecode Design</a></li>
    <li><a name="toc-Bytecode-Descriptions-1" href="Bytecode-Descriptions.html#Bytecode-Descriptions">F.2 Bytecode Descriptions</a></li>
    <li><a name="toc-Using-Agent-Expressions-1" href="Using-Agent-Expressions.html#Using-Agent-Expressions">F.3 Using Agent Expressions</a></li>
    <li><a name="toc-Varying-Target-Capabilities-1" href="Varying-Target-Capabilities.html#Varying-Target-Capabilities">F.4 Varying Target Capabilities</a></li>
    <li><a name="toc-Rationale-1" href="Rationale.html#Rationale">F.5 Rationale</a></li>
  </ul></li>
  <li><a name="toc-Target-Descriptions-1" href="Target-Descriptions.html#Target-Descriptions">Appendix G Target Descriptions</a>
  <ul class="no-bullet">
    <li><a name="toc-Retrieving-Descriptions-1" href="Retrieving-Descriptions.html#Retrieving-Descriptions">G.1 Retrieving Descriptions</a></li>
    <li><a name="toc-Target-Description-Format-1" href="Target-Description-Format.html#Target-Description-Format">G.2 Target Description Format</a>
    <ul class="no-bullet">
      <li><a name="toc-Inclusion" href="Target-Description-Format.html#Inclusion">G.2.1 Inclusion</a></li>
      <li><a name="toc-Architecture" href="Target-Description-Format.html#Architecture">G.2.2 Architecture</a></li>
      <li><a name="toc-OS-ABI" href="Target-Description-Format.html#OS-ABI">G.2.3 OS ABI</a></li>
      <li><a name="toc-Compatible-Architecture" href="Target-Description-Format.html#Compatible-Architecture">G.2.4 Compatible Architecture</a></li>
      <li><a name="toc-Features" href="Target-Description-Format.html#Features">G.2.5 Features</a></li>
      <li><a name="toc-Types" href="Target-Description-Format.html#Types">G.2.6 Types</a></li>
      <li><a name="toc-Registers-2" href="Target-Description-Format.html#Registers-2">G.2.7 Registers</a></li>
    </ul></li>
    <li><a name="toc-Predefined-Target-Types-1" href="Predefined-Target-Types.html#Predefined-Target-Types">G.3 Predefined Target Types</a></li>
    <li><a name="toc-Enum-Target-Types-1" href="Enum-Target-Types.html#Enum-Target-Types">G.4 Enum Target Types</a></li>
    <li><a name="toc-Standard-Target-Features-1" href="Standard-Target-Features.html#Standard-Target-Features">G.5 Standard Target Features</a>
    <ul class="no-bullet">
      <li><a name="toc-AArch64-Features-1" href="AArch64-Features.html#AArch64-Features">G.5.1 AArch64 Features</a></li>
      <li><a name="toc-ARC-Features-1" href="ARC-Features.html#ARC-Features">G.5.2 ARC Features</a></li>
      <li><a name="toc-ARM-Features-1" href="ARM-Features.html#ARM-Features">G.5.3 ARM Features</a></li>
      <li><a name="toc-i386-Features-1" href="i386-Features.html#i386-Features">G.5.4 i386 Features</a></li>
      <li><a name="toc-MicroBlaze-Features-1" href="MicroBlaze-Features.html#MicroBlaze-Features">G.5.5 MicroBlaze Features</a></li>
      <li><a name="toc-MIPS-Features-1" href="MIPS-Features.html#MIPS-Features">G.5.6 <acronym>MIPS</acronym> Features</a></li>
      <li><a name="toc-M68K-Features-1" href="M68K-Features.html#M68K-Features">G.5.7 M68K Features</a></li>
      <li><a name="toc-NDS32-Features-1" href="NDS32-Features.html#NDS32-Features">G.5.8 NDS32 Features</a></li>
      <li><a name="toc-Nios-II-Features-1" href="Nios-II-Features.html#Nios-II-Features">G.5.9 Nios II Features</a></li>
      <li><a name="toc-OpenRISC-1000-Features-1" href="OpenRISC-1000-Features.html#OpenRISC-1000-Features">G.5.10 OpenRISC 1000 Features</a></li>
      <li><a name="toc-PowerPC-Features-1" href="PowerPC-Features.html#PowerPC-Features">G.5.11 PowerPC Features</a></li>
      <li><a name="toc-RISC_002dV-Features-1" href="RISC_002dV-Features.html#RISC_002dV-Features">G.5.12 RISC-V Features</a></li>
      <li><a name="toc-RX-Features-1" href="RX-Features.html#RX-Features">G.5.13 RX Features</a></li>
      <li><a name="toc-S_002f390-and-System-z-Features-1" href="S_002f390-and-System-z-Features.html#S_002f390-and-System-z-Features">G.5.14 S/390 and System z Features</a></li>
      <li><a name="toc-Sparc-Features-1" href="Sparc-Features.html#Sparc-Features">G.5.15 Sparc Features</a></li>
      <li><a name="toc-TMS320C6x-Features" href="TIC6x-Features.html#TIC6x-Features">G.5.16 TMS320C6x Features</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Operating-System-Information-1" href="Operating-System-Information.html#Operating-System-Information">Appendix H Operating System Information</a>
  <ul class="no-bullet">
    <li><a name="toc-Process-list-1" href="Process-list.html#Process-list">H.1 Process list</a></li>
  </ul></li>
  <li><a name="toc-Trace-File-Format-1" href="Trace-File-Format.html#Trace-File-Format">Appendix I Trace File Format</a></li>
  <li><a name="toc-_002egdb_005findex-section-format" href="Index-Section-Format.html#Index-Section-Format">Appendix J <code>.gdb_index</code> section format</a></li>
  <li><a name="toc-Manual-pages" href="Man-Pages.html#Man-Pages">Appendix K Manual pages</a></li>
  <li><a name="toc-GNU-GENERAL-PUBLIC-LICENSE" href="Copying.html#Copying">Appendix L GNU GENERAL PUBLIC LICENSE</a></li>
  <li><a name="toc-GNU-Free-Documentation-License-1" href="GNU-Free-Documentation-License.html#GNU-Free-Documentation-License">Appendix M GNU Free Documentation License</a></li>
  <li><a name="toc-Concept-Index-1" href="Concept-Index.html#Concept-Index">Concept Index</a></li>
  <li><a name="toc-Command_002c-Variable_002c-and-Function-Index" href="Command-and-Variable-Index.html#Command-and-Variable-Index">Command, Variable, and Function Index</a></li>
</ul>
</div>


<hr>
<div class="header">
<p>
Next: <a href="Summary.html#Summary" accesskey="n" rel="next">Summary</a> &nbsp; [<a href="#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
