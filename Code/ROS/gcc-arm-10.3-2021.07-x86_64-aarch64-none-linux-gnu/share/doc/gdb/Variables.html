<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Variables</title>

<meta name="description" content="Debugging with GDB: Variables">
<meta name="keywords" content="Debugging with GDB: Variables">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Data.html#Data" rel="up" title="Data">
<link href="Arrays.html#Arrays" rel="next" title="Arrays">
<link href="Ambiguous-Expressions.html#Ambiguous-Expressions" rel="previous" title="Ambiguous Expressions">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Variables"></a>
<div class="header">
<p>
Next: <a href="Arrays.html#Arrays" accesskey="n" rel="next">Arrays</a>, Previous: <a href="Ambiguous-Expressions.html#Ambiguous-Expressions" accesskey="p" rel="previous">Ambiguous Expressions</a>, Up: <a href="Data.html#Data" accesskey="u" rel="up">Data</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Program-Variables"></a>
<h3 class="section">10.3 Program Variables</h3>

<p>The most common kind of expression to use is the name of a variable
in your program.
</p>
<p>Variables in expressions are understood in the selected stack frame
(see <a href="Selection.html#Selection">Selecting a Frame</a>); they must be either:
</p>
<ul>
<li> global (or file-static)
</li></ul>

<p>or
</p>
<ul>
<li> visible according to the scope rules of the
programming language from the point of execution in that frame
</li></ul>

<p>This means that in the function
</p>
<div class="smallexample">
<pre class="smallexample">foo (a)
     int a;
{
  bar (a);
  {
    int b = test ();
    bar (b);
  }
}
</pre></div>

<p>you can examine and use the variable <code>a</code> whenever your program is
executing within the function <code>foo</code>, but you can only use or
examine the variable <code>b</code> while your program is executing inside
the block where <code>b</code> is declared.
</p>
<a name="index-variable-name-conflict"></a>
<p>There is an exception: you can refer to a variable or function whose
scope is a single source file even if the current execution point is not
in this file.  But it is possible to have more than one such variable or
function with the same name (in different source files).  If that
happens, referring to that name has unpredictable effects.  If you wish,
you can specify a static variable in a particular function or file by
using the colon-colon (<code>::</code>) notation:
</p>
<a name="index-colon_002dcolon_002c-context-for-variables_002ffunctions"></a>
<a name="index-_003a_003a_002c-context-for-variables_002ffunctions"></a>
<div class="smallexample">
<pre class="smallexample"><var>file</var>::<var>variable</var>
<var>function</var>::<var>variable</var>
</pre></div>

<p>Here <var>file</var> or <var>function</var> is the name of the context for the
static <var>variable</var>.  In the case of file names, you can use quotes to
make sure <small>GDB</small> parses the file name as a single word&mdash;for example,
to print a global value of <code>x</code> defined in <samp>f2.c</samp>:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) p 'f2.c'::x
</pre></div>

<p>The <code>::</code> notation is normally used for referring to
static variables, since you typically disambiguate uses of local variables
in functions by selecting the appropriate frame and using the
simple name of the variable.  However, you may also use this notation
to refer to local variables in frames enclosing the selected frame:
</p>
<div class="smallexample">
<pre class="smallexample">void
foo (int a)
{
  if (a &lt; 10)
    bar (a);
  else
    process (a);    /* Stop here */
}

int
bar (int a)
{
  foo (a + 5);
}
</pre></div>

<p>For example, if there is a breakpoint at the commented line,
here is what you might see
when the program stops after executing the call <code>bar(0)</code>:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) p a
$1 = 10
(gdb) p bar::a
$2 = 5
(gdb) up 2
#2  0x080483d0 in foo (a=5) at foobar.c:12
(gdb) p a
$3 = 5
(gdb) p bar::a
$4 = 0
</pre></div>

<a name="index-C_002b_002b-scope-resolution"></a>
<p>These uses of &lsquo;<samp>::</samp>&rsquo; are very rarely in conflict with the very
similar use of the same notation in C<tt>++</tt>.  When they are in
conflict, the C<tt>++</tt> meaning takes precedence; however, this can be
overridden by quoting the file or function name with single quotes.
</p>
<p>For example, suppose the program is stopped in a method of a class
that has a field named <code>includefile</code>, and there is also an
include file named <samp>includefile</samp> that defines a variable,
<code>some_global</code>.
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) p includefile
$1 = 23
(gdb) p includefile::some_global
A syntax error in expression, near `'.
(gdb) p 'includefile'::some_global
$2 = 27
</pre></div>

<a name="index-wrong-values"></a>
<a name="index-variable-values_002c-wrong"></a>
<a name="index-function-entry_002fexit_002c-wrong-values-of-variables"></a>
<a name="index-optimized-code_002c-wrong-values-of-variables"></a>
<blockquote>
<p><em>Warning:</em> Occasionally, a local variable may appear to have the
wrong value at certain points in a function&mdash;just after entry to a new
scope, and just before exit.
</p></blockquote>
<p>You may see this problem when you are stepping by machine instructions.
This is because, on most machines, it takes more than one instruction to
set up a stack frame (including local variable definitions); if you are
stepping by machine instructions, variables may appear to have the wrong
values until the stack frame is completely built.  On exit, it usually
also takes more than one machine instruction to destroy a stack frame;
after you begin stepping through that group of instructions, local
variable definitions may be gone.
</p>
<p>This may also happen when the compiler does significant optimizations.
To be sure of always seeing accurate values, turn off all optimization
when compiling.
</p>
<a name="index-_0060_0060No-symbol-_0022foo_0022-in-current-context_0027_0027"></a>
<p>Another possible effect of compiler optimizations is to optimize
unused variables out of existence, or assign variables to registers (as
opposed to memory addresses).  Depending on the support for such cases
offered by the debug info format used by the compiler, <small>GDB</small>
might not be able to display values for such local variables.  If that
happens, <small>GDB</small> will print a message like this:
</p>
<div class="smallexample">
<pre class="smallexample">No symbol &quot;foo&quot; in current context.
</pre></div>

<p>To solve such problems, either recompile without optimizations, or use a
different debug info format, if the compiler supports several such
formats.  See <a href="Compilation.html#Compilation">Compilation</a>, for more information on choosing compiler
options.  See <a href="C.html#C">C and C<tt>++</tt></a>, for more information about debug
info formats that are best suited to C<tt>++</tt> programs.
</p>
<p>If you ask to print an object whose contents are unknown to
<small>GDB</small>, e.g., because its data type is not completely specified
by the debug information, <small>GDB</small> will say &lsquo;<samp>&lt;incomplete
type&gt;</samp>&rsquo;.  See <a href="Symbols.html#Symbols">incomplete type</a>, for more about this.
</p>
<a name="index-no-debug-info-variables"></a>
<p>If you try to examine or use the value of a (global) variable for
which <small>GDB</small> has no type information, e.g., because the program
includes no debug information, <small>GDB</small> displays an error message.
See <a href="Symbols.html#Symbols">unknown type</a>, for more about unknown types.  If you
cast the variable to its declared type, <small>GDB</small> gets the
variable&rsquo;s value using the cast-to type as the variable&rsquo;s type.  For
example, in a C program:
</p>
<div class="smallexample">
<pre class="smallexample">  (gdb) p var
  'var' has unknown type; cast it to its declared type
  (gdb) p (float) var
  $1 = 3.14
</pre></div>

<p>If you append <kbd>@entry</kbd> string to a function parameter name you get its
value at the time the function got called.  If the value is not available an
error message is printed.  Entry values are available only with some compilers.
Entry values are normally also printed at the function parameter list according
to <a href="Print-Settings.html#set-print-entry_002dvalues">set print entry-values</a>.
</p>
<div class="smallexample">
<pre class="smallexample">Breakpoint 1, d (i=30) at gdb.base/entry-value.c:29
29	  i++;
(gdb) next
30	  e (i);
(gdb) print i
$1 = 31
(gdb) print i@entry
$2 = 30
</pre></div>

<p>Strings are identified as arrays of <code>char</code> values without specified
signedness.  Arrays of either <code>signed char</code> or <code>unsigned char</code> get
printed as arrays of 1 byte sized integers.  <code>-fsigned-char</code> or
<code>-funsigned-char</code> <small>GCC</small> options have no effect as <small>GDB</small>
defines literal string type <code>&quot;char&quot;</code> as <code>char</code> without a sign.
For program code
</p>
<div class="smallexample">
<pre class="smallexample">char var0[] = &quot;A&quot;;
signed char var1[] = &quot;A&quot;;
</pre></div>

<p>You get during debugging
</p><div class="smallexample">
<pre class="smallexample">(gdb) print var0
$1 = &quot;A&quot;
(gdb) print var1
$2 = {65 'A', 0 '\0'}
</pre></div>

<hr>
<div class="header">
<p>
Next: <a href="Arrays.html#Arrays" accesskey="n" rel="next">Arrays</a>, Previous: <a href="Ambiguous-Expressions.html#Ambiguous-Expressions" accesskey="p" rel="previous">Ambiguous Expressions</a>, Up: <a href="Data.html#Data" accesskey="u" rel="up">Data</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
