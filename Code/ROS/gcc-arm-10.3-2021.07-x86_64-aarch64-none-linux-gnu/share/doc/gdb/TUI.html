<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: TUI</title>

<meta name="description" content="Debugging with GDB: TUI">
<meta name="keywords" content="Debugging with GDB: TUI">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="TUI-Overview.html#TUI-Overview" rel="next" title="TUI Overview">
<link href="Interpreters.html#Interpreters" rel="previous" title="Interpreters">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="TUI"></a>
<div class="header">
<p>
Next: <a href="Emacs.html#Emacs" accesskey="n" rel="next">Emacs</a>, Previous: <a href="Interpreters.html#Interpreters" accesskey="p" rel="previous">Interpreters</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="GDB-Text-User-Interface"></a>
<h2 class="chapter">25 <small>GDB</small> Text User Interface</h2>
<a name="index-TUI"></a>
<a name="index-Text-User-Interface"></a>

<table class="menu" border="0" cellspacing="0">
<tr><td align="left" valign="top">&bull; <a href="TUI-Overview.html#TUI-Overview" accesskey="1">TUI Overview</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">TUI overview
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="TUI-Keys.html#TUI-Keys" accesskey="2">TUI Keys</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">TUI key bindings
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="TUI-Single-Key-Mode.html#TUI-Single-Key-Mode" accesskey="3">TUI Single Key Mode</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">TUI single key mode
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="TUI-Commands.html#TUI-Commands" accesskey="4">TUI Commands</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">TUI-specific commands
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="TUI-Configuration.html#TUI-Configuration" accesskey="5">TUI Configuration</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">TUI configuration variables
</td></tr>
</table>

<p>The <small>GDB</small> Text User Interface (TUI) is a terminal
interface which uses the <code>curses</code> library to show the source
file, the assembly output, the program registers and <small>GDB</small>
commands in separate text windows.  The TUI mode is supported only
on platforms where a suitable version of the <code>curses</code> library
is available.
</p>
<p>The TUI mode is enabled by default when you invoke <small>GDB</small> as
&lsquo;<samp>gdb -tui</samp>&rsquo;.
You can also switch in and out of TUI mode while <small>GDB</small> runs by
using various TUI commands and key bindings, such as <code>tui
enable</code> or <kbd>C-x C-a</kbd>.  See <a href="TUI-Commands.html#TUI-Commands">TUI Commands</a>, and
<a href="TUI-Keys.html#TUI-Keys">TUI Key Bindings</a>.
</p>



</body>
</html>
