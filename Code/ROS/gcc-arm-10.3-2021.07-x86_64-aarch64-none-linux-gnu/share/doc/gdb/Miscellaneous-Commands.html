<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Miscellaneous Commands</title>

<meta name="description" content="Debugging with GDB: Miscellaneous Commands">
<meta name="keywords" content="Debugging with GDB: Miscellaneous Commands">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Bindable-Readline-Commands.html#Bindable-Readline-Commands" rel="up" title="Bindable Readline Commands">
<link href="Readline-vi-Mode.html#Readline-vi-Mode" rel="next" title="Readline vi Mode">
<link href="Keyboard-Macros.html#Keyboard-Macros" rel="previous" title="Keyboard Macros">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Miscellaneous-Commands"></a>
<div class="header">
<p>
Previous: <a href="Keyboard-Macros.html#Keyboard-Macros" accesskey="p" rel="previous">Keyboard Macros</a>, Up: <a href="Bindable-Readline-Commands.html#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Some-Miscellaneous-Commands"></a>
<h4 class="subsection">32.4.8 Some Miscellaneous Commands</h4>
<dl compact="compact">
<dt><code>re-read-init-file (C-x C-r)</code>
<a name="index-re_002dread_002dinit_002dfile-_0028C_002dx-C_002dr_0029"></a>
</dt>
<dd><p>Read in the contents of the <var>inputrc</var> file, and incorporate
any bindings or variable assignments found there.
</p>
</dd>
<dt><code>abort (C-g)</code>
<a name="index-abort-_0028C_002dg_0029"></a>
</dt>
<dd><p>Abort the current editing command and
ring the terminal&rsquo;s bell (subject to the setting of
<code>bell-style</code>).
</p>
</dd>
<dt><code>do-lowercase-version (M-A, M-B, M-<var>x</var>, &hellip;)</code>
<a name="index-do_002dlowercase_002dversion-_0028M_002dA_002c-M_002dB_002c-M_002dx_002c-_2026_0029"></a>
</dt>
<dd><p>If the metafied character <var>x</var> is upper case, run the command
that is bound to the corresponding metafied lower case character.
The behavior is undefined if <var>x</var> is already lower case.
</p>
</dd>
<dt><code>prefix-meta (<span class="key">ESC</span>)</code>
<a name="index-prefix_002dmeta-_0028ESC_0029"></a>
</dt>
<dd><p>Metafy the next character typed.  This is for keyboards
without a meta key.  Typing &lsquo;<samp><span class="key">ESC</span> f</samp>&rsquo; is equivalent to typing
<kbd>M-f</kbd>.
</p>
</dd>
<dt><code>undo (C-_ or C-x C-u)</code>
<a name="index-undo-_0028C_002d_005f-or-C_002dx-C_002du_0029"></a>
</dt>
<dd><p>Incremental undo, separately remembered for each line.
</p>
</dd>
<dt><code>revert-line (M-r)</code>
<a name="index-revert_002dline-_0028M_002dr_0029"></a>
</dt>
<dd><p>Undo all changes made to this line.  This is like executing the <code>undo</code>
command enough times to get back to the beginning.
</p>
</dd>
<dt><code>tilde-expand (M-~)</code>
<a name="index-tilde_002dexpand-_0028M_002d_007e_0029"></a>
</dt>
<dd><p>Perform tilde expansion on the current word.
</p>
</dd>
<dt><code>set-mark (C-@)</code>
<a name="index-set_002dmark-_0028C_002d_0040_0029"></a>
</dt>
<dd><p>Set the mark to the point.  If a
numeric argument is supplied, the mark is set to that position.
</p>
</dd>
<dt><code>exchange-point-and-mark (C-x C-x)</code>
<a name="index-exchange_002dpoint_002dand_002dmark-_0028C_002dx-C_002dx_0029"></a>
</dt>
<dd><p>Swap the point with the mark.  The current cursor position is set to
the saved position, and the old cursor position is saved as the mark.
</p>
</dd>
<dt><code>character-search (C-])</code>
<a name="index-character_002dsearch-_0028C_002d_005d_0029"></a>
</dt>
<dd><p>A character is read and point is moved to the next occurrence of that
character.  A negative count searches for previous occurrences.
</p>
</dd>
<dt><code>character-search-backward (M-C-])</code>
<a name="index-character_002dsearch_002dbackward-_0028M_002dC_002d_005d_0029"></a>
</dt>
<dd><p>A character is read and point is moved to the previous occurrence
of that character.  A negative count searches for subsequent
occurrences.
</p>
</dd>
<dt><code>skip-csi-sequence ()</code>
<a name="index-skip_002dcsi_002dsequence-_0028_0029"></a>
</dt>
<dd><p>Read enough characters to consume a multi-key sequence such as those
defined for keys like Home and End.  Such sequences begin with a
Control Sequence Indicator (CSI), usually ESC-[.  If this sequence is
bound to &quot;\e[&quot;, keys producing such sequences will have no effect
unless explicitly bound to a readline command, instead of inserting
stray characters into the editing buffer.  This is unbound by default,
but usually bound to ESC-[.
</p>
</dd>
<dt><code>insert-comment (M-#)</code>
<a name="index-insert_002dcomment-_0028M_002d_0023_0029"></a>
</dt>
<dd><p>Without a numeric argument, the value of the <code>comment-begin</code>
variable is inserted at the beginning of the current line.
If a numeric argument is supplied, this command acts as a toggle:  if
the characters at the beginning of the line do not match the value
of <code>comment-begin</code>, the value is inserted, otherwise
the characters in <code>comment-begin</code> are deleted from the beginning of
the line.
In either case, the line is accepted as if a newline had been typed.
</p>
</dd>
<dt><code>dump-functions ()</code>
<a name="index-dump_002dfunctions-_0028_0029"></a>
</dt>
<dd><p>Print all of the functions and their key bindings to the
Readline output stream.  If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <var>inputrc</var> file.  This command is unbound by default.
</p>
</dd>
<dt><code>dump-variables ()</code>
<a name="index-dump_002dvariables-_0028_0029"></a>
</dt>
<dd><p>Print all of the settable variables and their values to the
Readline output stream.  If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <var>inputrc</var> file.  This command is unbound by default.
</p>
</dd>
<dt><code>dump-macros ()</code>
<a name="index-dump_002dmacros-_0028_0029"></a>
</dt>
<dd><p>Print all of the Readline key sequences bound to macros and the
strings they output.  If a numeric argument is supplied,
the output is formatted in such a way that it can be made part
of an <var>inputrc</var> file.  This command is unbound by default.
</p>

</dd>
<dt><code>emacs-editing-mode (C-e)</code>
<a name="index-emacs_002dediting_002dmode-_0028C_002de_0029"></a>
</dt>
<dd><p>When in <code>vi</code> command mode, this causes a switch to <code>emacs</code>
editing mode.
</p>
</dd>
<dt><code>vi-editing-mode (M-C-j)</code>
<a name="index-vi_002dediting_002dmode-_0028M_002dC_002dj_0029"></a>
</dt>
<dd><p>When in <code>emacs</code> editing mode, this causes a switch to <code>vi</code>
editing mode.
</p>

</dd>
</dl>

<hr>
<div class="header">
<p>
Previous: <a href="Keyboard-Macros.html#Keyboard-Macros" accesskey="p" rel="previous">Keyboard Macros</a>, Up: <a href="Bindable-Readline-Commands.html#Bindable-Readline-Commands" accesskey="u" rel="up">Bindable Readline Commands</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
