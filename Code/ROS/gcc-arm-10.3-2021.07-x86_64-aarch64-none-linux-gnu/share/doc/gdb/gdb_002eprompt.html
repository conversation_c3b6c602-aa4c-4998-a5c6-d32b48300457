<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: gdb.prompt</title>

<meta name="description" content="Debugging with GDB: gdb.prompt">
<meta name="keywords" content="Debugging with GDB: gdb.prompt">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Python-modules.html#Python-modules" rel="up" title="Python modules">
<link href="Guile.html#Guile" rel="next" title="Guile">
<link href="gdb_002etypes.html#gdb_002etypes" rel="previous" title="gdb.types">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="gdb_002eprompt"></a>
<div class="header">
<p>
Previous: <a href="gdb_002etypes.html#gdb_002etypes" accesskey="p" rel="previous">gdb.types</a>, Up: <a href="Python-modules.html#Python-modules" accesskey="u" rel="up">Python modules</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="gdb_002eprompt-1"></a>
<h4 class="subsubsection">23.2.4.3 gdb.prompt</h4>
<a name="index-gdb_002eprompt"></a>

<p>This module provides a method for prompt value-substitution.
</p>
<dl compact="compact">
<dt><code>substitute_prompt (<var>string</var>)</code></dt>
<dd><p>Return <var>string</var> with escape sequences substituted by values.  Some
escape sequences take arguments.  You can specify arguments inside
&ldquo;{}&rdquo; immediately following the escape sequence.
</p>
<p>The escape sequences you can pass to this function are:
</p>
<dl compact="compact">
<dt><code>\\</code></dt>
<dd><p>Substitute a backslash.
</p></dd>
<dt><code>\e</code></dt>
<dd><p>Substitute an ESC character.
</p></dd>
<dt><code>\f</code></dt>
<dd><p>Substitute the selected frame; an argument names a frame parameter.
</p></dd>
<dt><code>\n</code></dt>
<dd><p>Substitute a newline.
</p></dd>
<dt><code>\p</code></dt>
<dd><p>Substitute a parameter&rsquo;s value; the argument names the parameter.
</p></dd>
<dt><code>\r</code></dt>
<dd><p>Substitute a carriage return.
</p></dd>
<dt><code>\t</code></dt>
<dd><p>Substitute the selected thread; an argument names a thread parameter.
</p></dd>
<dt><code>\v</code></dt>
<dd><p>Substitute the version of GDB.
</p></dd>
<dt><code>\w</code></dt>
<dd><p>Substitute the current working directory.
</p></dd>
<dt><code>\[</code></dt>
<dd><p>Begin a sequence of non-printing characters.  These sequences are
typically used with the ESC character, and are not counted in the string
length.  Example: &ldquo;\[\e[0;34m\](gdb)\[\e[0m\]&rdquo; will return a
blue-colored &ldquo;(gdb)&rdquo; prompt where the length is five.
</p></dd>
<dt><code>\]</code></dt>
<dd><p>End a sequence of non-printing characters.
</p></dd>
</dl>

<p>For example:
</p>
<div class="smallexample">
<pre class="smallexample">substitute_prompt (&quot;frame: \f, args: \p{print frame-arguments}&quot;)
</pre></div>

<p>will return the string:
</p>
<div class="smallexample">
<pre class="smallexample">&quot;frame: main, args: scalars&quot;
</pre></div>
</dd>
</dl>





</body>
</html>
