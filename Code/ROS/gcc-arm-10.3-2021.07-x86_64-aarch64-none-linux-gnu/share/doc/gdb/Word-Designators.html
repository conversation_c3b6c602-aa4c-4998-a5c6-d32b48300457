<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Word Designators</title>

<meta name="description" content="Debugging with GDB: Word Designators">
<meta name="keywords" content="Debugging with GDB: Word Designators">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="History-Interaction.html#History-Interaction" rel="up" title="History Interaction">
<link href="Modifiers.html#Modifiers" rel="next" title="Modifiers">
<link href="Event-Designators.html#Event-Designators" rel="previous" title="Event Designators">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Word-Designators"></a>
<div class="header">
<p>
Next: <a href="Modifiers.html#Modifiers" accesskey="n" rel="next">Modifiers</a>, Previous: <a href="Event-Designators.html#Event-Designators" accesskey="p" rel="previous">Event Designators</a>, Up: <a href="History-Interaction.html#History-Interaction" accesskey="u" rel="up">History Interaction</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Word-Designators-1"></a>
<h4 class="subsection">33.1.2 Word Designators</h4>

<p>Word designators are used to select desired words from the event.
A &lsquo;<samp>:</samp>&rsquo; separates the event specification from the word designator.  It
may be omitted if the word designator begins with a &lsquo;<samp>^</samp>&rsquo;, &lsquo;<samp>$</samp>&rsquo;,
&lsquo;<samp>*</samp>&rsquo;, &lsquo;<samp>-</samp>&rsquo;, or &lsquo;<samp>%</samp>&rsquo;.  Words are numbered from the beginning
of the line, with the first word being denoted by 0 (zero).  Words are
inserted into the current line separated by single spaces.
</p>
<p>For example,
</p>
<dl compact="compact">
<dt><code>!!</code></dt>
<dd><p>designates the preceding command.  When you type this, the preceding
command is repeated in toto.
</p>
</dd>
<dt><code>!!:$</code></dt>
<dd><p>designates the last argument of the preceding command.  This may be
shortened to <code>!$</code>.
</p>
</dd>
<dt><code>!fi:2</code></dt>
<dd><p>designates the second argument of the most recent command starting with
the letters <code>fi</code>.
</p></dd>
</dl>

<p>Here are the word designators:
</p> 
<dl compact="compact">
<dt><code>0 (zero)</code></dt>
<dd><p>The <code>0</code>th word.  For many applications, this is the command word.
</p>
</dd>
<dt><code><var>n</var></code></dt>
<dd><p>The <var>n</var>th word.
</p>
</dd>
<dt><code>^</code></dt>
<dd><p>The first argument; that is, word 1.
</p>
</dd>
<dt><code>$</code></dt>
<dd><p>The last argument.
</p>
</dd>
<dt><code>%</code></dt>
<dd><p>The word matched by the most recent &lsquo;<samp>?<var>string</var>?</samp>&rsquo; search.
</p>
</dd>
<dt><code><var>x</var>-<var>y</var></code></dt>
<dd><p>A range of words; &lsquo;<samp>-<var>y</var></samp>&rsquo; abbreviates &lsquo;<samp>0-<var>y</var></samp>&rsquo;.
</p>
</dd>
<dt><code>*</code></dt>
<dd><p>All of the words, except the <code>0</code>th.  This is a synonym for &lsquo;<samp>1-$</samp>&rsquo;.
It is not an error to use &lsquo;<samp>*</samp>&rsquo; if there is just one word in the event;
the empty string is returned in that case.
</p>
</dd>
<dt><code><var>x</var>*</code></dt>
<dd><p>Abbreviates &lsquo;<samp><var>x</var>-$</samp>&rsquo;
</p>
</dd>
<dt><code><var>x</var>-</code></dt>
<dd><p>Abbreviates &lsquo;<samp><var>x</var>-$</samp>&rsquo; like &lsquo;<samp><var>x</var>*</samp>&rsquo;, but omits the last word.
</p>
</dd>
</dl>

<p>If a word designator is supplied without an event specification, the
previous command is used as the event.
</p>
<hr>
<div class="header">
<p>
Next: <a href="Modifiers.html#Modifiers" accesskey="n" rel="next">Modifiers</a>, Previous: <a href="Event-Designators.html#Event-Designators" accesskey="p" rel="previous">Event Designators</a>, Up: <a href="History-Interaction.html#History-Interaction" accesskey="u" rel="up">History Interaction</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
