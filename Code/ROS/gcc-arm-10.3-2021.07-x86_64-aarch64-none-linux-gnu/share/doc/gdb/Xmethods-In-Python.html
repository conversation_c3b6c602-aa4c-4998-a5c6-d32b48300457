<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Xmethods In Python</title>

<meta name="description" content="Debugging with GDB: Xmethods In Python">
<meta name="keywords" content="Debugging with GDB: Xmethods In Python">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Python-API.html#Python-API" rel="up" title="Python API">
<link href="Xmethod-API.html#Xmethod-API" rel="next" title="Xmethod API">
<link href="Unwinding-Frames-in-Python.html#Unwinding-Frames-in-Python" rel="previous" title="Unwinding Frames in Python">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Xmethods-In-Python"></a>
<div class="header">
<p>
Next: <a href="Xmethod-API.html#Xmethod-API" accesskey="n" rel="next">Xmethod API</a>, Previous: <a href="Unwinding-Frames-in-Python.html#Unwinding-Frames-in-Python" accesskey="p" rel="previous">Unwinding Frames in Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Xmethods-In-Python-1"></a>
<h4 class="subsubsection">23.2.2.13 Xmethods In Python</h4>
<a name="index-xmethods-in-Python"></a>

<p><em>Xmethods</em> are additional methods or replacements for existing
methods of a C<tt>++</tt> class.  This feature is useful for those cases
where a method defined in C<tt>++</tt> source code could be inlined or
optimized out by the compiler, making it unavailable to <small>GDB</small>.
For such cases, one can define an xmethod to serve as a replacement
for the method defined in the C<tt>++</tt> source code.  <small>GDB</small> will
then invoke the xmethod, instead of the C<tt>++</tt> method, to
evaluate expressions.  One can also use xmethods when debugging
with core files.  Moreover, when debugging live programs, invoking an
xmethod need not involve running the inferior (which can potentially
perturb its state).  Hence, even if the C<tt>++</tt> method is available, it
is better to use its replacement xmethod if one is defined.
</p>
<p>The xmethods feature in Python is available via the concepts of an
<em>xmethod matcher</em> and an <em>xmethod worker</em>.  To
implement an xmethod, one has to implement a matcher and a
corresponding worker for it (more than one worker can be
implemented, each catering to a different overloaded instance of the
method).  Internally, <small>GDB</small> invokes the <code>match</code> method of a
matcher to match the class type and method name.  On a match, the
<code>match</code> method returns a list of matching <em>worker</em> objects.
Each worker object typically corresponds to an overloaded instance of
the xmethod.  They implement a <code>get_arg_types</code> method which
returns a sequence of types corresponding to the arguments the xmethod
requires.  <small>GDB</small> uses this sequence of types to perform
overload resolution and picks a winning xmethod worker.  A winner
is also selected from among the methods <small>GDB</small> finds in the
C<tt>++</tt> source code.  Next, the winning xmethod worker and the
winning C<tt>++</tt> method are compared to select an overall winner.  In
case of a tie between a xmethod worker and a C<tt>++</tt> method, the
xmethod worker is selected as the winner.  That is, if a winning
xmethod worker is found to be equivalent to the winning C<tt>++</tt>
method, then the xmethod worker is treated as a replacement for
the C<tt>++</tt> method.  <small>GDB</small> uses the overall winner to invoke the
method.  If the winning xmethod worker is the overall winner, then
the corresponding xmethod is invoked via the <code>__call__</code> method
of the worker object.
</p>
<p>If one wants to implement an xmethod as a replacement for an
existing C<tt>++</tt> method, then they have to implement an equivalent
xmethod which has exactly the same name and takes arguments of
exactly the same type as the C<tt>++</tt> method.  If the user wants to
invoke the C<tt>++</tt> method even though a replacement xmethod is
available for that method, then they can disable the xmethod.
</p>
<p>See <a href="Xmethod-API.html#Xmethod-API">Xmethod API</a>, for API to implement xmethods in Python.
See <a href="Writing-an-Xmethod.html#Writing-an-Xmethod">Writing an Xmethod</a>, for implementing xmethods in Python.
</p>
<hr>
<div class="header">
<p>
Next: <a href="Xmethod-API.html#Xmethod-API" accesskey="n" rel="next">Xmethod API</a>, Previous: <a href="Unwinding-Frames-in-Python.html#Unwinding-Frames-in-Python" accesskey="p" rel="previous">Unwinding Frames in Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
