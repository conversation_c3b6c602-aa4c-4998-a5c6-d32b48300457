<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: open</title>

<meta name="description" content="Debugging with GDB: open">
<meta name="keywords" content="Debugging with GDB: open">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="List-of-Supported-Calls.html#List-of-Supported-Calls" rel="up" title="List of Supported Calls">
<link href="close.html#close" rel="next" title="close">
<link href="List-of-Supported-Calls.html#List-of-Supported-Calls" rel="previous" title="List of Supported Calls">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="open"></a>
<div class="header">
<p>
Next: <a href="close.html#close" accesskey="n" rel="next">close</a>, Up: <a href="List-of-Supported-Calls.html#List-of-Supported-Calls" accesskey="u" rel="up">List of Supported Calls</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="open-1"></a>
<h4 class="unnumberedsubsubsec">open</h4>
<a name="index-open_002c-file_002di_002fo-system-call"></a>

<dl compact="compact">
<dt>Synopsis:</dt>
<dd><div class="smallexample">
<pre class="smallexample">int open(const char *pathname, int flags);
int open(const char *pathname, int flags, mode_t mode);
</pre></div>

</dd>
<dt>Request:</dt>
<dd><p>&lsquo;<samp>Fopen,<var>pathptr</var>/<var>len</var>,<var>flags</var>,<var>mode</var></samp>&rsquo;
</p>
<p><var>flags</var> is the bitwise <code>OR</code> of the following values:
</p>
<dl compact="compact">
<dt><code>O_CREAT</code></dt>
<dd><p>If the file does not exist it will be created.  The host
rules apply as far as file ownership and time stamps
are concerned.
</p>
</dd>
<dt><code>O_EXCL</code></dt>
<dd><p>When used with <code>O_CREAT</code>, if the file already exists it is
an error and open() fails.
</p>
</dd>
<dt><code>O_TRUNC</code></dt>
<dd><p>If the file already exists and the open mode allows
writing (<code>O_RDWR</code> or <code>O_WRONLY</code> is given) it will be
truncated to zero length.
</p>
</dd>
<dt><code>O_APPEND</code></dt>
<dd><p>The file is opened in append mode.
</p>
</dd>
<dt><code>O_RDONLY</code></dt>
<dd><p>The file is opened for reading only.
</p>
</dd>
<dt><code>O_WRONLY</code></dt>
<dd><p>The file is opened for writing only.
</p>
</dd>
<dt><code>O_RDWR</code></dt>
<dd><p>The file is opened for reading and writing.
</p></dd>
</dl>

<p>Other bits are silently ignored.
</p>

<p><var>mode</var> is the bitwise <code>OR</code> of the following values:
</p>
<dl compact="compact">
<dt><code>S_IRUSR</code></dt>
<dd><p>User has read permission.
</p>
</dd>
<dt><code>S_IWUSR</code></dt>
<dd><p>User has write permission.
</p>
</dd>
<dt><code>S_IRGRP</code></dt>
<dd><p>Group has read permission.
</p>
</dd>
<dt><code>S_IWGRP</code></dt>
<dd><p>Group has write permission.
</p>
</dd>
<dt><code>S_IROTH</code></dt>
<dd><p>Others have read permission.
</p>
</dd>
<dt><code>S_IWOTH</code></dt>
<dd><p>Others have write permission.
</p></dd>
</dl>

<p>Other bits are silently ignored.
</p>

</dd>
<dt>Return value:</dt>
<dd><p><code>open</code> returns the new file descriptor or -1 if an error
occurred.
</p>
</dd>
<dt>Errors:</dt>
<dd>
<dl compact="compact">
<dt><code>EEXIST</code></dt>
<dd><p><var>pathname</var> already exists and <code>O_CREAT</code> and <code>O_EXCL</code> were used.
</p>
</dd>
<dt><code>EISDIR</code></dt>
<dd><p><var>pathname</var> refers to a directory.
</p>
</dd>
<dt><code>EACCES</code></dt>
<dd><p>The requested access is not allowed.
</p>
</dd>
<dt><code>ENAMETOOLONG</code></dt>
<dd><p><var>pathname</var> was too long.
</p>
</dd>
<dt><code>ENOENT</code></dt>
<dd><p>A directory component in <var>pathname</var> does not exist.
</p>
</dd>
<dt><code>ENODEV</code></dt>
<dd><p><var>pathname</var> refers to a device, pipe, named pipe or socket.
</p>
</dd>
<dt><code>EROFS</code></dt>
<dd><p><var>pathname</var> refers to a file on a read-only filesystem and
write access was requested.
</p>
</dd>
<dt><code>EFAULT</code></dt>
<dd><p><var>pathname</var> is an invalid pointer value.
</p>
</dd>
<dt><code>ENOSPC</code></dt>
<dd><p>No space on device to create the file.
</p>
</dd>
<dt><code>EMFILE</code></dt>
<dd><p>The process already has the maximum number of files open.
</p>
</dd>
<dt><code>ENFILE</code></dt>
<dd><p>The limit on the total number of files open on the system
has been reached.
</p>
</dd>
<dt><code>EINTR</code></dt>
<dd><p>The call was interrupted by the user.
</p></dd>
</dl>

</dd>
</dl>

<hr>
<div class="header">
<p>
Next: <a href="close.html#close" accesskey="n" rel="next">close</a>, Up: <a href="List-of-Supported-Calls.html#List-of-Supported-Calls" accesskey="u" rel="up">List of Supported Calls</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
