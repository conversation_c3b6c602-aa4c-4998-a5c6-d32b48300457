<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: The F Request Packet</title>

<meta name="description" content="Debugging with GDB: The F Request Packet">
<meta name="keywords" content="Debugging with GDB: The F Request Packet">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="File_002dI_002fO-Remote-Protocol-Extension.html#File_002dI_002fO-Remote-Protocol-Extension" rel="up" title="File-I/O Remote Protocol Extension">
<link href="The-F-Reply-Packet.html#The-F-Reply-Packet" rel="next" title="The F Reply Packet">
<link href="Protocol-Basics.html#Protocol-Basics" rel="previous" title="Protocol Basics">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="The-F-Request-Packet"></a>
<div class="header">
<p>
Next: <a href="The-F-Reply-Packet.html#The-F-Reply-Packet" accesskey="n" rel="next">The F Reply Packet</a>, Previous: <a href="Protocol-Basics.html#Protocol-Basics" accesskey="p" rel="previous">Protocol Basics</a>, Up: <a href="File_002dI_002fO-Remote-Protocol-Extension.html#File_002dI_002fO-Remote-Protocol-Extension" accesskey="u" rel="up">File-I/O Remote Protocol Extension</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="The-F-Request-Packet-1"></a>
<h4 class="subsection">E.13.3 The <code>F</code> Request Packet</h4>
<a name="index-file_002di_002fo-request-packet"></a>
<a name="index-F-request-packet"></a>

<p>The <code>F</code> request packet has the following format:
</p>
<dl compact="compact">
<dt>&lsquo;<samp>F<var>call-id</var>,<var>parameter&hellip;</var></samp>&rsquo;</dt>
<dd>
<p><var>call-id</var> is the identifier to indicate the host system call to be called.
This is just the name of the function.
</p>
<p><var>parameter&hellip;</var> are the parameters to the system call.  
Parameters are hexadecimal integer values, either the actual values in case
of scalar datatypes, pointers to target buffer space in case of compound
datatypes and unspecified memory areas, or pointer/length pairs in case
of string parameters.  These are appended to the <var>call-id</var> as a 
comma-delimited list.  All values are transmitted in ASCII
string representation, pointer/length pairs separated by a slash.
</p>
</dd>
</dl>






</body>
</html>
