<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Nios II Features</title>

<meta name="description" content="Debugging with GDB: Nios II Features">
<meta name="keywords" content="Debugging with GDB: Nios II Features">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Standard-Target-Features.html#Standard-Target-Features" rel="up" title="Standard Target Features">
<link href="OpenRISC-1000-Features.html#OpenRISC-1000-Features" rel="next" title="OpenRISC 1000 Features">
<link href="NDS32-Features.html#NDS32-Features" rel="previous" title="NDS32 Features">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Nios-II-Features"></a>
<div class="header">
<p>
Next: <a href="OpenRISC-1000-Features.html#OpenRISC-1000-Features" accesskey="n" rel="next">OpenRISC 1000 Features</a>, Previous: <a href="NDS32-Features.html#NDS32-Features" accesskey="p" rel="previous">NDS32 Features</a>, Up: <a href="Standard-Target-Features.html#Standard-Target-Features" accesskey="u" rel="up">Standard Target Features</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Nios-II-Features-1"></a>
<h4 class="subsection">G.5.9 Nios II Features</h4>
<a name="index-target-descriptions_002c-Nios-II-features"></a>

<p>The &lsquo;<samp>org.gnu.gdb.nios2.cpu</samp>&rsquo; feature is required for Nios II
targets.  It should contain the 32 core registers (&lsquo;<samp>zero</samp>&rsquo;,
&lsquo;<samp>at</samp>&rsquo;, &lsquo;<samp>r2</samp>&rsquo; through &lsquo;<samp>r23</samp>&rsquo;, &lsquo;<samp>et</samp>&rsquo; through &lsquo;<samp>ra</samp>&rsquo;),
&lsquo;<samp>pc</samp>&rsquo;, and the 16 control registers (&lsquo;<samp>status</samp>&rsquo; through
&lsquo;<samp>mpuacc</samp>&rsquo;).
</p>



</body>
</html>
