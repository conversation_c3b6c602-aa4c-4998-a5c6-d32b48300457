<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: gdb-add-index man</title>

<meta name="description" content="Debugging with GDB: gdb-add-index man">
<meta name="keywords" content="Debugging with GDB: gdb-add-index man">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Man-Pages.html#Man-Pages" rel="up" title="Man Pages">
<link href="Copying.html#Copying" rel="next" title="Copying">
<link href="gdbinit-man.html#gdbinit-man" rel="previous" title="gdbinit man">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="gdb_002dadd_002dindex-man"></a>
<div class="header">
<p>
Previous: <a href="gdbinit-man.html#gdbinit-man" accesskey="p" rel="previous">gdbinit man</a>, Up: <a href="Man-Pages.html#Man-Pages" accesskey="u" rel="up">Man Pages</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<h4 class="node-heading">gdb-add-index man</h4>
<a name="gdb_002dadd_002dindex-1"></a>
<h3 class="heading">gdb-add-index</h3>
<a name="index-gdb_002dadd_002dindex"></a>
<a name="gdb_002dadd_002dindex"></a>

<p>gdb-add-index <var>filename</var>
</p>
<p>When <small>GDB</small> finds a symbol file, it scans the symbols in the
file in order to construct an internal symbol table.  This lets most
<small>GDB</small> operations work quickly&ndash;at the cost of a delay early on.
For large programs, this delay can be quite lengthy, so <small>GDB</small>
provides a way to build an index, which speeds up startup.
</p>
<p>To determine whether a file contains such an index, use the command
<kbd>readelf -S filename</kbd>: the index is stored in a section named
<code>.gdb_index</code>.  The index file can only be produced on systems
which use ELF binaries and DWARF debug information (i.e., sections
named <code>.debug_*</code>).
</p>
<p><code>gdb-add-index</code> uses <small>GDB</small> and <code>objdump</code> found
in the <code>PATH</code> environment variable.  If you want to use different
versions of these programs, you can specify them through the
<code>GDB</code> and <code>OBJDUMP</code> environment variables.
</p>
<p>See more in
<a href="Index-Files.html#Index-Files">Index Files</a>.
</p>




</body>
</html>
