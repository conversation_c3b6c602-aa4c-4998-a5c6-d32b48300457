<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: MicroBlaze</title>

<meta name="description" content="Debugging with GDB: MicroBlaze">
<meta name="keywords" content="Debugging with GDB: MicroBlaze">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Embedded-Processors.html#Embedded-Processors" rel="up" title="Embedded Processors">
<link href="MIPS-Embedded.html#MIPS-Embedded" rel="next" title="MIPS Embedded">
<link href="M68K.html#M68K" rel="previous" title="M68K">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="MicroBlaze"></a>
<div class="header">
<p>
Next: <a href="MIPS-Embedded.html#MIPS-Embedded" accesskey="n" rel="next">MIPS Embedded</a>, Previous: <a href="M68K.html#M68K" accesskey="p" rel="previous">M68K</a>, Up: <a href="Embedded-Processors.html#Embedded-Processors" accesskey="u" rel="up">Embedded Processors</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="MicroBlaze-1"></a>
<h4 class="subsection">21.3.5 MicroBlaze</h4>
<a name="index-Xilinx-MicroBlaze"></a>
<a name="index-XMD_002c-Xilinx-Microprocessor-Debugger"></a>

<p>The MicroBlaze is a soft-core processor supported on various Xilinx
FPGAs, such as Spartan or Virtex series.  Boards with these processors
usually have JTAG ports which connect to a host system running the Xilinx
Embedded Development Kit (EDK) or Software Development Kit (SDK).
This host system is used to download the configuration bitstream to
the target FPGA.  The Xilinx Microprocessor Debugger (XMD) program
communicates with the target board using the JTAG interface and
presents a <code>gdbserver</code> interface to the board.  By default
<code>xmd</code> uses port <code>1234</code>.  (While it is possible to change 
this default port, it requires the use of undocumented <code>xmd</code> 
commands.  Contact Xilinx support if you need to do this.)
</p>
<p>Use these GDB commands to connect to the MicroBlaze target processor.
</p>
<dl compact="compact">
<dt><code>target remote :1234</code></dt>
<dd><p>Use this command to connect to the target if you are running <small>GDB</small>
on the same system as <code>xmd</code>.
</p>
</dd>
<dt><code>target remote <var>xmd-host</var>:1234</code></dt>
<dd><p>Use this command to connect to the target if it is connected to <code>xmd</code>
running on a different system named <var>xmd-host</var>.
</p>
</dd>
<dt><code>load</code></dt>
<dd><p>Use this command to download a program to the MicroBlaze target.
</p>
</dd>
<dt><code>set debug microblaze <var>n</var></code></dt>
<dd><p>Enable MicroBlaze-specific debugging messages if non-zero.
</p>
</dd>
<dt><code>show debug microblaze <var>n</var></code></dt>
<dd><p>Show MicroBlaze-specific debugging level.
</p></dd>
</dl>




</body>
</html>
