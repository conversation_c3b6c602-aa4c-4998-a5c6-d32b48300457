<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: system</title>

<meta name="description" content="Debugging with GDB: system">
<meta name="keywords" content="Debugging with GDB: system">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="List-of-Supported-Calls.html#List-of-Supported-Calls" rel="up" title="List of Supported Calls">
<link href="Protocol_002dspecific-Representation-of-Datatypes.html#Protocol_002dspecific-Representation-of-Datatypes" rel="next" title="Protocol-specific Representation of Datatypes">
<link href="isatty.html#isatty" rel="previous" title="isatty">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="system"></a>
<div class="header">
<p>
Previous: <a href="isatty.html#isatty" accesskey="p" rel="previous">isatty</a>, Up: <a href="List-of-Supported-Calls.html#List-of-Supported-Calls" accesskey="u" rel="up">List of Supported Calls</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="system-1"></a>
<h4 class="unnumberedsubsubsec">system</h4>
<a name="index-system_002c-file_002di_002fo-system-call"></a>

<dl compact="compact">
<dt>Synopsis:</dt>
<dd><div class="smallexample">
<pre class="smallexample">int system(const char *command);
</pre></div>

</dd>
<dt>Request:</dt>
<dd><p>&lsquo;<samp>Fsystem,<var>commandptr</var>/<var>len</var></samp>&rsquo;
</p>
</dd>
<dt>Return value:</dt>
<dd><p>If <var>len</var> is zero, the return value indicates whether a shell is
available.  A zero return value indicates a shell is not available.
For non-zero <var>len</var>, the value returned is -1 on error and the
return status of the command otherwise.  Only the exit status of the
command is returned, which is extracted from the host&rsquo;s <code>system</code>
return value by calling <code>WEXITSTATUS(retval)</code>.  In case
<samp>/bin/sh</samp> could not be executed, 127 is returned.
</p>
</dd>
<dt>Errors:</dt>
<dd>
<dl compact="compact">
<dt><code>EINTR</code></dt>
<dd><p>The call was interrupted by the user.
</p></dd>
</dl>

</dd>
</dl>

<p><small>GDB</small> takes over the full task of calling the necessary host calls 
to perform the <code>system</code> call.  The return value of <code>system</code> on 
the host is simplified before it&rsquo;s returned
to the target.  Any termination signal information from the child process 
is discarded, and the return value consists
entirely of the exit status of the called command.
</p>
<p>Due to security concerns, the <code>system</code> call is by default refused
by <small>GDB</small>.  The user has to allow this call explicitly with the
<code>set remote system-call-allowed 1</code> command.
</p>
<dl compact="compact">
<dt><code>set remote system-call-allowed</code></dt>
<dd><a name="index-set-remote-system_002dcall_002dallowed"></a>
<p>Control whether to allow the <code>system</code> calls in the File I/O
protocol for the remote target.  The default is zero (disabled).
</p>
</dd>
<dt><code>show remote system-call-allowed</code></dt>
<dd><a name="index-show-remote-system_002dcall_002dallowed"></a>
<p>Show whether the <code>system</code> calls are allowed in the File I/O
protocol.
</p></dd>
</dl>




</body>
</html>
