<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: isatty</title>

<meta name="description" content="Debugging with GDB: isatty">
<meta name="keywords" content="Debugging with GDB: isatty">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="List-of-Supported-Calls.html#List-of-Supported-Calls" rel="up" title="List of Supported Calls">
<link href="system.html#system" rel="next" title="system">
<link href="gettimeofday.html#gettimeofday" rel="previous" title="gettimeofday">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="isatty"></a>
<div class="header">
<p>
Next: <a href="system.html#system" accesskey="n" rel="next">system</a>, Previous: <a href="gettimeofday.html#gettimeofday" accesskey="p" rel="previous">gettimeofday</a>, Up: <a href="List-of-Supported-Calls.html#List-of-Supported-Calls" accesskey="u" rel="up">List of Supported Calls</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="isatty-1"></a>
<h4 class="unnumberedsubsubsec">isatty</h4>
<a name="index-isatty_002c-file_002di_002fo-system-call"></a>

<dl compact="compact">
<dt>Synopsis:</dt>
<dd><div class="smallexample">
<pre class="smallexample">int isatty(int fd);
</pre></div>

</dd>
<dt>Request:</dt>
<dd><p>&lsquo;<samp>Fisatty,<var>fd</var></samp>&rsquo;
</p>
</dd>
<dt>Return value:</dt>
<dd><p>Returns 1 if <var>fd</var> refers to the <small>GDB</small> console, 0 otherwise.
</p>
</dd>
<dt>Errors:</dt>
<dd>
<dl compact="compact">
<dt><code>EINTR</code></dt>
<dd><p>The call was interrupted by the user.
</p></dd>
</dl>

</dd>
</dl>

<p>Note that the <code>isatty</code> call is treated as a special case: it returns
1 to the target if the file descriptor is attached
to the <small>GDB</small> console, 0 otherwise.  Implementing through system calls
would require implementing <code>ioctl</code> and would be more complex than
needed.
</p>




</body>
</html>
