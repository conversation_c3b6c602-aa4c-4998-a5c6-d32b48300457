<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Which flavor to choose?</title>

<meta name="description" content="Debugging with GDB: Which flavor to choose?">
<meta name="keywords" content="Debugging with GDB: Which flavor to choose?">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Auto_002dloading-extensions.html#Auto_002dloading-extensions" rel="up" title="Auto-loading extensions">
<link href="Multiple-Extension-Languages.html#Multiple-Extension-Languages" rel="next" title="Multiple Extension Languages">
<link href="dotdebug_005fgdb_005fscripts-section.html#dotdebug_005fgdb_005fscripts-section" rel="previous" title="dotdebug_gdb_scripts section">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Which-flavor-to-choose_003f"></a>
<div class="header">
<p>
Previous: <a href="dotdebug_005fgdb_005fscripts-section.html#dotdebug_005fgdb_005fscripts-section" accesskey="p" rel="previous">dotdebug_gdb_scripts section</a>, Up: <a href="Auto_002dloading-extensions.html#Auto_002dloading-extensions" accesskey="u" rel="up">Auto-loading extensions</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Which-Flavor-to-Choose_003f"></a>
<h4 class="subsection">23.4.3 Which Flavor to Choose?</h4>

<p>Given the multiple ways of auto-loading extensions, it might not always
be clear which one to choose.  This section provides some guidance.
</p>
<p>Benefits of the <samp>-gdb.<var>ext</var></samp> way:
</p>
<ul>
<li> Can be used with file formats that don&rsquo;t support multiple sections.

</li><li> Ease of finding scripts for public libraries.

<p>Scripts specified in the <code>.debug_gdb_scripts</code> section are searched for
in the source search path.
For publicly installed libraries, e.g., <samp>libstdc++</samp>, there typically
isn&rsquo;t a source directory in which to find the script.
</p>
</li><li> Doesn&rsquo;t require source code additions.
</li></ul>

<p>Benefits of the <code>.debug_gdb_scripts</code> way:
</p>
<ul>
<li> Works with static linking.

<p>Scripts for libraries done the <samp>-gdb.<var>ext</var></samp> way require an objfile to
trigger their loading.  When an application is statically linked the only
objfile available is the executable, and it is cumbersome to attach all the
scripts from all the input libraries to the executable&rsquo;s
<samp>-gdb.<var>ext</var></samp> script.
</p>
</li><li> Works with classes that are entirely inlined.

<p>Some classes can be entirely inlined, and thus there may not be an associated
shared library to attach a <samp>-gdb.<var>ext</var></samp> script to.
</p>
</li><li> Scripts needn&rsquo;t be copied out of the source tree.

<p>In some circumstances, apps can be built out of large collections of internal
libraries, and the build infrastructure necessary to install the
<samp>-gdb.<var>ext</var></samp> scripts in a place where <small>GDB</small> can find them is
cumbersome.  It may be easier to specify the scripts in the
<code>.debug_gdb_scripts</code> section as relative paths, and add a path to the
top of the source tree to the source search path.
</p></li></ul>




</body>
</html>
