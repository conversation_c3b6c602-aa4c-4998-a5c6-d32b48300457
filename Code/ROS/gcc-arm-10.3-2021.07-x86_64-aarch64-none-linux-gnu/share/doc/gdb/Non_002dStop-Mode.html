<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Non-Stop Mode</title>

<meta name="description" content="Debugging with GDB: Non-Stop Mode">
<meta name="keywords" content="Debugging with GDB: Non-Stop Mode">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Thread-Stops.html#Thread-Stops" rel="up" title="Thread Stops">
<link href="Background-Execution.html#Background-Execution" rel="next" title="Background Execution">
<link href="All_002dStop-Mode.html#All_002dStop-Mode" rel="previous" title="All-Stop Mode">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Non_002dStop-Mode"></a>
<div class="header">
<p>
Next: <a href="Background-Execution.html#Background-Execution" accesskey="n" rel="next">Background Execution</a>, Previous: <a href="All_002dStop-Mode.html#All_002dStop-Mode" accesskey="p" rel="previous">All-Stop Mode</a>, Up: <a href="Thread-Stops.html#Thread-Stops" accesskey="u" rel="up">Thread Stops</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Non_002dStop-Mode-1"></a>
<h4 class="subsection">5.5.2 Non-Stop Mode</h4>

<a name="index-non_002dstop-mode"></a>


<p>For some multi-threaded targets, <small>GDB</small> supports an optional
mode of operation in which you can examine stopped program threads in
the debugger while other threads continue to execute freely.  This
minimizes intrusion when debugging live systems, such as programs
where some threads have real-time constraints or must continue to
respond to external events.  This is referred to as <em>non-stop</em> mode.
</p>
<p>In non-stop mode, when a thread stops to report a debugging event,
<em>only</em> that thread is stopped; <small>GDB</small> does not stop other
threads as well, in contrast to the all-stop mode behavior.  Additionally,
execution commands such as <code>continue</code> and <code>step</code> apply by default
only to the current thread in non-stop mode, rather than all threads as
in all-stop mode.  This allows you to control threads explicitly in
ways that are not possible in all-stop mode &mdash; for example, stepping
one thread while allowing others to run freely, stepping
one thread while holding all others stopped, or stepping several threads
independently and simultaneously.
</p>
<p>To enter non-stop mode, use this sequence of commands before you run
or attach to your program:
</p>
<div class="smallexample">
<pre class="smallexample"># If using the CLI, pagination breaks non-stop.
set pagination off

# Finally, turn it on!
set non-stop on
</pre></div>

<p>You can use these commands to manipulate the non-stop mode setting:
</p>
<dl compact="compact">
<dd><a name="index-set-non_002dstop"></a>
</dd>
<dt><code>set non-stop on</code></dt>
<dd><p>Enable selection of non-stop mode.
</p></dd>
<dt><code>set non-stop off</code></dt>
<dd><p>Disable selection of non-stop mode.
<a name="index-show-non_002dstop"></a>
</p></dd>
<dt><code>show non-stop</code></dt>
<dd><p>Show the current non-stop enablement setting.
</p></dd>
</dl>

<p>Note these commands only reflect whether non-stop mode is enabled,
not whether the currently-executing program is being run in non-stop mode.
In particular, the <code>set non-stop</code> preference is only consulted when
<small>GDB</small> starts or connects to the target program, and it is generally
not possible to switch modes once debugging has started.  Furthermore,
since not all targets support non-stop mode, even when you have enabled
non-stop mode, <small>GDB</small> may still fall back to all-stop operation by
default.
</p>
<p>In non-stop mode, all execution commands apply only to the current thread
by default.  That is, <code>continue</code> only continues one thread.
To continue all threads, issue <code>continue -a</code> or <code>c -a</code>.
</p>
<p>You can use <small>GDB</small>&rsquo;s background execution commands
(see <a href="Background-Execution.html#Background-Execution">Background Execution</a>) to run some threads in the background
while you continue to examine or step others from <small>GDB</small>.
The MI execution commands (see <a href="GDB_002fMI-Program-Execution.html#GDB_002fMI-Program-Execution">GDB/MI Program Execution</a>) are
always executed asynchronously in non-stop mode.
</p>
<p>Suspending execution is done with the <code>interrupt</code> command when
running in the background, or <kbd>Ctrl-c</kbd> during foreground execution.
In all-stop mode, this stops the whole process;
but in non-stop mode the interrupt applies only to the current thread.
To stop the whole program, use <code>interrupt -a</code>.
</p>
<p>Other execution commands do not currently support the <code>-a</code> option.
</p>
<p>In non-stop mode, when a thread stops, <small>GDB</small> doesn&rsquo;t automatically make
that thread current, as it does in all-stop mode.  This is because the
thread stop notifications are asynchronous with respect to <small>GDB</small>&rsquo;s
command interpreter, and it would be confusing if <small>GDB</small> unexpectedly
changed to a different thread just as you entered a command to operate on the
previously current thread.
</p>
<hr>
<div class="header">
<p>
Next: <a href="Background-Execution.html#Background-Execution" accesskey="n" rel="next">Background Execution</a>, Previous: <a href="All_002dStop-Mode.html#All_002dStop-Mode" accesskey="p" rel="previous">All-Stop Mode</a>, Up: <a href="Thread-Stops.html#Thread-Stops" accesskey="u" rel="up">Thread Stops</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
