<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: gcore man</title>

<meta name="description" content="Debugging with GDB: gcore man">
<meta name="keywords" content="Debugging with GDB: gcore man">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Man-Pages.html#Man-Pages" rel="up" title="Man Pages">
<link href="gdbinit-man.html#gdbinit-man" rel="next" title="gdbinit man">
<link href="gdbserver-man.html#gdbserver-man" rel="previous" title="gdbserver man">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="gcore-man"></a>
<div class="header">
<p>
Next: <a href="gdbinit-man.html#gdbinit-man" accesskey="n" rel="next">gdbinit man</a>, Previous: <a href="gdbserver-man.html#gdbserver-man" accesskey="p" rel="previous">gdbserver man</a>, Up: <a href="Man-Pages.html#Man-Pages" accesskey="u" rel="up">Man Pages</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<h4 class="node-heading">gcore man</h4>
<a name="gcore"></a>
<h3 class="heading">gcore</h3>


<div class="format">
<pre class="format">gcore [-a] [-o <var>prefix</var>] <var>pid1</var> [<var>pid2</var>...<var>pidN</var>]
</pre></div>

<p>Generate core dumps of one or more running programs with process IDs
<var>pid1</var>, <var>pid2</var>, etc.  A core file produced by <code>gcore</code>
is equivalent to one produced by the kernel when the process crashes
(and when <kbd>ulimit -c</kbd> was used to set up an appropriate core dump
limit).  However, unlike after a crash, after <code>gcore</code> finishes
its job the program remains running without any change.
</p>
<dl compact="compact">
<dt><code>-a</code></dt>
<dd><p>Dump all memory mappings.  The actual effect of this option depends on
the Operating System.  On <small>GNU</small>/Linux, it will disable
<code>use-coredump-filter</code> (see <a href="Core-File-Generation.html#set-use_002dcoredump_002dfilter">set use-coredump-filter</a>) and
enable <code>dump-excluded-mappings</code> (see <a href="Core-File-Generation.html#set-dump_002dexcluded_002dmappings">set dump-excluded-mappings</a>).
</p>
</dd>
<dt><code>-o <var>prefix</var></code></dt>
<dd><p>The optional argument <var>prefix</var> specifies the prefix to be used
when composing the file names of the core dumps.  The file name is
composed as <samp><var>prefix</var>.<var>pid</var></samp>, where <var>pid</var> is the
process ID of the running program being analyzed by <code>gcore</code>.
If not specified, <var>prefix</var> defaults to <var>gcore</var>.
</p></dd>
</dl>





</body>
</html>
