<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Value Sizes</title>

<meta name="description" content="Debugging with GDB: Value Sizes">
<meta name="keywords" content="Debugging with GDB: Value Sizes">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Data.html#Data" rel="up" title="Data">
<link href="Optimized-Code.html#Optimized-Code" rel="next" title="Optimized Code">
<link href="Searching-Memory.html#Searching-Memory" rel="previous" title="Searching Memory">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Value-Sizes"></a>
<div class="header">
<p>
Previous: <a href="Searching-Memory.html#Searching-Memory" accesskey="p" rel="previous">Searching Memory</a>, Up: <a href="Data.html#Data" accesskey="u" rel="up">Data</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Value-Sizes-1"></a>
<h3 class="section">10.23 Value Sizes</h3>

<p>Whenever <small>GDB</small> prints a value memory will be allocated within
<small>GDB</small> to hold the contents of the value.  It is possible in
some languages with dynamic typing systems, that an invalid program
may indicate a value that is incorrectly large, this in turn may cause
<small>GDB</small> to try and allocate an overly large amount of memory.
</p>
<dl compact="compact">
<dd><a name="index-set-max_002dvalue_002dsize"></a>
</dd>
<dt><code>set max-value-size <var>bytes</var></code></dt>
<dt><code>set max-value-size unlimited</code></dt>
<dd><p>Set the maximum size of memory that <small>GDB</small> will allocate for the
contents of a value to <var>bytes</var>, trying to display a value that
requires more memory than that will result in an error.
</p>
<p>Setting this variable does not effect values that have already been
allocated within <small>GDB</small>, only future allocations.
</p>
<p>There&rsquo;s a minimum size that <code>max-value-size</code> can be set to in
order that <small>GDB</small> can still operate correctly, this minimum is
currently 16 bytes.
</p>
<p>The limit applies to the results of some subexpressions as well as to
complete expressions.  For example, an expression denoting a simple
integer component, such as <code>x.y.z</code>, may fail if the size of
<var>x.y</var> is dynamic and exceeds <var>bytes</var>.  On the other hand,
<small>GDB</small> is sometimes clever; the expression <code>A[i]</code>, where
<var>A</var> is an array variable with non-constant size, will generally
succeed regardless of the bounds on <var>A</var>, as long as the component
size is less than <var>bytes</var>.
</p>
<p>The default value of <code>max-value-size</code> is currently 64k.
</p>
<a name="index-show-max_002dvalue_002dsize"></a>
</dd>
<dt><code>show max-value-size</code></dt>
<dd><p>Show the maximum size of memory, in bytes, that <small>GDB</small> will
allocate for the contents of a value.
</p></dd>
</dl>




</body>
</html>
