<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: mode_t Values</title>

<meta name="description" content="Debugging with GDB: mode_t Values">
<meta name="keywords" content="Debugging with GDB: mode_t Values">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Constants.html#Constants" rel="up" title="Constants">
<link href="errno-Values.html#errno-Values" rel="next" title="errno Values">
<link href="Open-Flags.html#Open-Flags" rel="previous" title="Open Flags">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="mode_005ft-Values"></a>
<div class="header">
<p>
Next: <a href="errno-Values.html#errno-Values" accesskey="n" rel="next">errno Values</a>, Previous: <a href="Open-Flags.html#Open-Flags" accesskey="p" rel="previous">Open Flags</a>, Up: <a href="Constants.html#Constants" accesskey="u" rel="up">Constants</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="mode_005ft-Values-1"></a>
<h4 class="unnumberedsubsubsec">mode_t Values</h4>
<a name="index-mode_005ft-values_002c-in-file_002di_002fo-protocol"></a>

<p>All values are given in octal representation.
</p>
<div class="smallexample">
<pre class="smallexample">  S_IFREG       0100000
  S_IFDIR        040000
  S_IRUSR          0400
  S_IWUSR          0200
  S_IXUSR          0100
  S_IRGRP           040
  S_IWGRP           020
  S_IXGRP           010
  S_IROTH            04
  S_IWOTH            02
  S_IXOTH            01
</pre></div>




</body>
</html>
