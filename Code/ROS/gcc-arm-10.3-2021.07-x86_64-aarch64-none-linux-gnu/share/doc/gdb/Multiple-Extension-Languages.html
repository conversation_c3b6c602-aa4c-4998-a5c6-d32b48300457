<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Multiple Extension Languages</title>

<meta name="description" content="Debugging with GDB: Multiple Extension Languages">
<meta name="keywords" content="Debugging with GDB: Multiple Extension Languages">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Extending-GDB.html#Extending-GDB" rel="up" title="Extending GDB">
<link href="Aliases.html#Aliases" rel="next" title="Aliases">
<link href="Which-flavor-to-choose_003f.html#Which-flavor-to-choose_003f" rel="previous" title="Which flavor to choose?">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Multiple-Extension-Languages"></a>
<div class="header">
<p>
Next: <a href="Aliases.html#Aliases" accesskey="n" rel="next">Aliases</a>, Previous: <a href="Auto_002dloading-extensions.html#Auto_002dloading-extensions" accesskey="p" rel="previous">Auto-loading extensions</a>, Up: <a href="Extending-GDB.html#Extending-GDB" accesskey="u" rel="up">Extending GDB</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Multiple-Extension-Languages-1"></a>
<h3 class="section">23.5 Multiple Extension Languages</h3>

<p>The Guile and Python extension languages do not share any state,
and generally do not interfere with each other.
There are some things to be aware of, however.
</p>
<a name="Python-Comes-First"></a>
<h4 class="subsection">23.5.1 Python Comes First</h4>

<p>Python was <small>GDB</small>&rsquo;s first extension language, and to avoid breaking
existing behaviour Python comes first.  This is generally solved by the
&ldquo;first one wins&rdquo; principle.  <small>GDB</small> maintains a list of enabled
extension languages, and when it makes a call to an extension language,
(say to pretty-print a value), it tries each in turn until an extension
language indicates it has performed the request (e.g., has returned the
pretty-printed form of a value).
This extends to errors while performing such requests: If an error happens
while, for example, trying to pretty-print an object then the error is
reported and any following extension languages are not tried.
</p>



</body>
</html>
