<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Writing JIT Debug Info Readers</title>

<meta name="description" content="Debugging with GDB: Writing JIT Debug Info Readers">
<meta name="keywords" content="Debugging with GDB: Writing JIT Debug Info Readers">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Custom-Debug-Info.html#Custom-Debug-Info" rel="up" title="Custom Debug Info">
<link href="In_002dprocess-Agent.html#In_002dprocess-Agent" rel="next" title="In-process Agent">
<link href="Using-JIT-Debug-Info-Readers.html#Using-JIT-Debug-Info-Readers" rel="previous" title="Using JIT Debug Info Readers">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Writing-JIT-Debug-Info-Readers"></a>
<div class="header">
<p>
Previous: <a href="Using-JIT-Debug-Info-Readers.html#Using-JIT-Debug-Info-Readers" accesskey="p" rel="previous">Using JIT Debug Info Readers</a>, Up: <a href="Custom-Debug-Info.html#Custom-Debug-Info" accesskey="u" rel="up">Custom Debug Info</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Writing-JIT-Debug-Info-Readers-1"></a>
<h4 class="subsection">29.4.2 Writing JIT Debug Info Readers</h4>
<a name="index-writing-JIT-debug-info-readers"></a>

<p>As mentioned, a reader is essentially a shared object conforming to a
certain ABI.  This ABI is described in <samp>jit-reader.h</samp>.
</p>
<p><samp>jit-reader.h</samp> defines the structures, macros and functions
required to write a reader.  It is installed (along with
<small>GDB</small>), in <samp><var>includedir</var>/gdb</samp> where <var>includedir</var> is
the system include directory.
</p>
<p>Readers need to be released under a GPL compatible license.  A reader
can be declared as released under such a license by placing the macro
<code>GDB_DECLARE_GPL_COMPATIBLE_READER</code> in a source file.
</p>
<p>The entry point for readers is the symbol <code>gdb_init_reader</code>,
which is expected to be a function with the prototype
</p>
<a name="index-gdb_005finit_005freader"></a>
<div class="smallexample">
<pre class="smallexample">extern struct gdb_reader_funcs *gdb_init_reader (void);
</pre></div>

<a name="index-struct-gdb_005freader_005ffuncs"></a>

<p><code>struct gdb_reader_funcs</code> contains a set of pointers to callback
functions.  These functions are executed to read the debug info
generated by the JIT compiler (<code>read</code>), to unwind stack frames
(<code>unwind</code>) and to create canonical frame IDs
(<code>get_frame_id</code>).  It also has a callback that is called when the
reader is being unloaded (<code>destroy</code>).  The struct looks like this
</p>
<div class="smallexample">
<pre class="smallexample">struct gdb_reader_funcs
{
  /* Must be set to GDB_READER_INTERFACE_VERSION.  */
  int reader_version;

  /* For use by the reader.  */
  void *priv_data;

  gdb_read_debug_info *read;
  gdb_unwind_frame *unwind;
  gdb_get_frame_id *get_frame_id;
  gdb_destroy_reader *destroy;
};
</pre></div>

<a name="index-struct-gdb_005fsymbol_005fcallbacks"></a>
<a name="index-struct-gdb_005funwind_005fcallbacks"></a>

<p>The callbacks are provided with another set of callbacks by
<small>GDB</small> to do their job.  For <code>read</code>, these callbacks are
passed in a <code>struct gdb_symbol_callbacks</code> and for <code>unwind</code>
and <code>get_frame_id</code>, in a <code>struct gdb_unwind_callbacks</code>.
<code>struct gdb_symbol_callbacks</code> has callbacks to create new object
files and new symbol tables inside those object files.  <code>struct
gdb_unwind_callbacks</code> has callbacks to read registers off the current
frame and to write out the values of the registers in the previous
frame.  Both have a callback (<code>target_read</code>) to read bytes off the
target&rsquo;s address space.
</p>
<hr>
<div class="header">
<p>
Previous: <a href="Using-JIT-Debug-Info-Readers.html#Using-JIT-Debug-Info-Readers" accesskey="p" rel="previous">Using JIT Debug Info Readers</a>, Up: <a href="Custom-Debug-Info.html#Custom-Debug-Info" accesskey="u" rel="up">Custom Debug Info</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
