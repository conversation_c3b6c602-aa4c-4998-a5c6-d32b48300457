<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Objfiles In Guile</title>

<meta name="description" content="Debugging with GDB: Objfiles In Guile">
<meta name="keywords" content="Debugging with GDB: Objfiles In Guile">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Guile-API.html#Guile-API" rel="up" title="Guile API">
<link href="Frames-In-Guile.html#Frames-In-Guile" rel="next" title="Frames In Guile">
<link href="Progspaces-In-Guile.html#Progspaces-In-Guile" rel="previous" title="Progspaces In Guile">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Objfiles-In-Guile"></a>
<div class="header">
<p>
Next: <a href="Frames-In-Guile.html#Frames-In-Guile" accesskey="n" rel="next">Frames In Guile</a>, Previous: <a href="Progspaces-In-Guile.html#Progspaces-In-Guile" accesskey="p" rel="previous">Progspaces In Guile</a>, Up: <a href="Guile-API.html#Guile-API" accesskey="u" rel="up">Guile API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Objfiles-In-Guile-1"></a>
<h4 class="subsubsection">23.3.3.14 Objfiles In Guile</h4>

<a name="index-objfiles-in-guile"></a>
<a name="index-_003cgdb_003aobjfile_003e"></a>
<p><small>GDB</small> loads symbols for an inferior from various
symbol-containing files (see <a href="Files.html#Files">Files</a>).  These include the primary
executable file, any shared libraries used by the inferior, and any
separate debug info files (see <a href="Separate-Debug-Files.html#Separate-Debug-Files">Separate Debug Files</a>).
<small>GDB</small> calls these symbol-containing files <em>objfiles</em>.
</p>
<p>Each objfile is represented as an object of type <code>&lt;gdb:objfile&gt;</code>.
</p>
<p>The following objfile-related procedures are provided by the
<code>(gdb)</code> module:
</p>
<dl>
<dt><a name="index-objfile_003f"></a>Scheme Procedure: <strong>objfile?</strong> <em>object</em></dt>
<dd><p>Return <code>#t</code> if <var>object</var> is a <code>&lt;gdb:objfile&gt;</code> object.
Otherwise return <code>#f</code>.
</p></dd></dl>

<dl>
<dt><a name="index-objfile_002dvalid_003f"></a>Scheme Procedure: <strong>objfile-valid?</strong> <em>objfile</em></dt>
<dd><p>Return <code>#t</code> if <var>objfile</var> is valid, <code>#f</code> if not.
A <code>&lt;gdb:objfile&gt;</code> object can become invalid
if the object file it refers to is not loaded in <small>GDB</small> any
longer.  All other <code>&lt;gdb:objfile&gt;</code> procedures will throw an exception
if it is invalid at the time the procedure is called.
</p></dd></dl>

<dl>
<dt><a name="index-objfile_002dfilename"></a>Scheme Procedure: <strong>objfile-filename</strong> <em>objfile</em></dt>
<dd><p>Return the file name of <var>objfile</var> as a string,
with symbolic links resolved.
</p></dd></dl>

<dl>
<dt><a name="index-objfile_002dprogspace"></a>Scheme Procedure: <strong>objfile-progspace</strong> <em>objfile</em></dt>
<dd><p>Return the <code>&lt;gdb:progspace&gt;</code> that this object file lives in.
See <a href="Progspaces-In-Guile.html#Progspaces-In-Guile">Progspaces In Guile</a>, for more on progspaces.
</p></dd></dl>

<dl>
<dt><a name="index-objfile_002dpretty_002dprinters"></a>Scheme Procedure: <strong>objfile-pretty-printers</strong> <em>objfile</em></dt>
<dd><p>Return the list of registered <code>&lt;gdb:pretty-printer&gt;</code> objects for
<var>objfile</var>.  See <a href="Guile-Pretty-Printing-API.html#Guile-Pretty-Printing-API">Guile Pretty Printing API</a>, for more information.
</p></dd></dl>

<dl>
<dt><a name="index-set_002dobjfile_002dpretty_002dprinters_0021"></a>Scheme Procedure: <strong>set-objfile-pretty-printers!</strong> <em>objfile printer-list</em></dt>
<dd><p>Set the list of registered <code>&lt;gdb:pretty-printer&gt;</code> objects for
<var>objfile</var> to <var>printer-list</var>.  The
<var>printer-list</var> must be a list of <code>&lt;gdb:pretty-printer&gt;</code> objects.
See <a href="Guile-Pretty-Printing-API.html#Guile-Pretty-Printing-API">Guile Pretty Printing API</a>, for more information.
</p></dd></dl>

<dl>
<dt><a name="index-current_002dobjfile"></a>Scheme Procedure: <strong>current-objfile</strong></dt>
<dd><p>When auto-loading a Guile script (see <a href="Guile-Auto_002dloading.html#Guile-Auto_002dloading">Guile Auto-loading</a>), <small>GDB</small>
sets the &ldquo;current objfile&rdquo; to the corresponding objfile.  This
function returns the current objfile.  If there is no current objfile,
this function returns <code>#f</code>.
</p></dd></dl>

<dl>
<dt><a name="index-objfiles"></a>Scheme Procedure: <strong>objfiles</strong></dt>
<dd><p>Return a list of all the objfiles in the current program space.
</p></dd></dl>

<hr>
<div class="header">
<p>
Next: <a href="Frames-In-Guile.html#Frames-In-Guile" accesskey="n" rel="next">Frames In Guile</a>, Previous: <a href="Progspaces-In-Guile.html#Progspaces-In-Guile" accesskey="p" rel="previous">Progspaces In Guile</a>, Up: <a href="Guile-API.html#Guile-API" accesskey="u" rel="up">Guile API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
