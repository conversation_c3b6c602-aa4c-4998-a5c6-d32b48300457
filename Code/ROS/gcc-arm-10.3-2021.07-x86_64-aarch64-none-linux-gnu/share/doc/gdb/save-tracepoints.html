<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: save tracepoints</title>

<meta name="description" content="Debugging with GDB: save tracepoints">
<meta name="keywords" content="Debugging with GDB: save tracepoints">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Analyze-Collected-Data.html#Analyze-Collected-Data" rel="up" title="Analyze Collected Data">
<link href="Tracepoint-Variables.html#Tracepoint-Variables" rel="next" title="Tracepoint Variables">
<link href="tdump.html#tdump" rel="previous" title="tdump">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="save-tracepoints"></a>
<div class="header">
<p>
Previous: <a href="tdump.html#tdump" accesskey="p" rel="previous">tdump</a>, Up: <a href="Analyze-Collected-Data.html#Analyze-Collected-Data" accesskey="u" rel="up">Analyze Collected Data</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="save-tracepoints-filename"></a>
<h4 class="subsection">13.2.3 <code>save tracepoints <var>filename</var></code></h4>
<a name="index-save-tracepoints"></a>
<a name="index-save_002dtracepoints"></a>
<a name="index-save-tracepoints-for-future-sessions"></a>

<p>This command saves all current tracepoint definitions together with
their actions and passcounts, into a file <samp><var>filename</var></samp>
suitable for use in a later debugging session.  To read the saved
tracepoint definitions, use the <code>source</code> command (see <a href="Command-Files.html#Command-Files">Command Files</a>).  The <code><span class="nolinebreak">save-tracepoints</span></code><!-- /@w --> command is a deprecated
alias for <code>save&nbsp;tracepoints</code><!-- /@w -->
</p>



</body>
</html>
