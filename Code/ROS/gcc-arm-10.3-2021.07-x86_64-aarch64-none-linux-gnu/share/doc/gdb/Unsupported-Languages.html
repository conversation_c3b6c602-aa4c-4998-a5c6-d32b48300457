<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Unsupported Languages</title>

<meta name="description" content="Debugging with GDB: Unsupported Languages">
<meta name="keywords" content="Debugging with GDB: Unsupported Languages">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Languages.html#Languages" rel="up" title="Languages">
<link href="Symbols.html#Symbols" rel="next" title="Symbols">
<link href="Ada-Glitches.html#Ada-Glitches" rel="previous" title="Ada Glitches">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Unsupported-Languages"></a>
<div class="header">
<p>
Previous: <a href="Supported-Languages.html#Supported-Languages" accesskey="p" rel="previous">Supported Languages</a>, Up: <a href="Languages.html#Languages" accesskey="u" rel="up">Languages</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Unsupported-Languages-1"></a>
<h3 class="section">15.5 Unsupported Languages</h3>

<a name="index-unsupported-languages"></a>
<a name="index-minimal-language"></a>
<p>In addition to the other fully-supported programming languages,
<small>GDB</small> also provides a pseudo-language, called <code>minimal</code>.
It does not represent a real programming language, but provides a set
of capabilities close to what the C or assembly languages provide.
This should allow most simple operations to be performed while debugging
an application that uses a language currently not supported by <small>GDB</small>.
</p>
<p>If the language is set to <code>auto</code>, <small>GDB</small> will automatically
select this language if the current frame corresponds to an unsupported
language.
</p>



</body>
</html>
