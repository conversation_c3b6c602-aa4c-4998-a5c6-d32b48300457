<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: gdbinit man</title>

<meta name="description" content="Debugging with GDB: gdbinit man">
<meta name="keywords" content="Debugging with GDB: gdbinit man">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Man-Pages.html#Man-Pages" rel="up" title="Man Pages">
<link href="gdb_002dadd_002dindex-man.html#gdb_002dadd_002dindex-man" rel="next" title="gdb-add-index man">
<link href="gcore-man.html#gcore-man" rel="previous" title="gcore man">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="gdbinit-man"></a>
<div class="header">
<p>
Next: <a href="gdb_002dadd_002dindex-man.html#gdb_002dadd_002dindex-man" accesskey="n" rel="next">gdb-add-index man</a>, Previous: <a href="gcore-man.html#gcore-man" accesskey="p" rel="previous">gcore man</a>, Up: <a href="Man-Pages.html#Man-Pages" accesskey="u" rel="up">Man Pages</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<h4 class="node-heading">gdbinit man</h4>
<a name="gdbinit"></a>
<h3 class="heading">gdbinit</h3>


<div class="format">
<pre class="format">

~/.gdbinit

./.gdbinit
</pre></div>

<p>These files contain <small>GDB</small> commands to automatically execute during
<small>GDB</small> startup.  The lines of contents are canned sequences of commands,
described in
<a href="Sequences.html#Sequences">Sequences</a>.
</p>
<p>Please read more in
<a href="Startup.html#Startup">Startup</a>.
</p>
<dl compact="compact">
<dt><code>(not enabled with <code>--with-system-gdbinit</code> during compilation)</code></dt>
<dd><p>System-wide initialization file.  It is executed unless user specified
<small>GDB</small> option <code>-nx</code> or <code>-n</code>.
See more in
</p></dd>
<dt><code>(not enabled with <code>--with-system-gdbinit-dir</code> during compilation)</code></dt>
<dd><p>System-wide initialization directory.  All files in this directory are
executed on startup unless user specified <small>GDB</small> option <code>-nx</code> or
<code>-n</code>, as long as they have a recognized file extension.
See more in
<a href="System_002dwide-configuration.html#System_002dwide-configuration">System-wide configuration</a>.
</p>
</dd>
<dt><code>~/.gdbinit</code></dt>
<dd><p>User initialization file.  It is executed unless user specified
<small>GDB</small> options <code>-nx</code>, <code>-n</code> or <code>-nh</code>.
</p>
</dd>
<dt><code>./.gdbinit</code></dt>
<dd><p>Initialization file for current directory.  It may need to be enabled with
<small>GDB</small> security command <code>set auto-load local-gdbinit</code>.
See more in
<a href="Init-File-in-the-Current-Directory.html#Init-File-in-the-Current-Directory">Init File in the Current Directory</a>.
</p></dd>
</dl>





</body>
</html>
