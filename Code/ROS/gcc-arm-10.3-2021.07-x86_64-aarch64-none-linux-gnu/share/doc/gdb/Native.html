<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Native</title>

<meta name="description" content="Debugging with GDB: Native">
<meta name="keywords" content="Debugging with GDB: Native">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Configurations.html#Configurations" rel="up" title="Configurations">
<link href="BSD-libkvm-Interface.html#BSD-libkvm-Interface" rel="next" title="BSD libkvm Interface">
<link href="Configurations.html#Configurations" rel="previous" title="Configurations">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Native"></a>
<div class="header">
<p>
Next: <a href="Embedded-OS.html#Embedded-OS" accesskey="n" rel="next">Embedded OS</a>, Up: <a href="Configurations.html#Configurations" accesskey="u" rel="up">Configurations</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Native-1"></a>
<h3 class="section">21.1 Native</h3>

<p>This section describes details specific to particular native
configurations.
</p>
<table class="menu" border="0" cellspacing="0">
<tr><td align="left" valign="top">&bull; <a href="BSD-libkvm-Interface.html#BSD-libkvm-Interface" accesskey="1">BSD libkvm Interface</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Debugging BSD kernel memory images
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Process-Information.html#Process-Information" accesskey="2">Process Information</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Process information
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="DJGPP-Native.html#DJGPP-Native" accesskey="3">DJGPP Native</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Features specific to the DJGPP port
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Cygwin-Native.html#Cygwin-Native" accesskey="4">Cygwin Native</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Features specific to the Cygwin port
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Hurd-Native.html#Hurd-Native" accesskey="5">Hurd Native</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Features specific to <small>GNU</small> Hurd
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Darwin.html#Darwin" accesskey="6">Darwin</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Features specific to Darwin
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="FreeBSD.html#FreeBSD" accesskey="7">FreeBSD</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">Features specific to FreeBSD
</td></tr>
</table>




</body>
</html>
