<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Varying Target Capabilities</title>

<meta name="description" content="Debugging with GDB: Varying Target Capabilities">
<meta name="keywords" content="Debugging with GDB: Varying Target Capabilities">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Agent-Expressions.html#Agent-Expressions" rel="up" title="Agent Expressions">
<link href="Rationale.html#Rationale" rel="next" title="Rationale">
<link href="Using-Agent-Expressions.html#Using-Agent-Expressions" rel="previous" title="Using Agent Expressions">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Varying-Target-Capabilities"></a>
<div class="header">
<p>
Next: <a href="Rationale.html#Rationale" accesskey="n" rel="next">Rationale</a>, Previous: <a href="Using-Agent-Expressions.html#Using-Agent-Expressions" accesskey="p" rel="previous">Using Agent Expressions</a>, Up: <a href="Agent-Expressions.html#Agent-Expressions" accesskey="u" rel="up">Agent Expressions</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Varying-Target-Capabilities-1"></a>
<h3 class="section">F.4 Varying Target Capabilities</h3>

<p>Some targets don&rsquo;t support floating-point, and some would rather not
have to deal with <code>long long</code> operations.  Also, different targets
will have different stack sizes, and different bytecode buffer lengths.
</p>
<p>Thus, GDB needs a way to ask the target about itself.  We haven&rsquo;t worked
out the details yet, but in general, GDB should be able to send the
target a packet asking it to describe itself.  The reply should be a
packet whose length is explicit, so we can add new information to the
packet in future revisions of the agent, without confusing old versions
of GDB, and it should contain a version number.  It should contain at
least the following information:
</p>
<ul>
<li> whether floating point is supported

</li><li> whether <code>long long</code> is supported

</li><li> maximum acceptable size of bytecode stack

</li><li> maximum acceptable length of bytecode expressions

</li><li> which registers are actually available for collection

</li><li> whether the target supports disabled tracepoints

</li></ul>




</body>
</html>
