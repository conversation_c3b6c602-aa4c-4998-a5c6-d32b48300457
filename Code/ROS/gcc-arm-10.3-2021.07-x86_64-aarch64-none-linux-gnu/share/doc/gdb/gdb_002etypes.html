<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: gdb.types</title>

<meta name="description" content="Debugging with GDB: gdb.types">
<meta name="keywords" content="Debugging with GDB: gdb.types">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Python-modules.html#Python-modules" rel="up" title="Python modules">
<link href="gdb_002eprompt.html#gdb_002eprompt" rel="next" title="gdb.prompt">
<link href="gdb_002eprinting.html#gdb_002eprinting" rel="previous" title="gdb.printing">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="gdb_002etypes"></a>
<div class="header">
<p>
Next: <a href="gdb_002eprompt.html#gdb_002eprompt" accesskey="n" rel="next">gdb.prompt</a>, Previous: <a href="gdb_002eprinting.html#gdb_002eprinting" accesskey="p" rel="previous">gdb.printing</a>, Up: <a href="Python-modules.html#Python-modules" accesskey="u" rel="up">Python modules</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="gdb_002etypes-1"></a>
<h4 class="subsubsection">******** gdb.types</h4>
<a name="index-gdb_002etypes"></a>

<p>This module provides a collection of utilities for working with
<code>gdb.Type</code> objects.
</p>
<dl compact="compact">
<dt><code>get_basic_type (<var>type</var>)</code></dt>
<dd><p>Return <var>type</var> with const and volatile qualifiers stripped,
and with typedefs and C<tt>++</tt> references converted to the underlying type.
</p>
<p>C<tt>++</tt> example:
</p>
<div class="smallexample">
<pre class="smallexample">typedef const int const_int;
const_int foo (3);
const_int&amp; foo_ref (foo);
int main () { return 0; }
</pre></div>

<p>Then in gdb:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) start
(gdb) python import gdb.types
(gdb) python foo_ref = gdb.parse_and_eval(&quot;foo_ref&quot;)
(gdb) python print gdb.types.get_basic_type(foo_ref.type)
int
</pre></div>

</dd>
<dt><code>has_field (<var>type</var>, <var>field</var>)</code></dt>
<dd><p>Return <code>True</code> if <var>type</var>, assumed to be a type with fields
(e.g., a structure or union), has field <var>field</var>.
</p>
</dd>
<dt><code>make_enum_dict (<var>enum_type</var>)</code></dt>
<dd><p>Return a Python <code>dictionary</code> type produced from <var>enum_type</var>.
</p>
</dd>
<dt><code>deep_items (<var>type</var>)</code></dt>
<dd><p>Returns a Python iterator similar to the standard
<code>gdb.Type.iteritems</code> method, except that the iterator returned
by <code>deep_items</code> will recursively traverse anonymous struct or
union fields.  For example:
</p>
<div class="smallexample">
<pre class="smallexample">struct A
{
    int a;
    union {
        int b0;
        int b1;
    };
};
</pre></div>

<p>Then in <small>GDB</small>:
</p><div class="smallexample">
<pre class="smallexample">(gdb) python import gdb.types
(gdb) python struct_a = gdb.lookup_type(&quot;struct A&quot;)
(gdb) python print struct_a.keys ()
{['a', '']}
(gdb) python print [k for k,v in gdb.types.deep_items(struct_a)]
{['a', 'b0', 'b1']}
</pre></div>

</dd>
<dt><code>get_type_recognizers ()</code></dt>
<dd><p>Return a list of the enabled type recognizers for the current context.
This is called by <small>GDB</small> during the type-printing process
(see <a href="Type-Printing-API.html#Type-Printing-API">Type Printing API</a>).
</p>
</dd>
<dt><code>apply_type_recognizers (recognizers, type_obj)</code></dt>
<dd><p>Apply the type recognizers, <var>recognizers</var>, to the type object
<var>type_obj</var>.  If any recognizer returns a string, return that
string.  Otherwise, return <code>None</code>.  This is called by
<small>GDB</small> during the type-printing process (see <a href="Type-Printing-API.html#Type-Printing-API">Type Printing API</a>).
</p>
</dd>
<dt><code>register_type_printer (locus, printer)</code></dt>
<dd><p>This is a convenience function to register a type printer
<var>printer</var>.  The printer must implement the type printer protocol.
The <var>locus</var> argument is either a <code>gdb.Objfile</code>, in which case
the printer is registered with that objfile; a <code>gdb.Progspace</code>,
in which case the printer is registered with that progspace; or
<code>None</code>, in which case the printer is registered globally.
</p>
</dd>
<dt><code>TypePrinter</code></dt>
<dd><p>This is a base class that implements the type printer protocol.  Type
printers are encouraged, but not required, to derive from this class.
It defines a constructor:
</p>
<dl>
<dt><a name="index-_005f_005finit_005f_005f-on-TypePrinter"></a>Method on TypePrinter: <strong>__init__</strong> <em>(self, name)</em></dt>
<dd><p>Initialize the type printer with the given name.  The new printer
starts in the enabled state.
</p></dd></dl>

</dd>
</dl>

<hr>
<div class="header">
<p>
Next: <a href="gdb_002eprompt.html#gdb_002eprompt" accesskey="n" rel="next">gdb.prompt</a>, Previous: <a href="gdb_002eprinting.html#gdb_002eprinting" accesskey="p" rel="previous">gdb.printing</a>, Up: <a href="Python-modules.html#Python-modules" accesskey="u" rel="up">Python modules</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
