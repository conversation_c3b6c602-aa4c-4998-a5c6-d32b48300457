<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Using Agent Expressions</title>

<meta name="description" content="Debugging with GDB: Using Agent Expressions">
<meta name="keywords" content="Debugging with GDB: Using Agent Expressions">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Agent-Expressions.html#Agent-Expressions" rel="up" title="Agent Expressions">
<link href="Varying-Target-Capabilities.html#Varying-Target-Capabilities" rel="next" title="Varying Target Capabilities">
<link href="Bytecode-Descriptions.html#Bytecode-Descriptions" rel="previous" title="Bytecode Descriptions">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Using-Agent-Expressions"></a>
<div class="header">
<p>
Next: <a href="Varying-Target-Capabilities.html#Varying-Target-Capabilities" accesskey="n" rel="next">Varying Target Capabilities</a>, Previous: <a href="Bytecode-Descriptions.html#Bytecode-Descriptions" accesskey="p" rel="previous">Bytecode Descriptions</a>, Up: <a href="Agent-Expressions.html#Agent-Expressions" accesskey="u" rel="up">Agent Expressions</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Using-Agent-Expressions-1"></a>
<h3 class="section">F.3 Using Agent Expressions</h3>

<p>Agent expressions can be used in several different ways by <small>GDB</small>,
and the debugger can generate different bytecode sequences as appropriate.
</p>
<p>One possibility is to do expression evaluation on the target rather
than the host, such as for the conditional of a conditional
tracepoint.  In such a case, <small>GDB</small> compiles the source
expression into a bytecode sequence that simply gets values from
registers or memory, does arithmetic, and returns a result.
</p>
<p>Another way to use agent expressions is for tracepoint data
collection.  <small>GDB</small> generates a different bytecode sequence for
collection; in addition to bytecodes that do the calculation,
<small>GDB</small> adds <code>trace</code> bytecodes to save the pieces of
memory that were used.
</p>
<ul>
<li> The user selects trace points in the program&rsquo;s code at which GDB should
collect data.

</li><li> The user specifies expressions to evaluate at each trace point.  These
expressions may denote objects in memory, in which case those objects&rsquo;
contents are recorded as the program runs, or computed values, in which
case the values themselves are recorded.

</li><li> GDB transmits the tracepoints and their associated expressions to the
GDB agent, running on the debugging target.

</li><li> The agent arranges to be notified when a trace point is hit.

</li><li> When execution on the target reaches a trace point, the agent evaluates
the expressions associated with that trace point, and records the
resulting values and memory ranges.

</li><li> Later, when the user selects a given trace event and inspects the
objects and expression values recorded, GDB talks to the agent to
retrieve recorded data as necessary to meet the user&rsquo;s requests.  If the
user asks to see an object whose contents have not been recorded, GDB
reports an error.

</li></ul>


<hr>
<div class="header">
<p>
Next: <a href="Varying-Target-Capabilities.html#Varying-Target-Capabilities" accesskey="n" rel="next">Varying Target Capabilities</a>, Previous: <a href="Bytecode-Descriptions.html#Bytecode-Descriptions" accesskey="p" rel="previous">Bytecode Descriptions</a>, Up: <a href="Agent-Expressions.html#Agent-Expressions" accesskey="u" rel="up">Agent Expressions</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
