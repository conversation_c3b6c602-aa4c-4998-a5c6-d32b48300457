<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Xmethod API</title>

<meta name="description" content="Debugging with GDB: Xmethod API">
<meta name="keywords" content="Debugging with GDB: Xmethod API">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Python-API.html#Python-API" rel="up" title="Python API">
<link href="Writing-an-Xmethod.html#Writing-an-Xmethod" rel="next" title="Writing an Xmethod">
<link href="Xmethods-In-Python.html#Xmethods-In-Python" rel="previous" title="Xmethods In Python">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Xmethod-API"></a>
<div class="header">
<p>
Next: <a href="Writing-an-Xmethod.html#Writing-an-Xmethod" accesskey="n" rel="next">Writing an Xmethod</a>, Previous: <a href="Xmethods-In-Python.html#Xmethods-In-Python" accesskey="p" rel="previous">Xmethods In Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Xmethod-API-1"></a>
<h4 class="subsubsection">********* Xmethod API</h4>
<a name="index-xmethod-API"></a>

<p>The <small>GDB</small> Python API provides classes, interfaces and functions
to implement, register and manipulate xmethods.
See <a href="Xmethods-In-Python.html#Xmethods-In-Python">Xmethods In Python</a>.
</p>
<p>An xmethod matcher should be an instance of a class derived from
<code>XMethodMatcher</code> defined in the module <code>gdb.xmethod</code>, or an
object with similar interface and attributes.  An instance of
<code>XMethodMatcher</code> has the following attributes:
</p>
<dl>
<dt><a name="index-name"></a>Variable: <strong>name</strong></dt>
<dd><p>The name of the matcher.
</p></dd></dl>

<dl>
<dt><a name="index-enabled"></a>Variable: <strong>enabled</strong></dt>
<dd><p>A boolean value indicating whether the matcher is enabled or disabled.
</p></dd></dl>

<dl>
<dt><a name="index-methods"></a>Variable: <strong>methods</strong></dt>
<dd><p>A list of named methods managed by the matcher.  Each object in the list
is an instance of the class <code>XMethod</code> defined in the module
<code>gdb.xmethod</code>, or any object with the following attributes:
</p>
<dl compact="compact">
<dt><code>name</code></dt>
<dd><p>Name of the xmethod which should be unique for each xmethod
managed by the matcher.
</p>
</dd>
<dt><code>enabled</code></dt>
<dd><p>A boolean value indicating whether the xmethod is enabled or
disabled.
</p>
</dd>
</dl>

<p>The class <code>XMethod</code> is a convenience class with same
attributes as above along with the following constructor:
</p>
<dl>
<dt><a name="index-XMethod_002e_005f_005finit_005f_005f"></a>Function: <strong>XMethod.__init__</strong> <em>(self, name)</em></dt>
<dd><p>Constructs an enabled xmethod with name <var>name</var>.
</p></dd></dl>
</dd></dl>

<p>The <code>XMethodMatcher</code> class has the following methods:
</p>
<dl>
<dt><a name="index-XMethodMatcher_002e_005f_005finit_005f_005f"></a>Function: <strong>XMethodMatcher.__init__</strong> <em>(self, name)</em></dt>
<dd><p>Constructs an enabled xmethod matcher with name <var>name</var>.  The
<code>methods</code> attribute is initialized to <code>None</code>.
</p></dd></dl>

<dl>
<dt><a name="index-XMethodMatcher_002ematch"></a>Function: <strong>XMethodMatcher.match</strong> <em>(self, class_type, method_name)</em></dt>
<dd><p>Derived classes should override this method.  It should return a
xmethod worker object (or a sequence of xmethod worker
objects) matching the <var>class_type</var> and <var>method_name</var>.
<var>class_type</var> is a <code>gdb.Type</code> object, and <var>method_name</var>
is a string value.  If the matcher manages named methods as listed in
its <code>methods</code> attribute, then only those worker objects whose
corresponding entries in the <code>methods</code> list are enabled should be
returned.
</p></dd></dl>

<p>An xmethod worker should be an instance of a class derived from
<code>XMethodWorker</code> defined in the module <code>gdb.xmethod</code>,
or support the following interface:
</p>
<dl>
<dt><a name="index-XMethodWorker_002eget_005farg_005ftypes"></a>Function: <strong>XMethodWorker.get_arg_types</strong> <em>(self)</em></dt>
<dd><p>This method returns a sequence of <code>gdb.Type</code> objects corresponding
to the arguments that the xmethod takes.  It can return an empty
sequence or <code>None</code> if the xmethod does not take any arguments.
If the xmethod takes a single argument, then a single
<code>gdb.Type</code> object corresponding to it can be returned.
</p></dd></dl>

<dl>
<dt><a name="index-XMethodWorker_002eget_005fresult_005ftype"></a>Function: <strong>XMethodWorker.get_result_type</strong> <em>(self, *args)</em></dt>
<dd><p>This method returns a <code>gdb.Type</code> object representing the type
of the result of invoking this xmethod.
The <var>args</var> argument is the same tuple of arguments that would be
passed to the <code>__call__</code> method of this worker.
</p></dd></dl>

<dl>
<dt><a name="index-XMethodWorker_002e_005f_005fcall_005f_005f"></a>Function: <strong>XMethodWorker.__call__</strong> <em>(self, *args)</em></dt>
<dd><p>This is the method which does the <em>work</em> of the xmethod.  The
<var>args</var> arguments is the tuple of arguments to the xmethod.  Each
element in this tuple is a gdb.Value object.  The first element is
always the <code>this</code> pointer value.
</p></dd></dl>

<p>For <small>GDB</small> to lookup xmethods, the xmethod matchers
should be registered using the following function defined in the module
<code>gdb.xmethod</code>:
</p>
<dl>
<dt><a name="index-register_005fxmethod_005fmatcher"></a>Function: <strong>register_xmethod_matcher</strong> <em>(locus, matcher, replace=False)</em></dt>
<dd><p>The <code>matcher</code> is registered with <code>locus</code>, replacing an
existing matcher with the same name as <code>matcher</code> if
<code>replace</code> is <code>True</code>.  <code>locus</code> can be a
<code>gdb.Objfile</code> object (see <a href="Objfiles-In-Python.html#Objfiles-In-Python">Objfiles In Python</a>), or a
<code>gdb.Progspace</code> object (see <a href="Progspaces-In-Python.html#Progspaces-In-Python">Progspaces In Python</a>), or
<code>None</code>.  If it is <code>None</code>, then <code>matcher</code> is registered
globally.
</p></dd></dl>

<hr>
<div class="header">
<p>
Next: <a href="Writing-an-Xmethod.html#Writing-an-Xmethod" accesskey="n" rel="next">Writing an Xmethod</a>, Previous: <a href="Xmethods-In-Python.html#Xmethods-In-Python" accesskey="p" rel="previous">Xmethods In Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
