<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Tracepoint Passcounts</title>

<meta name="description" content="Debugging with GDB: Tracepoint Passcounts">
<meta name="keywords" content="Debugging with GDB: Tracepoint Passcounts">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Set-Tracepoints.html#Set-Tracepoints" rel="up" title="Set Tracepoints">
<link href="Tracepoint-Conditions.html#Tracepoint-Conditions" rel="next" title="Tracepoint Conditions">
<link href="Enable-and-Disable-Tracepoints.html#Enable-and-Disable-Tracepoints" rel="previous" title="Enable and Disable Tracepoints">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Tracepoint-Passcounts"></a>
<div class="header">
<p>
Next: <a href="Tracepoint-Conditions.html#Tracepoint-Conditions" accesskey="n" rel="next">Tracepoint Conditions</a>, Previous: <a href="Enable-and-Disable-Tracepoints.html#Enable-and-Disable-Tracepoints" accesskey="p" rel="previous">Enable and Disable Tracepoints</a>, Up: <a href="Set-Tracepoints.html#Set-Tracepoints" accesskey="u" rel="up">Set Tracepoints</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Tracepoint-Passcounts-1"></a>
<h4 class="subsection">13.1.3 Tracepoint Passcounts</h4>

<dl compact="compact">
<dd><a name="index-passcount"></a>
<a name="index-tracepoint-pass-count"></a>
</dd>
<dt><code>passcount <span class="roman">[</span><var>n</var> <span class="roman">[</span><var>num</var><span class="roman">]]</span></code></dt>
<dd><p>Set the <em>passcount</em> of a tracepoint.  The passcount is a way to
automatically stop a trace experiment.  If a tracepoint&rsquo;s passcount is
<var>n</var>, then the trace experiment will be automatically stopped on
the <var>n</var>&rsquo;th time that tracepoint is hit.  If the tracepoint number
<var>num</var> is not specified, the <code>passcount</code> command sets the
passcount of the most recently defined tracepoint.  If no passcount is
given, the trace experiment will run until stopped explicitly by the
user.
</p>
<p>Examples:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) <b>passcount 5 2</b> // Stop on the 5th execution of
</pre><pre class="smallexample">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>// tracepoint 2</code>
</pre><pre class="smallexample">
(gdb) <b>passcount 12</b>  // Stop on the 12th execution of the
</pre><pre class="smallexample">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>// most recently defined tracepoint.</code>
</pre><pre class="smallexample">(gdb) <b>trace foo</b>
(gdb) <b>pass 3</b>
(gdb) <b>trace bar</b>
(gdb) <b>pass 2</b>
(gdb) <b>trace baz</b>
(gdb) <b>pass 1</b>        // Stop tracing when foo has been
</pre><pre class="smallexample">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>// executed 3 times OR when bar has</code>
</pre><pre class="smallexample">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>// been executed 2 times</code>
</pre><pre class="smallexample">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>// OR when baz has been executed 1 time.</code>
</pre></div>
</dd>
</dl>




</body>
</html>
