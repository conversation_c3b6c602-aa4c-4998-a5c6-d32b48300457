<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: dotdebug_gdb_scripts section</title>

<meta name="description" content="Debugging with GDB: dotdebug_gdb_scripts section">
<meta name="keywords" content="Debugging with GDB: dotdebug_gdb_scripts section">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Auto_002dloading-extensions.html#Auto_002dloading-extensions" rel="up" title="Auto-loading extensions">
<link href="Which-flavor-to-choose_003f.html#Which-flavor-to-choose_003f" rel="next" title="Which flavor to choose?">
<link href="objfile_002dgdbdotext-file.html#objfile_002dgdbdotext-file" rel="previous" title="objfile-gdbdotext file">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="dotdebug_005fgdb_005fscripts-section"></a>
<div class="header">
<p>
Next: <a href="Which-flavor-to-choose_003f.html#Which-flavor-to-choose_003f" accesskey="n" rel="next">Which flavor to choose?</a>, Previous: <a href="objfile_002dgdbdotext-file.html#objfile_002dgdbdotext-file" accesskey="p" rel="previous">objfile-gdbdotext file</a>, Up: <a href="Auto_002dloading-extensions.html#Auto_002dloading-extensions" accesskey="u" rel="up">Auto-loading extensions</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="The-_002edebug_005fgdb_005fscripts-Section"></a>
<h4 class="subsection">23.4.2 The <code>.debug_gdb_scripts</code> Section</h4>
<a name="index-_002edebug_005fgdb_005fscripts-section"></a>

<p>For systems using file formats like ELF and COFF,
when <small>GDB</small> loads a new object file
it will look for a special section named <code>.debug_gdb_scripts</code>.
If this section exists, its contents is a list of null-terminated entries
specifying scripts to load.  Each entry begins with a non-null prefix byte that
specifies the kind of entry, typically the extension language and whether the
script is in a file or inlined in <code>.debug_gdb_scripts</code>.
</p>
<p>The following entries are supported:
</p>
<dl compact="compact">
<dt><code>SECTION_SCRIPT_ID_PYTHON_FILE = 1</code></dt>
<dt><code>SECTION_SCRIPT_ID_SCHEME_FILE = 3</code></dt>
<dt><code>SECTION_SCRIPT_ID_PYTHON_TEXT = 4</code></dt>
<dt><code>SECTION_SCRIPT_ID_SCHEME_TEXT = 6</code></dt>
</dl>

<a name="Script-File-Entries"></a>
<h4 class="subsubsection">******** Script File Entries</h4>

<p>If the entry specifies a file, <small>GDB</small> will look for the file first
in the current directory and then along the source search path
(see <a href="Source-Path.html#Source-Path">Specifying Source Directories</a>),
except that <samp>$cdir</samp> is not searched, since the compilation
directory is not relevant to scripts.
</p>
<p>File entries can be placed in section <code>.debug_gdb_scripts</code> with,
for example, this GCC macro for Python scripts.
</p>
<div class="example">
<pre class="example">/* Note: The &quot;MS&quot; section flags are to remove duplicates.  */
#define DEFINE_GDB_PY_SCRIPT(script_name) \
  asm(&quot;\
.pushsection \&quot;.debug_gdb_scripts\&quot;, \&quot;MS\&quot;,@progbits,1\n\
.byte 1 /* Python */\n\
.asciz \&quot;&quot; script_name &quot;\&quot;\n\
.popsection \n\
&quot;);
</pre></div>

<p>For Guile scripts, replace <code>.byte 1</code> with <code>.byte 3</code>.
Then one can reference the macro in a header or source file like this:
</p>
<div class="example">
<pre class="example">DEFINE_GDB_PY_SCRIPT (&quot;my-app-scripts.py&quot;)
</pre></div>

<p>The script name may include directories if desired.
</p>
<p>Note that loading of this script file also requires accordingly configured
<code>auto-load safe-path</code> (see <a href="Auto_002dloading-safe-path.html#Auto_002dloading-safe-path">Auto-loading safe path</a>).
</p>
<p>If the macro invocation is put in a header, any application or library
using this header will get a reference to the specified script,
and with the use of <code>&quot;MS&quot;</code> attributes on the section, the linker
will remove duplicates.
</p>
<a name="Script-Text-Entries"></a>
<h4 class="subsubsection">******** Script Text Entries</h4>

<p>Script text entries allow to put the executable script in the entry
itself instead of loading it from a file.
The first line of the entry, everything after the prefix byte and up to
the first newline (<code>0xa</code>) character, is the script name, and must not
contain any kind of space character, e.g., spaces or tabs.
The rest of the entry, up to the trailing null byte, is the script to
execute in the specified language.  The name needs to be unique among
all script names, as <small>GDB</small> executes each script only once based
on its name.
</p>
<p>Here is an example from file <samp>py-section-script.c</samp> in the <small>GDB</small>
testsuite.
</p>
<div class="example">
<pre class="example">#include &quot;symcat.h&quot;
#include &quot;gdb/section-scripts.h&quot;
asm(
&quot;.pushsection \&quot;.debug_gdb_scripts\&quot;, \&quot;MS\&quot;,@progbits,1\n&quot;
&quot;.byte &quot; XSTRING (SECTION_SCRIPT_ID_PYTHON_TEXT) &quot;\n&quot;
&quot;.ascii \&quot;gdb.inlined-script\\n\&quot;\n&quot;
&quot;.ascii \&quot;class test_cmd (gdb.Command):\\n\&quot;\n&quot;
&quot;.ascii \&quot;  def __init__ (self):\\n\&quot;\n&quot;
&quot;.ascii \&quot;    super (test_cmd, self).__init__ (&quot;
    &quot;\\\&quot;test-cmd\\\&quot;, gdb.COMMAND_OBSCURE)\\n\&quot;\n&quot;
&quot;.ascii \&quot;  def invoke (self, arg, from_tty):\\n\&quot;\n&quot;
&quot;.ascii \&quot;    print (\\\&quot;test-cmd output, arg = %s\\\&quot; % arg)\\n\&quot;\n&quot;
&quot;.ascii \&quot;test_cmd ()\\n\&quot;\n&quot;
&quot;.byte 0\n&quot;
&quot;.popsection\n&quot;
);
</pre></div>

<p>Loading of inlined scripts requires a properly configured
<code>auto-load safe-path</code> (see <a href="Auto_002dloading-safe-path.html#Auto_002dloading-safe-path">Auto-loading safe path</a>).
The path to specify in <code>auto-load safe-path</code> is the path of the file
containing the <code>.debug_gdb_scripts</code> section.
</p>
<hr>
<div class="header">
<p>
Next: <a href="Which-flavor-to-choose_003f.html#Which-flavor-to-choose_003f" accesskey="n" rel="next">Which flavor to choose?</a>, Previous: <a href="objfile_002dgdbdotext-file.html#objfile_002dgdbdotext-file" accesskey="p" rel="previous">objfile-gdbdotext file</a>, Up: <a href="Auto_002dloading-extensions.html#Auto_002dloading-extensions" accesskey="u" rel="up">Auto-loading extensions</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
