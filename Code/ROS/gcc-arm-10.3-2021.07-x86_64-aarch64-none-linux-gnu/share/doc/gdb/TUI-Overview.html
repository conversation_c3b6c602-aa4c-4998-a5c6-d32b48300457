<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: TUI Overview</title>

<meta name="description" content="Debugging with GDB: TUI Overview">
<meta name="keywords" content="Debugging with GDB: TUI Overview">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="TUI.html#TUI" rel="up" title="TUI">
<link href="TUI-Keys.html#TUI-Keys" rel="next" title="TUI Keys">
<link href="TUI.html#TUI" rel="previous" title="TUI">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="TUI-Overview"></a>
<div class="header">
<p>
Next: <a href="TUI-Keys.html#TUI-Keys" accesskey="n" rel="next">TUI Keys</a>, Up: <a href="TUI.html#TUI" accesskey="u" rel="up">TUI</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="TUI-Overview-1"></a>
<h3 class="section">25.1 TUI Overview</h3>

<p>In TUI mode, <small>GDB</small> can display several text windows:
</p>
<dl compact="compact">
<dt><em>command</em></dt>
<dd><p>This window is the <small>GDB</small> command window with the <small>GDB</small>
prompt and the <small>GDB</small> output.  The <small>GDB</small> input is still
managed using readline.
</p>
</dd>
<dt><em>source</em></dt>
<dd><p>The source window shows the source file of the program.  The current
line and active breakpoints are displayed in this window.
</p>
</dd>
<dt><em>assembly</em></dt>
<dd><p>The assembly window shows the disassembly output of the program.
</p>
</dd>
<dt><em>register</em></dt>
<dd><p>This window shows the processor registers.  Registers are highlighted
when their values change.
</p></dd>
</dl>

<p>The source and assembly windows show the current program position
by highlighting the current line and marking it with a &lsquo;<samp>&gt;</samp>&rsquo; marker.
Breakpoints are indicated with two markers.  The first marker
indicates the breakpoint type:
</p>
<dl compact="compact">
<dt><code>B</code></dt>
<dd><p>Breakpoint which was hit at least once.
</p>
</dd>
<dt><code>b</code></dt>
<dd><p>Breakpoint which was never hit.
</p>
</dd>
<dt><code>H</code></dt>
<dd><p>Hardware breakpoint which was hit at least once.
</p>
</dd>
<dt><code>h</code></dt>
<dd><p>Hardware breakpoint which was never hit.
</p></dd>
</dl>

<p>The second marker indicates whether the breakpoint is enabled or not:
</p>
<dl compact="compact">
<dt><code>+</code></dt>
<dd><p>Breakpoint is enabled.
</p>
</dd>
<dt><code>-</code></dt>
<dd><p>Breakpoint is disabled.
</p></dd>
</dl>

<p>The source, assembly and register windows are updated when the current
thread changes, when the frame changes, or when the program counter
changes.
</p>
<p>These windows are not all visible at the same time.  The command
window is always visible.  The others can be arranged in several
layouts:
</p>
<ul>
<li> source only,

</li><li> assembly only,

</li><li> source and assembly,

</li><li> source and registers, or

</li><li> assembly and registers.
</li></ul>

<p>These are the standard layouts, but other layouts can be defined.
</p>
<p>A status line above the command window shows the following information:
</p>
<dl compact="compact">
<dt><em>target</em></dt>
<dd><p>Indicates the current <small>GDB</small> target.
(see <a href="Targets.html#Targets">Specifying a Debugging Target</a>).
</p>
</dd>
<dt><em>process</em></dt>
<dd><p>Gives the current process or thread number.
When no process is being debugged, this field is set to <code>No process</code>.
</p>
</dd>
<dt><em>function</em></dt>
<dd><p>Gives the current function name for the selected frame.
The name is demangled if demangling is turned on (see <a href="Print-Settings.html#Print-Settings">Print Settings</a>).
When there is no symbol corresponding to the current program counter,
the string <code>??</code> is displayed.
</p>
</dd>
<dt><em>line</em></dt>
<dd><p>Indicates the current line number for the selected frame.
When the current line number is not known, the string <code>??</code> is displayed.
</p>
</dd>
<dt><em>pc</em></dt>
<dd><p>Indicates the current program counter address.
</p></dd>
</dl>

<hr>
<div class="header">
<p>
Next: <a href="TUI-Keys.html#TUI-Keys" accesskey="n" rel="next">TUI Keys</a>, Up: <a href="TUI.html#TUI" accesskey="u" rel="up">TUI</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
