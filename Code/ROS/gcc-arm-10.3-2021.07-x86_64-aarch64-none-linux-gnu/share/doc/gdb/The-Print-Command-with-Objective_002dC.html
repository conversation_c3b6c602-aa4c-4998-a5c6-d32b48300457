<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: The Print Command with Objective-C</title>

<meta name="description" content="Debugging with GDB: The Print Command with Objective-C">
<meta name="keywords" content="Debugging with GDB: The Print Command with Objective-C">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Objective_002dC.html#Objective_002dC" rel="up" title="Objective-C">
<link href="OpenCL-C.html#OpenCL-C" rel="next" title="OpenCL C">
<link href="Method-Names-in-Commands.html#Method-Names-in-Commands" rel="previous" title="Method Names in Commands">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="The-Print-Command-with-Objective_002dC"></a>
<div class="header">
<p>
Previous: <a href="Method-Names-in-Commands.html#Method-Names-in-Commands" accesskey="p" rel="previous">Method Names in Commands</a>, Up: <a href="Objective_002dC.html#Objective_002dC" accesskey="u" rel="up">Objective-C</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="The-Print-Command-with-Objective_002dC-1"></a>
<h4 class="subsubsection">******** The Print Command with Objective-C</h4>
<a name="index-Objective_002dC_002c-print-objects"></a>
<a name="index-print_002dobject"></a>
<a name="index-po-_0028print_002dobject_0029"></a>

<p>The print command has also been extended to accept methods.  For example:
</p>
<div class="smallexample">
<pre class="smallexample">print -[<var>object</var> hash]
</pre></div>

<a name="index-print-an-Objective_002dC-object-description"></a>
<a name="index-_005fNSPrintForDebugger_002c-and-printing-Objective_002dC-objects"></a>
<p>will tell <small>GDB</small> to send the <code>hash</code> message to <var>object</var>
and print the result.  Also, an additional command has been added,
<code>print-object</code> or <code>po</code> for short, which is meant to print
the description of an object.  However, this command may only work
with certain Objective-C libraries that have a particular hook
function, <code>_NSPrintForDebugger</code>, defined.
</p>



</body>
</html>
