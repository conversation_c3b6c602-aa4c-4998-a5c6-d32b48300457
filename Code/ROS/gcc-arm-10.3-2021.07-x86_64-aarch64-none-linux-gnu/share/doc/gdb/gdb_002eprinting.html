<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: gdb.printing</title>

<meta name="description" content="Debugging with GDB: gdb.printing">
<meta name="keywords" content="Debugging with GDB: gdb.printing">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Python-modules.html#Python-modules" rel="up" title="Python modules">
<link href="gdb_002etypes.html#gdb_002etypes" rel="next" title="gdb.types">
<link href="Python-modules.html#Python-modules" rel="previous" title="Python modules">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="gdb_002eprinting"></a>
<div class="header">
<p>
Next: <a href="gdb_002etypes.html#gdb_002etypes" accesskey="n" rel="next">gdb.types</a>, Up: <a href="Python-modules.html#Python-modules" accesskey="u" rel="up">Python modules</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="gdb_002eprinting-1"></a>
<h4 class="subsubsection">23.2.4.1 gdb.printing</h4>
<a name="index-gdb_002eprinting"></a>

<p>This module provides a collection of utilities for working with
pretty-printers.
</p>
<dl compact="compact">
<dt><code>PrettyPrinter (<var>name</var>, <var>subprinters</var>=None)</code></dt>
<dd><p>This class specifies the API that makes &lsquo;<samp>info pretty-printer</samp>&rsquo;,
&lsquo;<samp>enable pretty-printer</samp>&rsquo; and &lsquo;<samp>disable pretty-printer</samp>&rsquo; work.
Pretty-printers should generally inherit from this class.
</p>
</dd>
<dt><code>SubPrettyPrinter (<var>name</var>)</code></dt>
<dd><p>For printers that handle multiple types, this class specifies the
corresponding API for the subprinters.
</p>
</dd>
<dt><code>RegexpCollectionPrettyPrinter (<var>name</var>)</code></dt>
<dd><p>Utility class for handling multiple printers, all recognized via
regular expressions.
See <a href="Writing-a-Pretty_002dPrinter.html#Writing-a-Pretty_002dPrinter">Writing a Pretty-Printer</a>, for an example.
</p>
</dd>
<dt><code>FlagEnumerationPrinter (<var>name</var>)</code></dt>
<dd><p>A pretty-printer which handles printing of <code>enum</code> values.  Unlike
<small>GDB</small>&rsquo;s built-in <code>enum</code> printing, this printer attempts to
work properly when there is some overlap between the enumeration
constants.  The argument <var>name</var> is the name of the printer and
also the name of the <code>enum</code> type to look up.
</p>
</dd>
<dt><code>register_pretty_printer (<var>obj</var>, <var>printer</var>, <var>replace</var>=False)</code></dt>
<dd><p>Register <var>printer</var> with the pretty-printer list of <var>obj</var>.
If <var>replace</var> is <code>True</code> then any existing copy of the printer
is replaced.  Otherwise a <code>RuntimeError</code> exception is raised
if a printer with the same name already exists.
</p></dd>
</dl>




</body>
</html>
