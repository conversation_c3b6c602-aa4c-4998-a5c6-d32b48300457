<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Notification Packets</title>

<meta name="description" content="Debugging with GDB: Notification Packets">
<meta name="keywords" content="Debugging with GDB: Notification Packets">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Remote-Protocol.html#Remote-Protocol" rel="up" title="Remote Protocol">
<link href="Remote-Non_002dStop.html#Remote-Non_002dStop" rel="next" title="Remote Non-Stop">
<link href="Interrupts.html#Interrupts" rel="previous" title="Interrupts">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Notification-Packets"></a>
<div class="header">
<p>
Next: <a href="Remote-Non_002dStop.html#Remote-Non_002dStop" accesskey="n" rel="next">Remote Non-Stop</a>, Previous: <a href="Interrupts.html#Interrupts" accesskey="p" rel="previous">Interrupts</a>, Up: <a href="Remote-Protocol.html#Remote-Protocol" accesskey="u" rel="up">Remote Protocol</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Notification-Packets-1"></a>
<h3 class="section">E.9 Notification Packets</h3>
<a name="index-notification-packets"></a>
<a name="index-packets_002c-notification"></a>

<p>The <small>GDB</small> remote serial protocol includes <em>notifications</em>,
packets that require no acknowledgment.  Both the GDB and the stub
may send notifications (although the only notifications defined at
present are sent by the stub).  Notifications carry information
without incurring the round-trip latency of an acknowledgment, and so
are useful for low-impact communications where occasional packet loss
is not a problem.
</p>
<p>A notification packet has the form &lsquo;<samp>% <var>data</var> #
<var>checksum</var></samp>&rsquo;, where <var>data</var> is the content of the notification,
and <var>checksum</var> is a checksum of <var>data</var>, computed and formatted
as for ordinary <small>GDB</small> packets.  A notification&rsquo;s <var>data</var>
never contains &lsquo;<samp>$</samp>&rsquo;, &lsquo;<samp>%</samp>&rsquo; or &lsquo;<samp>#</samp>&rsquo; characters.  Upon
receiving a notification, the recipient sends no &lsquo;<samp>+</samp>&rsquo; or &lsquo;<samp>-</samp>&rsquo;
to acknowledge the notification&rsquo;s receipt or to report its corruption.
</p>
<p>Every notification&rsquo;s <var>data</var> begins with a name, which contains no
colon characters, followed by a colon character.
</p>
<p>Recipients should silently ignore corrupted notifications and
notifications they do not understand.  Recipients should restart
timeout periods on receipt of a well-formed notification, whether or
not they understand it.
</p>
<p>Senders should only send the notifications described here when this
protocol description specifies that they are permitted.  In the
future, we may extend the protocol to permit existing notifications in
new contexts; this rule helps older senders avoid confusing newer
recipients.
</p>
<p>(Older versions of <small>GDB</small> ignore bytes received until they see
the &lsquo;<samp>$</samp>&rsquo; byte that begins an ordinary packet, so new stubs may
transmit notifications without fear of confusing older clients.  There
are no notifications defined for <small>GDB</small> to send at the moment, but we
assume that most older stubs would ignore them, as well.)
</p>
<p>Each notification is comprised of three parts:
</p><dl compact="compact">
<dt>&lsquo;<samp><var>name</var>:<var>event</var></samp>&rsquo;</dt>
<dd><p>The notification packet is sent by the side that initiates the
exchange (currently, only the stub does that), with <var>event</var>
carrying the specific information about the notification, and
<var>name</var> specifying the name of the notification.
</p></dd>
<dt>&lsquo;<samp><var>ack</var></samp>&rsquo;</dt>
<dd><p>The acknowledge sent by the other side, usually <small>GDB</small>, to
acknowledge the exchange and request the event.
</p></dd>
</dl>

<p>The purpose of an asynchronous notification mechanism is to report to
<small>GDB</small> that something interesting happened in the remote stub.
</p>
<p>The remote stub may send notification <var>name</var>:<var>event</var>
at any time, but <small>GDB</small> acknowledges the notification when
appropriate.  The notification event is pending before <small>GDB</small>
acknowledges.  Only one notification at a time may be pending; if
additional events occur before <small>GDB</small> has acknowledged the
previous notification, they must be queued by the stub for later
synchronous transmission in response to <var>ack</var> packets from
<small>GDB</small>.  Because the notification mechanism is unreliable,
the stub is permitted to resend a notification if it believes
<small>GDB</small> may not have received it.
</p>
<p>Specifically, notifications may appear when <small>GDB</small> is not
otherwise reading input from the stub, or when <small>GDB</small> is
expecting to read a normal synchronous response or a
&lsquo;<samp>+</samp>&rsquo;/&lsquo;<samp>-</samp>&rsquo; acknowledgment to a packet it has sent.
Notification packets are distinct from any other communication from
the stub so there is no ambiguity.
</p>
<p>After receiving a notification, <small>GDB</small> shall acknowledge it by
sending a <var>ack</var> packet as a regular, synchronous request to the
stub.  Such acknowledgment is not required to happen immediately, as
<small>GDB</small> is permitted to send other, unrelated packets to the
stub first, which the stub should process normally.
</p>
<p>Upon receiving a <var>ack</var> packet, if the stub has other queued
events to report to <small>GDB</small>, it shall respond by sending a
normal <var>event</var>.  <small>GDB</small> shall then send another <var>ack</var>
packet to solicit further responses; again, it is permitted to send
other, unrelated packets as well which the stub should process
normally.
</p>
<p>If the stub receives a <var>ack</var> packet and there are no additional
<var>event</var> to report, the stub shall return an &lsquo;<samp>OK</samp>&rsquo; response.
At this point, <small>GDB</small> has finished processing a notification
and the stub has completed sending any queued events.  <small>GDB</small>
won&rsquo;t accept any new notifications until the final &lsquo;<samp>OK</samp>&rsquo; is
received .  If further notification events occur, the stub shall send
a new notification, <small>GDB</small> shall accept the notification, and
the process shall be repeated.
</p>
<p>The process of asynchronous notification can be illustrated by the
following example:
</p><div class="smallexample">
<pre class="smallexample">&lt;- <code>%Stop:T0505:98e7ffbf;04:4ce6ffbf;08:b1b6e54c;thread:p7526.7526;core:0;</code>
<code>...</code>
-&gt; <code>vStopped</code>
&lt;- <code>T0505:68f37db7;04:40f37db7;08:63850408;thread:p7526.7528;core:0;</code>
-&gt; <code>vStopped</code>
&lt;- <code>T0505:68e3fdb6;04:40e3fdb6;08:63850408;thread:p7526.7529;core:0;</code>
-&gt; <code>vStopped</code>
&lt;- <code>OK</code>
</pre></div>

<p>The following notifications are defined:
</p><table>
<tr><td width="12%">Notification</td><td width="12%">Ack</td><td width="38%">Event</td><td width="38%">Description</td></tr>
<tr><td width="12%">Stop</td><td width="12%">vStopped</td><td width="38%"><var>reply</var>.  The <var>reply</var> has the form of a stop reply, as
described in <a href="Stop-Reply-Packets.html#Stop-Reply-Packets">Stop Reply Packets</a>.  Refer to <a href="Remote-Non_002dStop.html#Remote-Non_002dStop">Remote Non-Stop</a>,
for information on how these notifications are acknowledged by 
<small>GDB</small>.</td><td width="38%">Report an asynchronous stop event in non-stop mode.</td></tr>
</table>

<hr>
<div class="header">
<p>
Next: <a href="Remote-Non_002dStop.html#Remote-Non_002dStop" accesskey="n" rel="next">Remote Non-Stop</a>, Previous: <a href="Interrupts.html#Interrupts" accesskey="p" rel="previous">Interrupts</a>, Up: <a href="Remote-Protocol.html#Remote-Protocol" accesskey="u" rel="up">Remote Protocol</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
