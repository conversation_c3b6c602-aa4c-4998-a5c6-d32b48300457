<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: tdump</title>

<meta name="description" content="Debugging with GDB: tdump">
<meta name="keywords" content="Debugging with GDB: tdump">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Analyze-Collected-Data.html#Analyze-Collected-Data" rel="up" title="Analyze Collected Data">
<link href="save-tracepoints.html#save-tracepoints" rel="next" title="save tracepoints">
<link href="tfind.html#tfind" rel="previous" title="tfind">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="tdump"></a>
<div class="header">
<p>
Next: <a href="save-tracepoints.html#save-tracepoints" accesskey="n" rel="next">save tracepoints</a>, Previous: <a href="tfind.html#tfind" accesskey="p" rel="previous">tfind</a>, Up: <a href="Analyze-Collected-Data.html#Analyze-Collected-Data" accesskey="u" rel="up">Analyze Collected Data</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="tdump-1"></a>
<h4 class="subsection">13.2.2 <code>tdump</code></h4>
<a name="index-tdump"></a>
<a name="index-dump-all-data-collected-at-tracepoint"></a>
<a name="index-tracepoint-data_002c-display"></a>

<p>This command takes no arguments.  It prints all the data collected at
the current trace snapshot.
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) <b>trace 444</b>
(gdb) <b>actions</b>
Enter actions for tracepoint #2, one per line:
&gt; collect $regs, $locals, $args, gdb_long_test
&gt; end

(gdb) <b>tstart</b>

(gdb) <b>tfind line 444</b>
#0  gdb_test (p1=0x11, p2=0x22, p3=0x33, p4=0x44, p5=0x55, p6=0x66)
at gdb_test.c:444
444        printp( &quot;%s: arguments = 0x%X 0x%X 0x%X 0x%X 0x%X 0x%X\n&quot;, )

(gdb) <b>tdump</b>
Data collected at tracepoint 2, trace frame 1:
d0             0xc4aa0085       -995491707
d1             0x18     24
d2             0x80     128
d3             0x33     51
d4             0x71aea3d        119204413
d5             0x22     34
d6             0xe0     224
d7             0x380035 3670069
a0             0x19e24a 1696330
a1             0x3000668        50333288
a2             0x100    256
a3             0x322000 3284992
a4             0x3000698        50333336
a5             0x1ad3cc 1758156
fp             0x30bf3c 0x30bf3c
sp             0x30bf34 0x30bf34
ps             0x0      0
pc             0x20b2c8 0x20b2c8
fpcontrol      0x0      0
fpstatus       0x0      0
fpiaddr        0x0      0
p = 0x20e5b4 &quot;gdb-test&quot;
p1 = (void *) 0x11
p2 = (void *) 0x22
p3 = (void *) 0x33
p4 = (void *) 0x44
p5 = (void *) 0x55
p6 = (void *) 0x66
gdb_long_test = 17 '\021'

(gdb)
</pre></div>

<p><code>tdump</code> works by scanning the tracepoint&rsquo;s current collection
actions and printing the value of each expression listed.  So
<code>tdump</code> can fail, if after a run, you change the tracepoint&rsquo;s
actions to mention variables that were not collected during the run.
</p>
<p>Also, for tracepoints with <code>while-stepping</code> loops, <code>tdump</code>
uses the collected value of <code>$pc</code> to distinguish between trace
frames that were collected at the tracepoint hit, and frames that were
collected while stepping.  This allows it to correctly choose whether
to display the basic list of collections, or the collections from the
body of the while-stepping loop.  However, if <code>$pc</code> was not collected,
then <code>tdump</code> will always attempt to dump using the basic collection
list, and may fail if a while-stepping frame does not include all the
same data that is collected at the tracepoint hit.
</p>
<hr>
<div class="header">
<p>
Next: <a href="save-tracepoints.html#save-tracepoints" accesskey="n" rel="next">save tracepoints</a>, Previous: <a href="tfind.html#tfind" accesskey="p" rel="previous">tfind</a>, Up: <a href="Analyze-Collected-Data.html#Analyze-Collected-Data" accesskey="u" rel="up">Analyze Collected Data</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
