<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Messages/Warnings</title>

<meta name="description" content="Debugging with GDB: Messages/Warnings">
<meta name="keywords" content="Debugging with GDB: Messages/Warnings">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Controlling-GDB.html#Controlling-GDB" rel="up" title="Controlling GDB">
<link href="Debugging-Output.html#Debugging-Output" rel="next" title="Debugging Output">
<link href="Auto_002dloading-verbose-mode.html#Auto_002dloading-verbose-mode" rel="previous" title="Auto-loading verbose mode">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Messages_002fWarnings"></a>
<div class="header">
<p>
Next: <a href="Debugging-Output.html#Debugging-Output" accesskey="n" rel="next">Debugging Output</a>, Previous: <a href="Auto_002dloading.html#Auto_002dloading" accesskey="p" rel="previous">Auto-loading</a>, Up: <a href="Controlling-GDB.html#Controlling-GDB" accesskey="u" rel="up">Controlling GDB</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Optional-Warnings-and-Messages"></a>
<h3 class="section">22.9 Optional Warnings and Messages</h3>

<a name="index-verbose-operation"></a>
<a name="index-optional-warnings"></a>
<p>By default, <small>GDB</small> is silent about its inner workings.  If you are
running on a slow machine, you may want to use the <code>set verbose</code>
command.  This makes <small>GDB</small> tell you when it does a lengthy
internal operation, so you will not think it has crashed.
</p>
<p>Currently, the messages controlled by <code>set verbose</code> are those
which announce that the symbol table for a source file is being read;
see <code>symbol-file</code> in <a href="Files.html#Files">Commands to Specify Files</a>.
</p>
<dl compact="compact">
<dd><a name="index-set-verbose"></a>
</dd>
<dt><code>set verbose on</code></dt>
<dd><p>Enables <small>GDB</small> output of certain informational messages.
</p>
</dd>
<dt><code>set verbose off</code></dt>
<dd><p>Disables <small>GDB</small> output of certain informational messages.
</p>
<a name="index-show-verbose"></a>
</dd>
<dt><code>show verbose</code></dt>
<dd><p>Displays whether <code>set verbose</code> is on or off.
</p></dd>
</dl>

<p>By default, if <small>GDB</small> encounters bugs in the symbol table of an
object file, it is silent; but if you are debugging a compiler, you may
find this information useful (see <a href="Symbol-Errors.html#Symbol-Errors">Errors Reading
Symbol Files</a>).
</p>
<dl compact="compact">
<dd>
<a name="index-set-complaints"></a>
</dd>
<dt><code>set complaints <var>limit</var></code></dt>
<dd><p>Permits <small>GDB</small> to output <var>limit</var> complaints about each type of
unusual symbols before becoming silent about the problem.  Set
<var>limit</var> to zero to suppress all complaints; set it to a large number
to prevent complaints from being suppressed.
</p>
<a name="index-show-complaints"></a>
</dd>
<dt><code>show complaints</code></dt>
<dd><p>Displays how many symbol complaints <small>GDB</small> is permitted to produce.
</p>
</dd>
</dl>

<a name="confirmation-requests"></a><p>By default, <small>GDB</small> is cautious, and asks what sometimes seems to be a
lot of stupid questions to confirm certain commands.  For example, if
you try to run a program which is already running:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) run
The program being debugged has been started already.
Start it from the beginning? (y or n)
</pre></div>

<p>If you are willing to unflinchingly face the consequences of your own
commands, you can disable this &ldquo;feature&rdquo;:
</p>
<dl compact="compact">
<dd>
<a name="index-set-confirm"></a>
<a name="index-flinching"></a>
<a name="index-confirmation"></a>
<a name="index-stupid-questions"></a>
</dd>
<dt><code>set confirm off</code></dt>
<dd><p>Disables confirmation requests.  Note that running <small>GDB</small> with
the <samp>--batch</samp> option (see <a href="Mode-Options.html#Mode-Options">-batch</a>) also
automatically disables confirmation requests.
</p>
</dd>
<dt><code>set confirm on</code></dt>
<dd><p>Enables confirmation requests (the default).
</p>
<a name="index-show-confirm"></a>
</dd>
<dt><code>show confirm</code></dt>
<dd><p>Displays state of confirmation requests.
</p>
</dd>
</dl>

<a name="index-command-tracing"></a>
<p>If you need to debug user-defined commands or sourced files you may find it
useful to enable <em>command tracing</em>.  In this mode each command will be
printed as it is executed, prefixed with one or more &lsquo;<samp>+</samp>&rsquo; symbols, the
quantity denoting the call depth of each command.
</p>
<dl compact="compact">
<dd><a name="index-set-trace_002dcommands"></a>
<a name="index-command-scripts_002c-debugging"></a>
</dd>
<dt><code>set trace-commands on</code></dt>
<dd><p>Enable command tracing.
</p></dd>
<dt><code>set trace-commands off</code></dt>
<dd><p>Disable command tracing.
</p></dd>
<dt><code>show trace-commands</code></dt>
<dd><p>Display the current state of command tracing.
</p></dd>
</dl>

<hr>
<div class="header">
<p>
Next: <a href="Debugging-Output.html#Debugging-Output" accesskey="n" rel="next">Debugging Output</a>, Previous: <a href="Auto_002dloading.html#Auto_002dloading" accesskey="p" rel="previous">Auto-loading</a>, Up: <a href="Controlling-GDB.html#Controlling-GDB" accesskey="u" rel="up">Controlling GDB</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
