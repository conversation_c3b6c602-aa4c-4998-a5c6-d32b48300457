<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Tracepoint Restrictions</title>

<meta name="description" content="Debugging with GDB: Tracepoint Restrictions">
<meta name="keywords" content="Debugging with GDB: Tracepoint Restrictions">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Set-Tracepoints.html#Set-Tracepoints" rel="up" title="Set Tracepoints">
<link href="Analyze-Collected-Data.html#Analyze-Collected-Data" rel="next" title="Analyze Collected Data">
<link href="Starting-and-Stopping-Trace-Experiments.html#Starting-and-Stopping-Trace-Experiments" rel="previous" title="Starting and Stopping Trace Experiments">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Tracepoint-Restrictions"></a>
<div class="header">
<p>
Previous: <a href="Starting-and-Stopping-Trace-Experiments.html#Starting-and-Stopping-Trace-Experiments" accesskey="p" rel="previous">Starting and Stopping Trace Experiments</a>, Up: <a href="Set-Tracepoints.html#Set-Tracepoints" accesskey="u" rel="up">Set Tracepoints</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Tracepoint-Restrictions-1"></a>
<h4 class="subsection">13.1.10 Tracepoint Restrictions</h4>

<a name="index-tracepoint-restrictions"></a>
<p>There are a number of restrictions on the use of tracepoints.  As
described above, tracepoint data gathering occurs on the target
without interaction from <small>GDB</small>.  Thus the full capabilities of
the debugger are not available during data gathering, and then at data
examination time, you will be limited by only having what was
collected.  The following items describe some common problems, but it
is not exhaustive, and you may run into additional difficulties not
mentioned here.
</p>
<ul>
<li> Tracepoint expressions are intended to gather objects (lvalues).  Thus
the full flexibility of GDB&rsquo;s expression evaluator is not available.
You cannot call functions, cast objects to aggregate types, access
convenience variables or modify values (except by assignment to trace
state variables).  Some language features may implicitly call
functions (for instance Objective-C fields with accessors), and therefore
cannot be collected either.

</li><li> Collection of local variables, either individually or in bulk with
<code>$locals</code> or <code>$args</code>, during <code>while-stepping</code> may
behave erratically.  The stepping action may enter a new scope (for
instance by stepping into a function), or the location of the variable
may change (for instance it is loaded into a register).  The
tracepoint data recorded uses the location information for the
variables that is correct for the tracepoint location.  When the
tracepoint is created, it is not possible, in general, to determine
where the steps of a <code>while-stepping</code> sequence will advance the
program&mdash;particularly if a conditional branch is stepped.

</li><li> Collection of an incompletely-initialized or partially-destroyed object
may result in something that <small>GDB</small> cannot display, or displays
in a misleading way.

</li><li> When <small>GDB</small> displays a pointer to character it automatically
dereferences the pointer to also display characters of the string
being pointed to.  However, collecting the pointer during tracing does
not automatically collect the string.  You need to explicitly
dereference the pointer and provide size information if you want to
collect not only the pointer, but the memory pointed to.  For example,
<code>*ptr@50</code> can be used to collect the 50 element array pointed to
by <code>ptr</code>.

</li><li> It is not possible to collect a complete stack backtrace at a
tracepoint.  Instead, you may collect the registers and a few hundred
bytes from the stack pointer with something like <code>*(unsigned char *)$esp@300</code>
(adjust to use the name of the actual stack pointer register on your
target architecture, and the amount of stack you wish to capture).
Then the <code>backtrace</code> command will show a partial backtrace when
using a trace frame.  The number of stack frames that can be examined
depends on the sizes of the frames in the collected stack.  Note that
if you ask for a block so large that it goes past the bottom of the
stack, the target agent may report an error trying to read from an
invalid address.

</li><li> If you do not collect registers at a tracepoint, <small>GDB</small> can
infer that the value of <code>$pc</code> must be the same as the address of
the tracepoint and use that when you are looking at a trace frame
for that tracepoint.  However, this cannot work if the tracepoint has
multiple locations (for instance if it was set in a function that was
inlined), or if it has a <code>while-stepping</code> loop.  In those cases
<small>GDB</small> will warn you that it can&rsquo;t infer <code>$pc</code>, and default
it to zero.

</li></ul>

<hr>
<div class="header">
<p>
Previous: <a href="Starting-and-Stopping-Trace-Experiments.html#Starting-and-Stopping-Trace-Experiments" accesskey="p" rel="previous">Starting and Stopping Trace Experiments</a>, Up: <a href="Set-Tracepoints.html#Set-Tracepoints" accesskey="u" rel="up">Set Tracepoints</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
