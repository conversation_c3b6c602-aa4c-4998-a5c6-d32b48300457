<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: The F Reply Packet</title>

<meta name="description" content="Debugging with GDB: The F Reply Packet">
<meta name="keywords" content="Debugging with GDB: The F Reply Packet">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="File_002dI_002fO-Remote-Protocol-Extension.html#File_002dI_002fO-Remote-Protocol-Extension" rel="up" title="File-I/O Remote Protocol Extension">
<link href="The-Ctrl_002dC-Message.html#The-Ctrl_002dC-Message" rel="next" title="The Ctrl-C Message">
<link href="The-F-Request-Packet.html#The-F-Request-Packet" rel="previous" title="The F Request Packet">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="The-F-Reply-Packet"></a>
<div class="header">
<p>
Next: <a href="The-Ctrl_002dC-Message.html#The-Ctrl_002dC-Message" accesskey="n" rel="next">The Ctrl-C Message</a>, Previous: <a href="The-F-Request-Packet.html#The-F-Request-Packet" accesskey="p" rel="previous">The F Request Packet</a>, Up: <a href="File_002dI_002fO-Remote-Protocol-Extension.html#File_002dI_002fO-Remote-Protocol-Extension" accesskey="u" rel="up">File-I/O Remote Protocol Extension</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="The-F-Reply-Packet-1"></a>
<h4 class="subsection">E.13.4 The <code>F</code> Reply Packet</h4>
<a name="index-file_002di_002fo-reply-packet"></a>
<a name="index-F-reply-packet"></a>

<p>The <code>F</code> reply packet has the following format:
</p>
<dl compact="compact">
<dt>&lsquo;<samp>F<var>retcode</var>,<var>errno</var>,<var>Ctrl-C flag</var>;<var>call-specific attachment</var></samp>&rsquo;</dt>
<dd>
<p><var>retcode</var> is the return code of the system call as hexadecimal value.
</p>
<p><var>errno</var> is the <code>errno</code> set by the call, in protocol-specific
representation.
This parameter can be omitted if the call was successful.
</p>
<p><var>Ctrl-C flag</var> is only sent if the user requested a break.  In this
case, <var>errno</var> must be sent as well, even if the call was successful.
The <var>Ctrl-C flag</var> itself consists of the character &lsquo;<samp>C</samp>&rsquo;:
</p>
<div class="smallexample">
<pre class="smallexample">F0,0,C
</pre></div>

<p>or, if the call was interrupted before the host call has been performed:
</p>
<div class="smallexample">
<pre class="smallexample">F-1,4,C
</pre></div>

<p>assuming 4 is the protocol-specific representation of <code>EINTR</code>.
</p>
</dd>
</dl>





</body>
</html>
