<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: gdbserver man</title>

<meta name="description" content="Debugging with GDB: gdbserver man">
<meta name="keywords" content="Debugging with GDB: gdbserver man">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Man-Pages.html#Man-Pages" rel="up" title="Man Pages">
<link href="gcore-man.html#gcore-man" rel="next" title="gcore man">
<link href="gdb-man.html#gdb-man" rel="previous" title="gdb man">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="gdbserver-man"></a>
<div class="header">
<p>
Next: <a href="gcore-man.html#gcore-man" accesskey="n" rel="next">gcore man</a>, Previous: <a href="gdb-man.html#gdb-man" accesskey="p" rel="previous">gdb man</a>, Up: <a href="Man-Pages.html#Man-Pages" accesskey="u" rel="up">Man Pages</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<h4 class="node-heading">gdbserver man</h4>
<a name="gdbserver-man-1"></a>
<h3 class="heading">gdbserver man</h3>

<div class="format">
<pre class="format">gdbserver <var>comm</var> <var>prog</var> [<var>args</var>&hellip;]

gdbserver &ndash;attach <var>comm</var> <var>pid</var>

gdbserver &ndash;multi <var>comm</var>
</pre></div>

<p><code>gdbserver</code> is a program that allows you to run <small>GDB</small> on a different machine
than the one which is running the program being debugged.
</p>
<a name="Usage-_0028server-_0028target_0029-side_0029"></a>
<h4 class="subheading">Usage (server (target) side)</h4>

<p>First, you need to have a copy of the program you want to debug put onto
the target system.  The program can be stripped to save space if needed, as
<code>gdbserver</code> doesn&rsquo;t care about symbols.  All symbol handling is taken care of by
the <small>GDB</small> running on the host system.
</p>
<p>To use the server, you log on to the target system, and run the <code>gdbserver</code>
program.  You must tell it (a) how to communicate with <small>GDB</small>, (b) the name of
your program, and (c) its arguments.  The general syntax is:
</p>
<div class="smallexample">
<pre class="smallexample">target&gt; gdbserver <var>comm</var> <var>program</var> [<var>args</var> ...]
</pre></div>

<p>For example, using a serial port, you might say:
</p>
<div class="smallexample">
<pre class="smallexample">target&gt; gdbserver <samp>/dev/com1</samp> emacs foo.txt
</pre></div>

<p>This tells <code>gdbserver</code> to debug emacs with an argument of foo.txt, and
to communicate with <small>GDB</small> via <samp>/dev/com1</samp>.  <code>gdbserver</code> now
waits patiently for the host <small>GDB</small> to communicate with it.
</p>
<p>To use a TCP connection, you could say:
</p>
<div class="smallexample">
<pre class="smallexample">target&gt; gdbserver host:2345 emacs foo.txt
</pre></div>

<p>This says pretty much the same thing as the last example, except that we are
going to communicate with the <code>host</code> <small>GDB</small> via TCP.  The <code>host:2345</code> argument means
that we are expecting to see a TCP connection from <code>host</code> to local TCP port
2345.  (Currently, the <code>host</code> part is ignored.)  You can choose any number you
want for the port number as long as it does not conflict with any existing TCP
ports on the target system.  This same port number must be used in the host
<small>GDB</small>s <code>target remote</code> command, which will be described shortly.  Note that if
you chose a port number that conflicts with another service, <code>gdbserver</code> will
print an error message and exit.
</p>
<p><code>gdbserver</code> can also attach to running programs.
This is accomplished via the <samp>--attach</samp> argument.  The syntax is:
</p>
<div class="smallexample">
<pre class="smallexample">target&gt; gdbserver --attach <var>comm</var> <var>pid</var>
</pre></div>

<p><var>pid</var> is the process ID of a currently running process.  It isn&rsquo;t
necessary to point <code>gdbserver</code> at a binary for the running process.
</p>
<p>To start <code>gdbserver</code> without supplying an initial command to run
or process ID to attach, use the <samp>--multi</samp> command line option.
In such case you should connect using <kbd>target extended-remote</kbd> to start
the program you want to debug.
</p>
<div class="smallexample">
<pre class="smallexample">target&gt; gdbserver --multi <var>comm</var>
</pre></div>

<a name="Usage-_0028host-side_0029"></a>
<h4 class="subheading">Usage (host side)</h4>

<p>You need an unstripped copy of the target program on your host system, since
<small>GDB</small> needs to examine its symbol tables and such.  Start up <small>GDB</small> as you normally
would, with the target program as the first argument.  (You may need to use the
<samp>--baud</samp> option if the serial line is running at anything except 9600 baud.)
That is <code>gdb TARGET-PROG</code>, or <code>gdb --baud BAUD TARGET-PROG</code>.  After that, the only
new command you need to know about is <code>target remote</code>
(or <code>target extended-remote</code>).  Its argument is either
a device name (usually a serial device, like <samp>/dev/ttyb</samp>), or a <code>HOST:PORT</code>
descriptor.  For example:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) target remote <samp>/dev/ttyb</samp>
</pre></div>

<p>communicates with the server via serial line <samp>/dev/ttyb</samp>, and:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) target remote the-target:2345
</pre></div>

<p>communicates via a TCP connection to port 2345 on host &lsquo;the-target&rsquo;, where
you previously started up <code>gdbserver</code> with the same port number.  Note that for
TCP connections, you must start up <code>gdbserver</code> prior to using the &lsquo;target remote&rsquo;
command, otherwise you may get an error that looks something like
&lsquo;Connection refused&rsquo;.
</p>
<p><code>gdbserver</code> can also debug multiple inferiors at once,
described in
<a href="Inferiors-Connections-and-Programs.html#Inferiors-Connections-and-Programs">Inferiors Connections and Programs</a>.
In such case use the <code>extended-remote</code> <small>GDB</small> command variant:
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) target extended-remote the-target:2345
</pre></div>

<p>The <code>gdbserver</code> option <samp>--multi</samp> may or may not be used in such
case.
</p>
<p>There are three different modes for invoking <code>gdbserver</code>:
</p>
<ul>
<li> Debug a specific program specified by its program name:

<div class="smallexample">
<pre class="smallexample">gdbserver <var>comm</var> <var>prog</var> [<var>args</var>&hellip;]
</pre></div>

<p>The <var>comm</var> parameter specifies how should the server communicate
with <small>GDB</small>; it is either a device name (to use a serial line),
a TCP port number (<code>:1234</code>), or <code>-</code> or <code>stdio</code> to use
stdin/stdout of <code>gdbserver</code>.  Specify the name of the program to
debug in <var>prog</var>.  Any remaining arguments will be passed to the
program verbatim.  When the program exits, <small>GDB</small> will close the
connection, and <code>gdbserver</code> will exit.
</p>
</li><li> Debug a specific program by specifying the process ID of a running
program:

<div class="smallexample">
<pre class="smallexample">gdbserver --attach <var>comm</var> <var>pid</var>
</pre></div>

<p>The <var>comm</var> parameter is as described above.  Supply the process ID
of a running program in <var>pid</var>; <small>GDB</small> will do everything
else.  Like with the previous mode, when the process <var>pid</var> exits,
<small>GDB</small> will close the connection, and <code>gdbserver</code> will exit.
</p>
</li><li> Multi-process mode &ndash; debug more than one program/process:

<div class="smallexample">
<pre class="smallexample">gdbserver --multi <var>comm</var>
</pre></div>

<p>In this mode, <small>GDB</small> can instruct <code>gdbserver</code> which
command(s) to run.  Unlike the other 2 modes, <small>GDB</small> will not
close the connection when a process being debugged exits, so you can
debug several processes in the same session.
</p></li></ul>

<p>In each of the modes you may specify these options:
</p>
<dl compact="compact">
<dt><code>--help</code></dt>
<dd><p>List all options, with brief explanations.
</p>
</dd>
<dt><code>--version</code></dt>
<dd><p>This option causes <code>gdbserver</code> to print its version number and exit.
</p>
</dd>
<dt><code>--attach</code></dt>
<dd><p><code>gdbserver</code> will attach to a running program.  The syntax is:
</p>
<div class="smallexample">
<pre class="smallexample">target&gt; gdbserver --attach <var>comm</var> <var>pid</var>
</pre></div>

<p><var>pid</var> is the process ID of a currently running process.  It isn&rsquo;t
necessary to point <code>gdbserver</code> at a binary for the running process.
</p>
</dd>
<dt><code>--multi</code></dt>
<dd><p>To start <code>gdbserver</code> without supplying an initial command to run
or process ID to attach, use this command line option.
Then you can connect using <kbd>target extended-remote</kbd> and start
the program you want to debug.  The syntax is:
</p>
<div class="smallexample">
<pre class="smallexample">target&gt; gdbserver --multi <var>comm</var>
</pre></div>

</dd>
<dt><code>--debug</code></dt>
<dd><p>Instruct <code>gdbserver</code> to display extra status information about the debugging
process.
This option is intended for <code>gdbserver</code> development and for bug reports to
the developers.
</p>
</dd>
<dt><code>--remote-debug</code></dt>
<dd><p>Instruct <code>gdbserver</code> to display remote protocol debug output.
This option is intended for <code>gdbserver</code> development and for bug reports to
the developers.
</p>
</dd>
<dt><code>--debug-file=<var>filename</var></code></dt>
<dd><p>Instruct <code>gdbserver</code> to send any debug output to the given <var>filename</var>.
This option is intended for <code>gdbserver</code> development and for bug reports to
the developers.
</p>
</dd>
<dt><code>--debug-format=option1<span class="roman">[</span>,option2,...<span class="roman">]</span></code></dt>
<dd><p>Instruct <code>gdbserver</code> to include extra information in each line
of debugging output.
See <a href="Server.html#Other-Command_002dLine-Arguments-for-gdbserver">Other Command-Line Arguments for gdbserver</a>.
</p>
</dd>
<dt><code>--wrapper</code></dt>
<dd><p>Specify a wrapper to launch programs
for debugging.  The option should be followed by the name of the
wrapper, then any command-line arguments to pass to the wrapper, then
<kbd>--</kbd> indicating the end of the wrapper arguments.
</p>
</dd>
<dt><code>--once</code></dt>
<dd><p>By default, <code>gdbserver</code> keeps the listening TCP port open, so that
additional connections are possible.  However, if you start <code>gdbserver</code>
with the <samp>--once</samp> option, it will stop listening for any further
connection attempts after connecting to the first <small>GDB</small> session.
</p>


</dd>
</dl>


<hr>
<div class="header">
<p>
Next: <a href="gcore-man.html#gcore-man" accesskey="n" rel="next">gcore man</a>, Previous: <a href="gdb-man.html#gdb-man" accesskey="p" rel="previous">gdb man</a>, Up: <a href="Man-Pages.html#Man-Pages" accesskey="u" rel="up">Man Pages</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
