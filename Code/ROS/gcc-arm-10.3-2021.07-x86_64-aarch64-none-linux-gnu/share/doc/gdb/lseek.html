<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: lseek</title>

<meta name="description" content="Debugging with GDB: lseek">
<meta name="keywords" content="Debugging with GDB: lseek">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="List-of-Supported-Calls.html#List-of-Supported-Calls" rel="up" title="List of Supported Calls">
<link href="rename.html#rename" rel="next" title="rename">
<link href="write.html#write" rel="previous" title="write">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="lseek"></a>
<div class="header">
<p>
Next: <a href="rename.html#rename" accesskey="n" rel="next">rename</a>, Previous: <a href="write.html#write" accesskey="p" rel="previous">write</a>, Up: <a href="List-of-Supported-Calls.html#List-of-Supported-Calls" accesskey="u" rel="up">List of Supported Calls</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="lseek-1"></a>
<h4 class="unnumberedsubsubsec">lseek</h4>
<a name="index-lseek_002c-file_002di_002fo-system-call"></a>

<dl compact="compact">
<dt>Synopsis:</dt>
<dd><div class="smallexample">
<pre class="smallexample">long lseek (int fd, long offset, int flag);
</pre></div>

</dd>
<dt>Request:</dt>
<dd><p>&lsquo;<samp>Flseek,<var>fd</var>,<var>offset</var>,<var>flag</var></samp>&rsquo;
</p>
<p><var>flag</var> is one of:
</p>
<dl compact="compact">
<dt><code>SEEK_SET</code></dt>
<dd><p>The offset is set to <var>offset</var> bytes.
</p>
</dd>
<dt><code>SEEK_CUR</code></dt>
<dd><p>The offset is set to its current location plus <var>offset</var>
bytes.
</p>
</dd>
<dt><code>SEEK_END</code></dt>
<dd><p>The offset is set to the size of the file plus <var>offset</var>
bytes.
</p></dd>
</dl>

</dd>
<dt>Return value:</dt>
<dd><p>On success, the resulting unsigned offset in bytes from
the beginning of the file is returned.  Otherwise, a
value of -1 is returned.
</p>
</dd>
<dt>Errors:</dt>
<dd>
<dl compact="compact">
<dt><code>EBADF</code></dt>
<dd><p><var>fd</var> is not a valid open file descriptor.
</p>
</dd>
<dt><code>ESPIPE</code></dt>
<dd><p><var>fd</var> is associated with the <small>GDB</small> console.
</p>
</dd>
<dt><code>EINVAL</code></dt>
<dd><p><var>flag</var> is not a proper value.
</p>
</dd>
<dt><code>EINTR</code></dt>
<dd><p>The call was interrupted by the user.
</p></dd>
</dl>

</dd>
</dl>




</body>
</html>
