<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Type Checking</title>

<meta name="description" content="Debugging with GDB: Type Checking">
<meta name="keywords" content="Debugging with GDB: Type Checking">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Checks.html#Checks" rel="up" title="Checks">
<link href="Range-Checking.html#Range-Checking" rel="next" title="Range Checking">
<link href="Checks.html#Checks" rel="previous" title="Checks">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Type-Checking"></a>
<div class="header">
<p>
Next: <a href="Range-Checking.html#Range-Checking" accesskey="n" rel="next">Range Checking</a>, Up: <a href="Checks.html#Checks" accesskey="u" rel="up">Checks</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="An-Overview-of-Type-Checking"></a>
<h4 class="subsection">15.3.1 An Overview of Type Checking</h4>

<p>Some languages, such as C and C<tt>++</tt>, are strongly typed, meaning that the
arguments to operators and functions have to be of the correct type,
otherwise an error occurs.  These checks prevent type mismatch
errors from ever causing any run-time problems.  For example,
</p>
<div class="smallexample">
<pre class="smallexample">int klass::my_method(char *b) { return  b ? 1 : 2; }

(gdb) print obj.my_method (0)
$1 = 2
</pre><pre class="smallexample">but
</pre><pre class="smallexample">(gdb) print obj.my_method (0x1234)
Cannot resolve method klass::my_method to any overloaded instance
</pre></div>

<p>The second example fails because in C<tt>++</tt> the integer constant
&lsquo;<samp>0x1234</samp>&rsquo; is not type-compatible with the pointer parameter type.
</p>
<p>For the expressions you use in <small>GDB</small> commands, you can tell
<small>GDB</small> to not enforce strict type checking or
to treat any mismatches as errors and abandon the expression;
When type checking is disabled, <small>GDB</small> successfully evaluates
expressions like the second example above.
</p>
<p>Even if type checking is off, there may be other reasons
related to type that prevent <small>GDB</small> from evaluating an expression.
For instance, <small>GDB</small> does not know how to add an <code>int</code> and
a <code>struct foo</code>.  These particular type errors have nothing to do
with the language in use and usually arise from expressions which make
little sense to evaluate anyway.
</p>
<p><small>GDB</small> provides some additional commands for controlling type checking:
</p>
<a name="index-set-check-type"></a>
<a name="index-show-check-type"></a>
<dl compact="compact">
<dt><code>set check type on</code></dt>
<dt><code>set check type off</code></dt>
<dd><p>Set strict type checking on or off.  If any type mismatches occur in
evaluating an expression while type checking is on, <small>GDB</small> prints a
message and aborts evaluation of the expression.
</p>
</dd>
<dt><code>show check type</code></dt>
<dd><p>Show the current setting of type checking and whether <small>GDB</small>
is enforcing strict type checking rules.
</p></dd>
</dl>

<a name="index-range-checking"></a>
<a name="index-checks_002c-range"></a>
<hr>
<div class="header">
<p>
Next: <a href="Range-Checking.html#Range-Checking" accesskey="n" rel="next">Range Checking</a>, Up: <a href="Checks.html#Checks" accesskey="u" rel="up">Checks</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
