<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: TUI Windows In Python</title>

<meta name="description" content="Debugging with GDB: TUI Windows In Python">
<meta name="keywords" content="Debugging with GDB: TUI Windows In Python">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Python-API.html#Python-API" rel="up" title="Python API">
<link href="Python-Auto_002dloading.html#Python-Auto_002dloading" rel="next" title="Python Auto-loading">
<link href="Registers-In-Python.html#Registers-In-Python" rel="previous" title="Registers In Python">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="TUI-Windows-In-Python"></a>
<div class="header">
<p>
Previous: <a href="Registers-In-Python.html#Registers-In-Python" accesskey="p" rel="previous">Registers In Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Implementing-new-TUI-windows"></a>
<h4 class="subsubsection">23.2.2.35 Implementing new TUI windows</h4>
<a name="index-Python-TUI-Windows"></a>

<p>New TUI (see <a href="TUI.html#TUI">TUI</a>) windows can be implemented in Python.
</p>
<a name="index-gdb_002eregister_005fwindow_005ftype"></a>
<dl>
<dt><a name="index-gdb_002eregister_005fwindow_005ftype-1"></a>Function: <strong>gdb.register_window_type</strong> <em>(<var>name</var>, <var>factory</var>)</em></dt>
<dd><p>Because TUI windows are created and destroyed depending on the layout
the user chooses, new window types are implemented by registering a
factory function with <small>GDB</small>.
</p>
<p><var>name</var> is the name of the new window.  It&rsquo;s an error to try to
replace one of the built-in windows, but other window types can be
replaced.
</p>
<p><var>function</var> is a factory function that is called to create the TUI
window.  This is called with a single argument of type
<code>gdb.TuiWindow</code>, described below.  It should return an object
that implements the TUI window protocol, also described below.
</p></dd></dl>

<p>As mentioned above, when a factory function is called, it is passed a
an object of type <code>gdb.TuiWindow</code>.  This object has these
methods and attributes:
</p>
<dl>
<dt><a name="index-TuiWindow_002eis_005fvalid"></a>Function: <strong>TuiWindow.is_valid</strong> <em>()</em></dt>
<dd><p>This method returns <code>True</code> when this window is valid.  When the
user changes the TUI layout, windows no longer visible in the new
layout will be destroyed.  At this point, the <code>gdb.TuiWindow</code>
will no longer be valid, and methods (and attributes) other than
<code>is_valid</code> will throw an exception.
</p></dd></dl>

<dl>
<dt><a name="index-TuiWindow_002ewidth"></a>Variable: <strong>TuiWindow.width</strong></dt>
<dd><p>This attribute holds the width of the window.  It is not writable.
</p></dd></dl>

<dl>
<dt><a name="index-TuiWindow_002eheight"></a>Variable: <strong>TuiWindow.height</strong></dt>
<dd><p>This attribute holds the height of the window.  It is not writable.
</p></dd></dl>

<dl>
<dt><a name="index-TuiWindow_002etitle"></a>Variable: <strong>TuiWindow.title</strong></dt>
<dd><p>This attribute holds the window&rsquo;s title, a string.  This is normally
displayed above the window.  This attribute can be modified.
</p></dd></dl>

<dl>
<dt><a name="index-TuiWindow_002eerase"></a>Function: <strong>TuiWindow.erase</strong> <em>()</em></dt>
<dd><p>Remove all the contents of the window.
</p></dd></dl>

<dl>
<dt><a name="index-TuiWindow_002ewrite"></a>Function: <strong>TuiWindow.write</strong> <em>(<var>string</var>)</em></dt>
<dd><p>Write <var>string</var> to the window.  <var>string</var> can contain ANSI
terminal escape styling sequences; <small>GDB</small> will translate these
as appropriate for the terminal.
</p></dd></dl>

<p>The factory function that you supply should return an object
conforming to the TUI window protocol.  These are the method that can
be called on this object, which is referred to below as the &ldquo;window
object&rdquo;.  The methods documented below are optional; if the object
does not implement one of these methods, <small>GDB</small> will not attempt
to call it.  Additional new methods may be added to the window
protocol in the future.  <small>GDB</small> guarantees that they will begin
with a lower-case letter, so you can start implementation methods with
upper-case letters or underscore to avoid any future conflicts.
</p>
<dl>
<dt><a name="index-Window_002eclose"></a>Function: <strong>Window.close</strong> <em>()</em></dt>
<dd><p>When the TUI window is closed, the <code>gdb.TuiWindow</code> object will be
put into an invalid state.  At this time, <small>GDB</small> will call
<code>close</code> method on the window object.
</p>
<p>After this method is called, <small>GDB</small> will discard any references
it holds on this window object, and will no longer call methods on
this object.
</p></dd></dl>

<dl>
<dt><a name="index-Window_002erender"></a>Function: <strong>Window.render</strong> <em>()</em></dt>
<dd><p>In some situations, a TUI window can change size.  For example, this
can happen if the user resizes the terminal, or changes the layout.
When this happens, <small>GDB</small> will call the <code>render</code> method on
the window object.
</p>
<p>If your window is intended to update in response to changes in the
inferior, you will probably also want to register event listeners and
send output to the <code>gdb.TuiWindow</code>.
</p></dd></dl>

<dl>
<dt><a name="index-Window_002ehscroll"></a>Function: <strong>Window.hscroll</strong> <em>(<var>num</var>)</em></dt>
<dd><p>This is a request to scroll the window horizontally.  <var>num</var> is the
amount by which to scroll, with negative numbers meaning to scroll
right.  In the TUI model, it is the viewport that moves, not the
contents.  A positive argument should cause the viewport to move
right, and so the content should appear to move to the left.
</p></dd></dl>

<dl>
<dt><a name="index-Window_002evscroll"></a>Function: <strong>Window.vscroll</strong> <em>(<var>num</var>)</em></dt>
<dd><p>This is a request to scroll the window vertically.  <var>num</var> is the
amount by which to scroll, with negative numbers meaning to scroll
backward.  In the TUI model, it is the viewport that moves, not the
contents.  A positive argument should cause the viewport to move down,
and so the content should appear to move up.
</p></dd></dl>

<hr>
<div class="header">
<p>
Previous: <a href="Registers-In-Python.html#Registers-In-Python" accesskey="p" rel="previous">Registers In Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
