<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Objfiles In Python</title>

<meta name="description" content="Debugging with GDB: Objfiles In Python">
<meta name="keywords" content="Debugging with GDB: Objfiles In Python">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="Python-API.html#Python-API" rel="up" title="Python API">
<link href="Frames-In-Python.html#Frames-In-Python" rel="next" title="Frames In Python">
<link href="Progspaces-In-Python.html#Progspaces-In-Python" rel="previous" title="Progspaces In Python">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Objfiles-In-Python"></a>
<div class="header">
<p>
Next: <a href="Frames-In-Python.html#Frames-In-Python" accesskey="n" rel="next">Frames In Python</a>, Previous: <a href="Progspaces-In-Python.html#Progspaces-In-Python" accesskey="p" rel="previous">Progspaces In Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Objfiles-In-Python-1"></a>
<h4 class="subsubsection">23.2.2.24 Objfiles In Python</h4>

<a name="index-objfiles-in-python"></a>
<a name="index-gdb_002eObjfile"></a>
<a name="index-Objfile"></a>
<p><small>GDB</small> loads symbols for an inferior from various
symbol-containing files (see <a href="Files.html#Files">Files</a>).  These include the primary
executable file, any shared libraries used by the inferior, and any
separate debug info files (see <a href="Separate-Debug-Files.html#Separate-Debug-Files">Separate Debug Files</a>).
<small>GDB</small> calls these symbol-containing files <em>objfiles</em>.
</p>
<p>The following objfile-related functions are available in the
<code>gdb</code> module:
</p>
<a name="index-gdb_002ecurrent_005fobjfile"></a>
<dl>
<dt><a name="index-gdb_002ecurrent_005fobjfile-1"></a>Function: <strong>gdb.current_objfile</strong> <em>()</em></dt>
<dd><p>When auto-loading a Python script (see <a href="Python-Auto_002dloading.html#Python-Auto_002dloading">Python Auto-loading</a>), <small>GDB</small>
sets the &ldquo;current objfile&rdquo; to the corresponding objfile.  This
function returns the current objfile.  If there is no current objfile,
this function returns <code>None</code>.
</p></dd></dl>

<a name="index-gdb_002eobjfiles"></a>
<dl>
<dt><a name="index-gdb_002eobjfiles-1"></a>Function: <strong>gdb.objfiles</strong> <em>()</em></dt>
<dd><p>Return a sequence of objfiles referenced by the current program space.
See <a href="#Objfiles-In-Python">Objfiles In Python</a>, and <a href="Progspaces-In-Python.html#Progspaces-In-Python">Progspaces In Python</a>.  This is identical
to <code>gdb.selected_inferior().progspace.objfiles()</code> and is included for
historical compatibility.
</p></dd></dl>

<a name="index-gdb_002elookup_005fobjfile"></a>
<dl>
<dt><a name="index-gdb_002elookup_005fobjfile-1"></a>Function: <strong>gdb.lookup_objfile</strong> <em>(name <span class="roman">[</span>, by_build_id])</em></dt>
<dd><p>Look up <var>name</var>, a file name or build ID, in the list of objfiles
for the current program space (see <a href="Progspaces-In-Python.html#Progspaces-In-Python">Progspaces In Python</a>).
If the objfile is not found throw the Python <code>ValueError</code> exception.
</p>
<p>If <var>name</var> is a relative file name, then it will match any
source file name with the same trailing components.  For example, if
<var>name</var> is &lsquo;<samp>gcc/expr.c</samp>&rsquo;, then it will match source file
name of <samp>/build/trunk/gcc/expr.c</samp>, but not
<samp>/build/trunk/libcpp/expr.c</samp> or <samp>/build/trunk/gcc/x-expr.c</samp>.
</p>
<p>If <var>by_build_id</var> is provided and is <code>True</code> then <var>name</var>
is the build ID of the objfile.  Otherwise, <var>name</var> is a file name.
This is supported only on some operating systems, notably those which use
the ELF format for binary files and the <small>GNU</small> Binutils.  For more details
about this feature, see the description of the <samp>--build-id</samp>
command-line option in <a href="http://sourceware.org/binutils/docs/ld/Options.html#Options">Command Line Options</a> in <cite>The GNU Linker</cite>.
</p></dd></dl>

<p>Each objfile is represented by an instance of the <code>gdb.Objfile</code>
class.
</p>
<dl>
<dt><a name="index-Objfile_002efilename"></a>Variable: <strong>Objfile.filename</strong></dt>
<dd><p>The file name of the objfile as a string, with symbolic links resolved.
</p>
<p>The value is <code>None</code> if the objfile is no longer valid.
See the <code>gdb.Objfile.is_valid</code> method, described below.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002eusername"></a>Variable: <strong>Objfile.username</strong></dt>
<dd><p>The file name of the objfile as specified by the user as a string.
</p>
<p>The value is <code>None</code> if the objfile is no longer valid.
See the <code>gdb.Objfile.is_valid</code> method, described below.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002eowner"></a>Variable: <strong>Objfile.owner</strong></dt>
<dd><p>For separate debug info objfiles this is the corresponding <code>gdb.Objfile</code>
object that debug info is being provided for.
Otherwise this is <code>None</code>.
Separate debug info objfiles are added with the
<code>gdb.Objfile.add_separate_debug_file</code> method, described below.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002ebuild_005fid"></a>Variable: <strong>Objfile.build_id</strong></dt>
<dd><p>The build ID of the objfile as a string.
If the objfile does not have a build ID then the value is <code>None</code>.
</p>
<p>This is supported only on some operating systems, notably those which use
the ELF format for binary files and the <small>GNU</small> Binutils.  For more details
about this feature, see the description of the <samp>--build-id</samp>
command-line option in <a href="http://sourceware.org/binutils/docs/ld/Options.html#Options">Command Line Options</a> in <cite>The GNU Linker</cite>.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002eprogspace"></a>Variable: <strong>Objfile.progspace</strong></dt>
<dd><p>The containing program space of the objfile as a <code>gdb.Progspace</code>
object.  See <a href="Progspaces-In-Python.html#Progspaces-In-Python">Progspaces In Python</a>.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002epretty_005fprinters"></a>Variable: <strong>Objfile.pretty_printers</strong></dt>
<dd><p>The <code>pretty_printers</code> attribute is a list of functions.  It is
used to look up pretty-printers.  A <code>Value</code> is passed to each
function in order; if the function returns <code>None</code>, then the
search continues.  Otherwise, the return value should be an object
which is used to format the value.  See <a href="Pretty-Printing-API.html#Pretty-Printing-API">Pretty Printing API</a>, for more
information.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002etype_005fprinters"></a>Variable: <strong>Objfile.type_printers</strong></dt>
<dd><p>The <code>type_printers</code> attribute is a list of type printer objects.
See <a href="Type-Printing-API.html#Type-Printing-API">Type Printing API</a>, for more information.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002eframe_005ffilters"></a>Variable: <strong>Objfile.frame_filters</strong></dt>
<dd><p>The <code>frame_filters</code> attribute is a dictionary of frame filter
objects.  See <a href="Frame-Filter-API.html#Frame-Filter-API">Frame Filter API</a>, for more information.
</p></dd></dl>

<p>One may add arbitrary attributes to <code>gdb.Objfile</code> objects
in the usual Python way.
This is useful if, for example, one needs to do some extra record keeping
associated with the objfile.
</p>
<p>In this contrived example we record the time when <small>GDB</small>
loaded the objfile.
</p>
<div class="smallexample">
<pre class="smallexample">(gdb) python
import datetime
def new_objfile_handler(event):
    # Set the time_loaded attribute of the new objfile.
    event.new_objfile.time_loaded = datetime.datetime.today()
gdb.events.new_objfile.connect(new_objfile_handler)
end
(gdb) file ./hello
Reading symbols from ./hello...
(gdb) python print gdb.objfiles()[0].time_loaded
2014-10-09 11:41:36.770345
</pre></div>

<p>A <code>gdb.Objfile</code> object has the following methods:
</p>
<dl>
<dt><a name="index-Objfile_002eis_005fvalid"></a>Function: <strong>Objfile.is_valid</strong> <em>()</em></dt>
<dd><p>Returns <code>True</code> if the <code>gdb.Objfile</code> object is valid,
<code>False</code> if not.  A <code>gdb.Objfile</code> object can become invalid
if the object file it refers to is not loaded in <small>GDB</small> any
longer.  All other <code>gdb.Objfile</code> methods will throw an exception
if it is invalid at the time the method is called.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002eadd_005fseparate_005fdebug_005ffile"></a>Function: <strong>Objfile.add_separate_debug_file</strong> <em>(file)</em></dt>
<dd><p>Add <var>file</var> to the list of files that <small>GDB</small> will search for
debug information for the objfile.
This is useful when the debug info has been removed from the program
and stored in a separate file.  <small>GDB</small> has built-in support for
finding separate debug info files (see <a href="Separate-Debug-Files.html#Separate-Debug-Files">Separate Debug Files</a>), but if
the file doesn&rsquo;t live in one of the standard places that <small>GDB</small>
searches then this function can be used to add a debug info file
from a different place.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002elookup_005fglobal_005fsymbol"></a>Function: <strong>Objfile.lookup_global_symbol</strong> <em>(name <span class="roman">[</span>, domain<span class="roman">]</span>)</em></dt>
<dd><p>Search for a global symbol named <var>name</var> in this objfile.  Optionally, the
search scope can be restricted with the <var>domain</var> argument.
The <var>domain</var> argument must be a domain constant defined in the <code>gdb</code>
module and described in <a href="Symbols-In-Python.html#Symbols-In-Python">Symbols In Python</a>.  This function is similar to
<code>gdb.lookup_global_symbol</code>, except that the search is limited to this
objfile.
</p>
<p>The result is a <code>gdb.Symbol</code> object or <code>None</code> if the symbol
is not found.
</p></dd></dl>

<dl>
<dt><a name="index-Objfile_002elookup_005fstatic_005fsymbol"></a>Function: <strong>Objfile.lookup_static_symbol</strong> <em>(name <span class="roman">[</span>, domain<span class="roman">]</span>)</em></dt>
<dd><p>Like <code>Objfile.lookup_global_symbol</code>, but searches for a global
symbol with static linkage named <var>name</var> in this objfile.
</p></dd></dl>

<hr>
<div class="header">
<p>
Next: <a href="Frames-In-Python.html#Frames-In-Python" accesskey="n" rel="next">Frames In Python</a>, Previous: <a href="Progspaces-In-Python.html#Progspaces-In-Python" accesskey="p" rel="previous">Progspaces In Python</a>, Up: <a href="Python-API.html#Python-API" accesskey="u" rel="up">Python API</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
