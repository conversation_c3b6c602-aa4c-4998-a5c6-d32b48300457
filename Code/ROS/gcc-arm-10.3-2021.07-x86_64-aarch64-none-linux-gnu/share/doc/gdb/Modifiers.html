<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: Modifiers</title>

<meta name="description" content="Debugging with GDB: Modifiers">
<meta name="keywords" content="Debugging with GDB: Modifiers">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="History-Interaction.html#History-Interaction" rel="up" title="History Interaction">
<link href="In-Memoriam.html#In-Memoriam" rel="next" title="In Memoriam">
<link href="Word-Designators.html#Word-Designators" rel="previous" title="Word Designators">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Modifiers"></a>
<div class="header">
<p>
Previous: <a href="Word-Designators.html#Word-Designators" accesskey="p" rel="previous">Word Designators</a>, Up: <a href="History-Interaction.html#History-Interaction" accesskey="u" rel="up">History Interaction</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Modifiers-1"></a>
<h4 class="subsection">33.1.3 Modifiers</h4>

<p>After the optional word designator, you can add a sequence of one or more
of the following modifiers, each preceded by a &lsquo;<samp>:</samp>&rsquo;.
</p>
<dl compact="compact">
<dt><code>h</code></dt>
<dd><p>Remove a trailing pathname component, leaving only the head.
</p>
</dd>
<dt><code>t</code></dt>
<dd><p>Remove all leading pathname components, leaving the tail.
</p>
</dd>
<dt><code>r</code></dt>
<dd><p>Remove a trailing suffix of the form &lsquo;<samp>.<var>suffix</var></samp>&rsquo;, leaving
the basename.
</p>
</dd>
<dt><code>e</code></dt>
<dd><p>Remove all but the trailing suffix.
</p>
</dd>
<dt><code>p</code></dt>
<dd><p>Print the new command but do not execute it.
</p>

</dd>
<dt><code>s/<var>old</var>/<var>new</var>/</code></dt>
<dd><p>Substitute <var>new</var> for the first occurrence of <var>old</var> in the
event line.  Any delimiter may be used in place of &lsquo;<samp>/</samp>&rsquo;.
The delimiter may be quoted in <var>old</var> and <var>new</var>
with a single backslash.  If &lsquo;<samp>&amp;</samp>&rsquo; appears in <var>new</var>,
it is replaced by <var>old</var>.  A single backslash will quote
the &lsquo;<samp>&amp;</samp>&rsquo;.  The final delimiter is optional if it is the last
character on the input line.
</p>
</dd>
<dt><code>&amp;</code></dt>
<dd><p>Repeat the previous substitution.
</p>
</dd>
<dt><code>g</code></dt>
<dt><code>a</code></dt>
<dd><p>Cause changes to be applied over the entire event line.  Used in
conjunction with &lsquo;<samp>s</samp>&rsquo;, as in <code>gs/<var>old</var>/<var>new</var>/</code>,
or with &lsquo;<samp>&amp;</samp>&rsquo;.
</p>
</dd>
<dt><code>G</code></dt>
<dd><p>Apply the following &lsquo;<samp>s</samp>&rsquo; modifier once to each word in the event.
</p>
</dd>
</dl>




</body>
</html>
