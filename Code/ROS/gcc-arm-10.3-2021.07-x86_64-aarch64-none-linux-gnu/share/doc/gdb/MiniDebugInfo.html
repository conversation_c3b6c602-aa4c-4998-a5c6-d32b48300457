<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being "Free Software" and "Free Software Needs
Free Documentation", with the Front-Cover Texts being "A GNU Manual,"
and with the Back-Cover Texts as in (a) below.

(a) The FSF's Back-Cover Text is: "You are free to copy and modify
this GNU Manual.  Buying copies from GNU Press supports the FSF in
developing GNU and promoting software freedom." -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>Debugging with GDB: MiniDebugInfo</title>

<meta name="description" content="Debugging with GDB: MiniDebugInfo">
<meta name="keywords" content="Debugging with GDB: MiniDebugInfo">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="Concept-Index.html#Concept-Index" rel="index" title="Concept Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="GDB-Files.html#GDB-Files" rel="up" title="GDB Files">
<link href="Index-Files.html#Index-Files" rel="next" title="Index Files">
<link href="Separate-Debug-Files.html#Separate-Debug-Files" rel="previous" title="Separate Debug Files">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="MiniDebugInfo"></a>
<div class="header">
<p>
Next: <a href="Index-Files.html#Index-Files" accesskey="n" rel="next">Index Files</a>, Previous: <a href="Separate-Debug-Files.html#Separate-Debug-Files" accesskey="p" rel="previous">Separate Debug Files</a>, Up: <a href="GDB-Files.html#GDB-Files" accesskey="u" rel="up">GDB Files</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Debugging-Information-in-a-Special-Section"></a>
<h3 class="section">18.4 Debugging Information in a Special Section</h3>
<a name="index-separate-debug-sections"></a>
<a name="index-_002egnu_005fdebugdata-section"></a>

<p>Some systems ship pre-built executables and libraries that have a
special &lsquo;<samp>.gnu_debugdata</samp>&rsquo; section.  This feature is called
<em>MiniDebugInfo</em>.  This section holds an LZMA-compressed object and
is used to supply extra symbols for backtraces.
</p>
<p>The intent of this section is to provide extra minimal debugging
information for use in simple backtraces.  It is not intended to be a
replacement for full separate debugging information (see <a href="Separate-Debug-Files.html#Separate-Debug-Files">Separate Debug Files</a>).  The example below shows the intended use; however,
<small>GDB</small> does not currently put restrictions on what sort of
debugging information might be included in the section.
</p>
<p><small>GDB</small> has support for this extension.  If the section exists,
then it is used provided that no other source of debugging information
can be found, and that <small>GDB</small> was configured with LZMA support.
</p>
<p>This section can be easily created using <code>objcopy</code> and other
standard utilities:
</p>
<div class="smallexample">
<pre class="smallexample"># Extract the dynamic symbols from the main binary, there is no need
# to also have these in the normal symbol table.
nm -D <var>binary</var> --format=posix --defined-only \
  | awk '{ print $1 }' | sort &gt; dynsyms

# Extract all the text (i.e. function) symbols from the debuginfo.
# (Note that we actually also accept &quot;D&quot; symbols, for the benefit
# of platforms like PowerPC64 that use function descriptors.)
nm <var>binary</var> --format=posix --defined-only \
  | awk '{ if ($2 == &quot;T&quot; || $2 == &quot;t&quot; || $2 == &quot;D&quot;) print $1 }' \
  | sort &gt; funcsyms

# Keep all the function symbols not already in the dynamic symbol
# table.
comm -13 dynsyms funcsyms &gt; keep_symbols

# Separate full debug info into debug binary.
objcopy --only-keep-debug <var>binary</var> debug

# Copy the full debuginfo, keeping only a minimal set of symbols and
# removing some unnecessary sections.
objcopy -S --remove-section .gdb_index --remove-section .comment \
  --keep-symbols=keep_symbols debug mini_debuginfo

# Drop the full debug info from the original binary.
strip --strip-all -R .comment <var>binary</var>

# Inject the compressed data into the .gnu_debugdata section of the
# original binary.
xz mini_debuginfo
objcopy --add-section .gnu_debugdata=mini_debuginfo.xz <var>binary</var>
</pre></div>

<hr>
<div class="header">
<p>
Next: <a href="Index-Files.html#Index-Files" accesskey="n" rel="next">Index Files</a>, Previous: <a href="Separate-Debug-Files.html#Separate-Debug-Files" accesskey="p" rel="previous">Separate Debug Files</a>, Up: <a href="GDB-Files.html#GDB-Files" accesskey="u" rel="up">GDB Files</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="Concept-Index.html#Concept-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
