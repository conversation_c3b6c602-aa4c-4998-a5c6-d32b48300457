<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- This file documents the GNU linker LD
(GNU Toolchain for the A-profile Architecture 10.3-2021.07 (arm-10.29))
version 2.36.1.

Copyright (C) 1991-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3
or any later version published by the Free Software Foundation;
with no Invariant Sections, with no Front-Cover Texts, and with no
Back-Cover Texts.  A copy of the license is included in the
section entitled "GNU Free Documentation License". -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>LD: Output Section Discarding</title>

<meta name="description" content="LD: Output Section Discarding">
<meta name="keywords" content="LD: Output Section Discarding">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="LD-Index.html#LD-Index" rel="index" title="LD Index">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="SECTIONS.html#SECTIONS" rel="up" title="SECTIONS">
<link href="Output-Section-Attributes.html#Output-Section-Attributes" rel="next" title="Output Section Attributes">
<link href="Output-Section-Keywords.html#Output-Section-Keywords" rel="previous" title="Output Section Keywords">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Output-Section-Discarding"></a>
<div class="header">
<p>
Next: <a href="Output-Section-Attributes.html#Output-Section-Attributes" accesskey="n" rel="next">Output Section Attributes</a>, Previous: <a href="Output-Section-Keywords.html#Output-Section-Keywords" accesskey="p" rel="previous">Output Section Keywords</a>, Up: <a href="SECTIONS.html#SECTIONS" accesskey="u" rel="up">SECTIONS</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="LD-Index.html#LD-Index" title="Index" rel="index">Index</a>]</p>
</div>
<hr>
<a name="Output-Section-Discarding-1"></a>
<h4 class="subsection">3.6.7 Output Section Discarding</h4>
<a name="index-discarding-sections"></a>
<a name="index-sections_002c-discarding"></a>
<a name="index-removing-sections"></a>
<p>The linker will not normally create output sections with no contents.
This is for convenience when referring to input sections that may or
may not be present in any of the input files.  For example:
</p><div class="smallexample">
<pre class="smallexample">.foo : { *(.foo) }
</pre></div>
<p>will only create a &lsquo;<samp>.foo</samp>&rsquo; section in the output file if there is a
&lsquo;<samp>.foo</samp>&rsquo; section in at least one input file, and if the input
sections are not all empty.  Other link script directives that allocate
space in an output section will also create the output section.  So
too will assignments to dot even if the assignment does not create
space, except for &lsquo;<samp>. = 0</samp>&rsquo;, &lsquo;<samp>. = . + 0</samp>&rsquo;, &lsquo;<samp>. = sym</samp>&rsquo;,
&lsquo;<samp>. = . + sym</samp>&rsquo; and &lsquo;<samp>. = ALIGN (. != 0, expr, 1)</samp>&rsquo; when
&lsquo;<samp>sym</samp>&rsquo; is an absolute symbol of value 0 defined in the script.
This allows you to force output of an empty section with &lsquo;<samp>. = .</samp>&rsquo;.
</p>
<p>The linker will ignore address assignments (see <a href="Output-Section-Address.html#Output-Section-Address">Output Section Address</a>)
on discarded output sections, except when the linker script defines
symbols in the output section.  In that case the linker will obey
the address assignments, possibly advancing dot even though the
section is discarded.
</p>
<a name="index-_002fDISCARD_002f"></a>
<p>The special output section name &lsquo;<samp>/DISCARD/</samp>&rsquo; may be used to discard
input sections.  Any input sections which are assigned to an output
section named &lsquo;<samp>/DISCARD/</samp>&rsquo; are not included in the output file.
</p>
<p>This can be used to discard input sections marked with the ELF flag
<code>SHF_GNU_RETAIN</code>, which would otherwise have been saved from linker
garbage collection.
</p>
<p>Note, sections that match the &lsquo;<samp>/DISCARD/</samp>&rsquo; output section will be
discarded even if they are in an ELF section group which has other
members which are not being discarded.  This is deliberate.
Discarding takes precedence over grouping.
</p>
<hr>
<div class="header">
<p>
Next: <a href="Output-Section-Attributes.html#Output-Section-Attributes" accesskey="n" rel="next">Output Section Attributes</a>, Previous: <a href="Output-Section-Keywords.html#Output-Section-Keywords" accesskey="p" rel="previous">Output Section Keywords</a>, Up: <a href="SECTIONS.html#SECTIONS" accesskey="u" rel="up">SECTIONS</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>][<a href="LD-Index.html#LD-Index" title="Index" rel="index">Index</a>]</p>
</div>



</body>
</html>
