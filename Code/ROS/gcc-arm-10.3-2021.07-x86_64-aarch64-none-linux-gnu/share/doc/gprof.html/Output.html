<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- This file documents the gprof profiler of the GNU system.

Copyright (C) 1988-2021 Free Software Foundation, Inc.

Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3
or any later version published by the Free Software Foundation;
with no Invariant Sections, with no Front-Cover Texts, and with no
Back-Cover Texts.  A copy of the license is included in the
section entitled "GNU Free Documentation License".
 -->
<!-- Created by GNU Texinfo 5.1, http://www.gnu.org/software/texinfo/ -->
<head>
<title>GNU gprof: Output</title>

<meta name="description" content="GNU gprof: Output">
<meta name="keywords" content="GNU gprof: Output">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="makeinfo">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="index.html#Top" rel="start" title="Top">
<link href="index.html#SEC_Contents" rel="contents" title="Table of Contents">
<link href="index.html#Top" rel="up" title="Top">
<link href="Flat-Profile.html#Flat-Profile" rel="next" title="Flat Profile">
<link href="Symspecs.html#Symspecs" rel="previous" title="Symspecs">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
blockquote.smallquotation {font-size: smaller}
div.display {margin-left: 3.2em}
div.example {margin-left: 3.2em}
div.indentedblock {margin-left: 3.2em}
div.lisp {margin-left: 3.2em}
div.smalldisplay {margin-left: 3.2em}
div.smallexample {margin-left: 3.2em}
div.smallindentedblock {margin-left: 3.2em; font-size: smaller}
div.smalllisp {margin-left: 3.2em}
kbd {font-style:oblique}
pre.display {font-family: inherit}
pre.format {font-family: inherit}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: inherit; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: inherit; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.nocodebreak {white-space:nowrap}
span.nolinebreak {white-space:nowrap}
span.roman {font-family:serif; font-weight:normal}
span.sansserif {font-family:sans-serif; font-weight:normal}
ul.no-bullet {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">
<a name="Output"></a>
<div class="header">
<p>
Next: <a href="Inaccuracy.html#Inaccuracy" accesskey="n" rel="next">Inaccuracy</a>, Previous: <a href="Invoking.html#Invoking" accesskey="p" rel="previous">Invoking</a>, Up: <a href="index.html#Top" accesskey="u" rel="up">Top</a> &nbsp; [<a href="index.html#SEC_Contents" title="Table of contents" rel="contents">Contents</a>]</p>
</div>
<hr>
<a name="Interpreting-gprof_0027s-Output"></a>
<h2 class="chapter">5 Interpreting <code>gprof</code>&rsquo;s Output</h2>

<p><code>gprof</code> can produce several different output styles, the
most important of which are described below.  The simplest output
styles (file information, execution count, and function and file ordering)
are not described here, but are documented with the respective options
that trigger them.
See <a href="Output-Options.html#Output-Options">Output Options</a>.
</p>
<table class="menu" border="0" cellspacing="0">
<tr><td align="left" valign="top">&bull; <a href="Flat-Profile.html#Flat-Profile" accesskey="1">Flat Profile</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">The flat profile shows how much time was spent
                            executing directly in each function.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Call-Graph.html#Call-Graph" accesskey="2">Call Graph</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">The call graph shows which functions called which
                            others, and how much time each function used
                            when its subroutine calls are included.
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Line_002dby_002dline.html#Line_002dby_002dline" accesskey="3">Line-by-line</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top"><code>gprof</code> can analyze individual source code lines
</td></tr>
<tr><td align="left" valign="top">&bull; <a href="Annotated-Source.html#Annotated-Source" accesskey="4">Annotated Source</a>:</td><td>&nbsp;&nbsp;</td><td align="left" valign="top">The annotated source listing displays source code
                            labeled with execution counts
</td></tr>
</table>





</body>
</html>
