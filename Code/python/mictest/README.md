# 讯飞6麦设备功能检测程序

这是一个用于检测讯飞6麦阵列设备功能的Python程序，可以全面测试设备的各项性能指标。

## 功能特性

### 🖥️ 图形用户界面

- 现代化PyQt6界面设计
- 实时测试进度显示
- 交互式图表分析
- 多标签页结果展示
- 设备信息可视化

### 🔍 设备检测

- 自动检测讯飞6麦设备
- 手动选择特定麦克风设备
- 列出所有可用音频设备
- 验证设备连接状态
- 获取设备能力信息

### 🎤 基本功能测试

- 各通道录音功能测试
- 音频质量分析（THD+N、动态范围、频率响应）
- 信噪比测量
- 频谱分析和可视化

### 📡 波束成形测试

- 方向性拾音测试
- 波束图案分析
- 主瓣宽度和旁瓣抑制测量
- 空间分辨率测试

### 🔇 降噪功能测试

- 谱减法降噪测试
- 维纳滤波测试
- 自适应噪声消除
- 降噪性能评估

### 🌐 远程设备检测 **(新功能)**

- SSH远程连接支持
- 远程Python环境检查
- 自动依赖安装
- 远程测试执行
- 结果自动下载
- 支持密码和密钥认证

### 📊 数据可视化

- 频率响应图表
- 波束图案显示（2D/3D）
- 通道性能对比
- 信噪比分析图表

## 安装要求

### 系统要求

- Python 3.7+
- Windows/Linux/macOS
- 讯飞6麦阵列设备

### 依赖包安装

```bash
# 基础依赖安装
pip install -r requirements.txt

# 如果需要远程检测功能，还需要安装：
pip install paramiko scp

# 如果遇到pyaudio安装问题，可以尝试：
# Windows:
pip install pipwin
pipwin install pyaudio

# Linux:
sudo apt-get install portaudio19-dev python3-pyaudio
pip install pyaudio

# macOS:
brew install portaudio
pip install pyaudio
```

## 使用方法

### 快速开始

```bash
# 运行GUI版本（推荐）
python run_gui.py
# 或者在Windows下双击 run_gui.bat

# 运行命令行版本（本地检测）
python main.py                    # 自动检测设备
python main.py -m                 # 手动选择设备
python main.py -d 5               # 直接使用设备索引5
python main.py --list-devices     # 仅列出所有设备

# 远程设备检测
python main.py -r                 # 远程检测（交互式）
python main.py -r --host ************* --user pi  # 远程检测（命令行）

# 设备选择器（专用工具）
python device_selector.py         # 交互式设备选择器
python device_selector.py -r 5    # 直接使用设备5运行测试
```

### 设备选择方式

#### 1. 自动检测模式（默认）

```bash
python main.py
```

程序会自动搜索讯飞相关关键词的设备，如果没找到则选择第一个多通道设备。

#### 2. 手动选择模式

```bash
python main.py -m
```

程序会列出所有设备并让您交互式选择，支持以下操作：

- 输入设备索引直接选择
- 输入 'auto' 切换到自动检测
- 输入 'quit' 退出程序

#### 3. 直接指定设备

```bash
# 首先查看所有设备
python main.py --list-devices

# 使用特定设备运行测试
python main.py -d 设备索引
```

#### 4. 设备选择器工具

```bash
python device_selector.py
```

提供更友好的交互界面，支持：

- 彩色设备列表显示
- 设备连接测试
- 设备兼容性提示
- 直接运行测试

#### 5. 远程设备检测

```bash
# 交互式远程检测（推荐）
python main.py -r

# 命令行模式
python main.py -r --host <IP地址> --user <用户名>

# 使用私钥认证
python main.py -r --host <IP地址> --user <用户名> --key ~/.ssh/id_rsa

# 直接运行远程检测器
python remote_main.py --interactive
```

远程检测支持：

- 🔗 SSH连接（密码/私钥/默认密钥认证）
- 🔍 自动环境检查和依赖安装
- 🎤 远程音频设备检测
- 📊 完整测试流程执行
- 📥 自动下载测试结果

### 测试流程

1. **设备检测阶段**

   - 程序会扫描并检测可用音频设备
   - 显示所有设备列表和推荐状态
   - 验证设备连接状态
   - 支持手动选择特定设备
2. **基本功能测试**

   - 提示用户在设备附近说话或制造声音
   - 录制3秒音频进行分析
   - 测试各通道功能和音频质量
3. **波束成形测试**

   - 提示用户从不同方向对设备说话
   - 录制5秒音频进行方向性分析
   - 生成波束图案和性能报告
4. **降噪功能测试**

   - 分别在安静和噪声环境中录音
   - 测试不同降噪算法的效果
   - 评估降噪性能指标

### GUI界面使用

#### 主界面布局

- **左侧控制面板**: 设备选择、测试配置、测试控制
- **右侧结果面板**: 多标签页显示日志、设备信息、测试结果、图表分析

#### 使用步骤

1. **启动程序**: 双击 `run_gui.bat` 或运行 `python run_gui.py`
2. **检测设备**: 点击"🔍 检测设备"按钮
3. **配置参数**: 在测试配置区域调整录音时长、采样率等参数
4. **选择测试**:
   - 单项测试: 点击对应的测试按钮
   - 完整测试: 点击"🚀 运行完整测试"
5. **查看结果**: 在右侧标签页中查看详细结果和图表

#### 图表分析功能

- **频率响应**: 显示各通道的频率响应曲线
- **波束图案**: 极坐标和直角坐标显示波束成形效果
- **通道对比**: 柱状图对比各通道的RMS、峰值、SNR
- **信噪比分析**: 可视化各通道信噪比，标注性能阈值
- **3D波束图案**: 三维立体显示波束成形图案

## 输出结果

### 文件输出

- `test_results/` - 测试结果目录
  - `test_report.json` - 详细测试报告
  - `basic_test_ch*.wav` - 各通道录音文件
  - `frequency_response.png` - 频率响应图
  - `beam_pattern.png` - 波束图案图
- `xunfei_mic_test.log` - 测试日志文件

### 控制台输出

- 实时测试进度显示
- 彩色状态指示
- 测试结果摘要
- 错误和警告信息

## 配置选项

可以通过修改 `config.py` 文件来调整测试参数：

```python
# 设备配置
DEVICE_CONFIG = {
    'channels': 6,          # 麦克风通道数
    'sample_rate': 16000,   # 采样率
    'record_duration': 3,   # 录音时长
}

# 测试配置
TEST_CONFIG = {
    'volume_threshold': 0.01,  # 音量阈值
    'snr_threshold': 20,       # 信噪比阈值
    'frequency_range': (100, 8000),  # 测试频率范围
}
```

## 详细文档

- [设备选择使用指南](DEVICE_SELECTION_GUIDE.md) - 详细的设备选择说明和故障排除

## 故障排除

### 常见问题

1. **找不到设备**

   - 确保设备已正确连接
   - 检查设备驱动是否安装
   - 尝试在系统音频设置中查看设备
2. **录音失败**

   - 检查设备权限设置
   - 确保没有其他程序占用设备
   - 尝试重新插拔设备
3. **依赖包安装失败**

   - 更新pip: `pip install --upgrade pip`
   - 使用conda安装: `conda install package_name`
   - 查看具体错误信息并搜索解决方案

### 调试模式

```bash
# 启用详细日志
export PYTHONPATH=.
python -c "
from config import OUTPUT_CONFIG
OUTPUT_CONFIG['log_level'] = 'DEBUG'
exec(open('main.py').read())
"
```

## 技术说明

### 测试原理

1. **通道测试**: 通过RMS、峰值和SNR分析各通道信号质量
2. **波束成形**: 使用延迟求和算法实现方向性拾音
3. **降噪测试**: 实现谱减法和维纳滤波算法
4. **质量评估**: 基于THD+N、动态范围等指标评估音频质量

### 性能指标

- **信噪比(SNR)**: > 20dB 为良好
- **波束宽度**: 通常在30-60度之间
- **旁瓣抑制**: > 15dB 为良好
- **降噪效果**: SNR改善 > 5dB 为有效

## 许可证

本项目仅供学习和测试使用。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 创建Issue
- 发送邮件

---

**注意**: 使用前请确保已正确安装讯飞6麦设备驱动程序。
