#!/usr/bin/env python3
"""
讯飞6麦设备检测工具 - GUI启动脚本
"""
import sys
import os

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import PyQt6
    except ImportError:
        missing_packages.append('PyQt6')
    
    try:
        import matplotlib
    except ImportError:
        missing_packages.append('matplotlib')
        
    try:
        import numpy
    except ImportError:
        missing_packages.append('numpy')
        
    try:
        import sounddevice
    except ImportError:
        missing_packages.append('sounddevice')
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print("pip install -r requirements.txt")
        print("或者运行: python install.py")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动讯飞6麦设备检测工具...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 导入并启动GUI
    try:
        from gui_main import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n请检查:")
        print("1. 是否正确安装了所有依赖包")
        print("2. 是否有权限访问音频设备")
        print("3. 查看详细错误信息")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
