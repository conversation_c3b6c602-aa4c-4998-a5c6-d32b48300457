#!/usr/bin/env python3
"""
讯飞6麦设备功能检测主程序
"""
import logging
import sys
import os
import json
from datetime import datetime
from colorama import init, Fore, Style
from tqdm import tqdm
import numpy as np
# 初始化colorama
init(autoreset=True)

# 导入自定义模块
from device_detector import DeviceDetector
from audio_tester import AudioTester
from beamforming_tester import BeamformingTester
from noise_suppression_tester import NoiseSuppressionTester
from config import DEVICE_CONFIG, TEST_CONFIG, OUTPUT_CONFIG

class XunfeiMicTester:
    """讯飞6麦设备测试器主类"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        self.device_detector = DeviceDetector()
        self.audio_tester = None
        self.beamforming_tester = BeamformingTester()
        self.noise_tester = NoiseSuppressionTester()
        
        self.test_results = {}
        
    def setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, OUTPUT_CONFIG['log_level'])
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('xunfei_mic_test.log', encoding='utf-8')
            ]
        )
    
    def print_banner(self):
        """打印程序横幅"""
        banner = f"""
{Fore.CYAN}{'='*60}
{Fore.YELLOW}           讯飞6麦设备功能检测程序
{Fore.CYAN}{'='*60}
{Fore.GREEN}版本: 1.0.0
{Fore.GREEN}作者: AI Assistant
{Fore.GREEN}时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{Fore.CYAN}{'='*60}{Style.RESET_ALL}
        """
        print(banner)
    
    def detect_device(self, manual_select: bool = False, device_index: int = None) -> bool:
        """检测设备"""
        print(f"\n{Fore.YELLOW}🔍 正在检测讯飞6麦设备...{Style.RESET_ALL}")
        
        # 列出所有设备
        all_devices = self.device_detector.list_all_devices()
        print(f"\n{Fore.CYAN}📋 检测到的音频设备:{Style.RESET_ALL}")
        
        for i, device in enumerate(all_devices):
            status = "✅" if device['input_channels'] >= 6 else "❌"
            recommended = " [推荐]" if device['input_channels'] >= 6 else ""
            print(f"  {status} [{i:2d}] {device['name'][:50]:<50} "
                  f"输入:{device['input_channels']:2d} 输出:{device['output_channels']:2d}{recommended}")
        
        device_info = None
        
        # 如果指定了设备索引，直接使用
        if device_index is not None:
            device_info = self.device_detector.select_device_manually(device_index)
            if device_info:
                print(f"\n{Fore.GREEN}✅ 已选择设备 [{device_index}]:{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.RED}❌ 设备索引 {device_index} 无效{Style.RESET_ALL}")
                return False
        
        # 如果需要手动选择
        elif manual_select:
            while True:
                try:
                    print(f"\n{Fore.YELLOW}请选择要使用的麦克风设备:{Style.RESET_ALL}")
                    print(f"{Fore.CYAN}  输入设备索引 (0-{len(all_devices)-1}):{Style.RESET_ALL}")
                    print(f"{Fore.CYAN}  输入 'auto' 进行自动检测{Style.RESET_ALL}")
                    print(f"{Fore.CYAN}  输入 'quit' 退出程序{Style.RESET_ALL}")
                    
                    user_input = input("> ").strip().lower()
                    
                    if user_input == 'quit':
                        print(f"{Fore.YELLOW}程序退出{Style.RESET_ALL}")
                        return False
                    elif user_input == 'auto':
                        device_info = self.device_detector.detect_xunfei_device()
                        break
                    else:
                        device_index = int(user_input)
                        if 0 <= device_index < len(all_devices):
                            device_info = self.device_detector.select_device_manually(device_index)
                            if device_info:
                                break
                        else:
                            print(f"{Fore.RED}❌ 设备索引超出范围，请输入 0-{len(all_devices)-1}{Style.RESET_ALL}")
                            
                except ValueError:
                    print(f"{Fore.RED}❌ 请输入有效的数字、'auto' 或 'quit'{Style.RESET_ALL}")
                except KeyboardInterrupt:
                    print(f"\n{Fore.YELLOW}程序退出{Style.RESET_ALL}")
                    return False
        
        # 自动检测模式
        else:
            device_info = self.device_detector.detect_xunfei_device()
        
        if device_info:
            print(f"\n{Fore.GREEN}✅ 成功检测到设备:{Style.RESET_ALL}")
            print(f"   设备名称: {device_info['name']}")
            print(f"   设备索引: {device_info['index']}")
            print(f"   输入通道: {device_info['channels']}")
            print(f"   采样率: {device_info['sample_rate']}")
            
            # 测试设备连接
            if self.device_detector.test_device_connection():
                print(f"{Fore.GREEN}✅ 设备连接测试通过{Style.RESET_ALL}")
                
                # 初始化音频测试器
                self.audio_tester = AudioTester(device_info['index'])
                
                # 获取设备能力
                capabilities = self.device_detector.get_device_capabilities()
                self.test_results['device_info'] = device_info
                self.test_results['device_capabilities'] = capabilities
                
                return True
            else:
                print(f"{Fore.RED}❌ 设备连接测试失败{Style.RESET_ALL}")
                return False
        else:
            print(f"{Fore.RED}❌ 未检测到合适的6麦设备{Style.RESET_ALL}")
            return False
    
    def test_basic_functionality(self):
        """测试基本功能"""
        print(f"\n{Fore.YELLOW}🎤 开始基本功能测试...{Style.RESET_ALL}")
        
        if not self.audio_tester:
            print(f"{Fore.RED}❌ 音频测试器未初始化{Style.RESET_ALL}")
            return
        
        # 录制测试音频
        print(f"{Fore.CYAN}📹 请在设备附近说话或制造声音...{Style.RESET_ALL}")
        audio_data = self.audio_tester.record_audio()
        
        if audio_data.size == 0:
            print(f"{Fore.RED}❌ 录音失败{Style.RESET_ALL}")
            return
        
        # 保存录音文件
        self.audio_tester.save_audio_file(audio_data, "basic_test")
        
        # 测试各通道功能
        print(f"{Fore.CYAN}🔧 测试各通道功能...{Style.RESET_ALL}")
        channel_results = self.audio_tester.test_channel_functionality()
        
        # 显示通道测试结果
        print(f"\n{Fore.CYAN}📊 通道测试结果:{Style.RESET_ALL}")
        for channel, result in channel_results.items():
            status = "✅" if result['is_active'] else "❌"
            print(f"  {status} 通道 {channel}: "
                  f"RMS={result['rms']:.4f} "
                  f"Peak={result['peak']:.4f} "
                  f"SNR={result['snr_db']:.1f}dB")
        
        # 音频质量测试
        print(f"{Fore.CYAN}🎵 测试音频质量...{Style.RESET_ALL}")
        quality_results = self.audio_tester.test_audio_quality(audio_data)
        
        # 绘制频率响应图
        self.audio_tester.plot_frequency_response(channel_results)
        
        self.test_results['channel_test'] = channel_results
        self.test_results['quality_test'] = quality_results
        
        print(f"{Fore.GREEN}✅ 基本功能测试完成{Style.RESET_ALL}")
    
    def test_beamforming(self):
        """测试波束成形功能"""
        print(f"\n{Fore.YELLOW}📡 开始波束成形测试...{Style.RESET_ALL}")
        
        if not self.audio_tester:
            print(f"{Fore.RED}❌ 音频测试器未初始化{Style.RESET_ALL}")
            return
        
        # 录制用于波束成形测试的音频
        print(f"{Fore.CYAN}📹 请从不同方向对设备说话...{Style.RESET_ALL}")
        audio_data = self.audio_tester.record_audio(duration=5)  # 录制5秒
        
        if audio_data.size == 0:
            print(f"{Fore.RED}❌ 录音失败{Style.RESET_ALL}")
            return
        
        # 测试方向性响应
        print(f"{Fore.CYAN}🎯 测试方向性响应...{Style.RESET_ALL}")
        directional_results = self.beamforming_tester.test_directional_response(audio_data)
        
        # 显示方向性测试结果
        print(f"\n{Fore.CYAN}📊 方向性测试结果:{Style.RESET_ALL}")
        for angle, result in directional_results.items():
            print(f"  📐 {angle:3.0f}°: "
                  f"RMS={result['rms_power']:.4f} "
                  f"Peak={result['peak_power']:.4f}")
        
        # 测试波束成形性能
        print(f"{Fore.CYAN}⚡ 测试波束成形性能...{Style.RESET_ALL}")
        performance_results = self.beamforming_tester.test_beamforming_performance(audio_data)
        
        print(f"\n{Fore.CYAN}📈 波束成形性能:{Style.RESET_ALL}")
        print(f"  主瓣角度: {performance_results['main_lobe_angle']:.1f}°")
        print(f"  -3dB波束宽度: {performance_results['beam_width_3db']:.1f}°")
        print(f"  旁瓣抑制: {performance_results['sidelobe_suppression_db']:.1f}dB")
        
        # 绘制波束图案
        angles, responses = performance_results['beam_pattern']
        self.beamforming_tester.plot_beam_pattern(np.array(angles), np.array(responses))
        
        # 测试空间分辨率
        print(f"{Fore.CYAN}🎯 测试空间分辨率...{Style.RESET_ALL}")
        resolution_results = self.beamforming_tester.test_spatial_resolution(audio_data)
        
        self.test_results['beamforming_test'] = {
            'directional_response': directional_results,
            'performance': performance_results,
            'spatial_resolution': resolution_results
        }
        
        print(f"{Fore.GREEN}✅ 波束成形测试完成{Style.RESET_ALL}")
    
    def test_noise_suppression(self):
        """测试降噪功能"""
        print(f"\n{Fore.YELLOW}🔇 开始降噪功能测试...{Style.RESET_ALL}")
        
        if not self.audio_tester:
            print(f"{Fore.RED}❌ 音频测试器未初始化{Style.RESET_ALL}")
            return
        
        # 录制干净音频
        print(f"{Fore.CYAN}📹 请在安静环境中说话（录制干净音频）...{Style.RESET_ALL}")
        clean_audio = self.audio_tester.record_audio()
        
        # 录制噪声环境音频
        print(f"{Fore.CYAN}📹 请在有噪声的环境中说话（录制噪声音频）...{Style.RESET_ALL}")
        noisy_audio = self.audio_tester.record_audio()
        
        if clean_audio.size == 0 or noisy_audio.size == 0:
            print(f"{Fore.RED}❌ 录音失败{Style.RESET_ALL}")
            return
        
        # 测试降噪性能
        print(f"{Fore.CYAN}🔧 测试降噪算法性能...{Style.RESET_ALL}")
        noise_results = self.noise_tester.test_noise_suppression(clean_audio, noisy_audio)
        
        # 显示降噪测试结果
        print(f"\n{Fore.CYAN}📊 降噪测试结果:{Style.RESET_ALL}")
        for channel, result in noise_results.items():
            print(f"  🎤 {channel}:")
            print(f"    原始SNR: {result['original_snr']:.1f}dB")
            print(f"    谱减法改善: {result['spectral_subtraction']['snr_improvement']:.1f}dB")
            print(f"    维纳滤波改善: {result['wiener_filter']['snr_improvement']:.1f}dB")
        
        self.test_results['noise_suppression_test'] = noise_results
        
        print(f"{Fore.GREEN}✅ 降噪功能测试完成{Style.RESET_ALL}")
    
    def generate_report(self):
        """生成测试报告"""
        print(f"\n{Fore.YELLOW}📋 生成测试报告...{Style.RESET_ALL}")
        
        # 创建输出目录
        os.makedirs(OUTPUT_CONFIG['output_dir'], exist_ok=True)
        
        # 保存详细结果
        report_file = os.path.join(OUTPUT_CONFIG['output_dir'], 'test_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
        
        # 生成简要报告
        self._print_summary_report()
        
        print(f"{Fore.GREEN}✅ 测试报告已保存到: {report_file}{Style.RESET_ALL}")
    
    def _print_summary_report(self):
        """打印简要报告"""
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"{Fore.YELLOW}                    测试总结报告")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        if 'device_info' in self.test_results:
            device_info = self.test_results['device_info']
            print(f"\n{Fore.GREEN}📱 设备信息:{Style.RESET_ALL}")
            print(f"  设备名称: {device_info['name']}")
            print(f"  通道数量: {device_info['channels']}")
            print(f"  采样率: {device_info['sample_rate']} Hz")
        
        if 'channel_test' in self.test_results:
            channel_results = self.test_results['channel_test']
            active_channels = sum(1 for r in channel_results.values() if r['is_active'])
            print(f"\n{Fore.GREEN}🎤 通道测试:{Style.RESET_ALL}")
            print(f"  活跃通道: {active_channels}/{len(channel_results)}")
            
            avg_snr = np.mean([r['snr_db'] for r in channel_results.values()])
            print(f"  平均SNR: {avg_snr:.1f} dB")
        
        if 'beamforming_test' in self.test_results:
            beam_results = self.test_results['beamforming_test']
            if 'performance' in beam_results:
                perf = beam_results['performance']
                print(f"\n{Fore.GREEN}📡 波束成形:{Style.RESET_ALL}")
                print(f"  波束宽度: {perf['beam_width_3db']:.1f}°")
                print(f"  旁瓣抑制: {perf['sidelobe_suppression_db']:.1f} dB")
        
        print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    def run_all_tests(self, manual_select: bool = False, device_index: int = None):
        """运行所有测试"""
        self.print_banner()
        
        try:
            # 设备检测
            if not self.detect_device(manual_select=manual_select, device_index=device_index):
                print(f"{Fore.RED}❌ 设备检测失败，无法继续测试{Style.RESET_ALL}")
                return False
            
            # 基本功能测试
            self.test_basic_functionality()
            
            # 波束成形测试
            self.test_beamforming()
            
            # 降噪功能测试
            self.test_noise_suppression()
            
            # 生成报告
            self.generate_report()
            
            print(f"\n{Fore.GREEN}🎉 所有测试完成！{Style.RESET_ALL}")
            return True
            
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}⚠️  测试被用户中断{Style.RESET_ALL}")
            return False
        except Exception as e:
            self.logger.error(f"测试过程中发生错误: {e}")
            print(f"{Fore.RED}❌ 测试失败: {e}{Style.RESET_ALL}")
            return False

def main():
    """主函数"""
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(
        description='讯飞6麦设备功能检测程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 本地自动检测设备
  python main.py -m                 # 本地手动选择设备
  python main.py -d 5               # 本地直接使用设备索引5
  python main.py --list-devices     # 仅列出本地所有设备
  
  python main.py -r                 # 远程检测模式（交互式）
  python main.py -r --host ************* --user pi  # 远程检测（命令行）
  python main.py -r --host example.com --user ubuntu --key ~/.ssh/id_rsa  # 使用私钥
        """
    )
    
    parser.add_argument('-m', '--manual', 
                        action='store_true',
                        help='手动选择麦克风设备')
    
    parser.add_argument('-d', '--device', 
                        type=int, 
                        metavar='INDEX',
                        help='直接指定设备索引 (可通过 --list-devices 查看)')
    
    parser.add_argument('--list-devices', 
                        action='store_true',
                        help='列出所有音频设备并退出')
    
    parser.add_argument('-r', '--remote', 
                        action='store_true',
                        help='远程设备检测模式')
    
    parser.add_argument('--host', 
                        help='远程主机名/IP地址 (用于远程模式)')
    
    parser.add_argument('--user', 
                        help='SSH用户名 (用于远程模式)')
    
    parser.add_argument('--password', 
                        help='SSH密码 (用于远程模式)')
    
    parser.add_argument('--key', 
                        help='SSH私钥文件路径 (用于远程模式)')
    
    args = parser.parse_args()
    
    # 远程检测模式
    if args.remote:
        try:
            from remote_main import RemoteMicTester
            remote_tester = RemoteMicTester()
            
            if args.host:
                # 命令行模式
                connection_info = {
                    'hostname': args.host,
                    'port': 22,
                    'username': args.user,
                    'password': args.password,
                    'private_key_path': args.key
                }
                
                remote_tester.print_banner()
                if not remote_tester.connect_to_remote(connection_info):
                    sys.exit(1)
                
                if not remote_tester.run_remote_environment_check():
                    sys.exit(1)
                
                test_results = remote_tester.run_remote_test(args.device)
                success = test_results is not None and 'error' not in test_results
                
                remote_tester.cleanup()
                sys.exit(0 if success else 1)
            else:
                # 交互式模式
                success = remote_tester.run_interactive_test()
                sys.exit(0 if success else 1)
                
        except ImportError:
            print(f"{Fore.RED}❌ 远程检测功能需要额外依赖，请运行:{Style.RESET_ALL}")
            print(f"{Fore.CYAN}pip install paramiko scp{Style.RESET_ALL}")
            sys.exit(1)
        except Exception as e:
            print(f"{Fore.RED}❌ 远程检测失败: {e}{Style.RESET_ALL}")
            sys.exit(1)
    
    # 如果只是列出设备，不运行测试
    if args.list_devices:
        from colorama import init, Fore, Style
        init(autoreset=True)
        
        detector = DeviceDetector()
        devices = detector.list_all_devices()
        
        print(f"\n{Fore.CYAN}📋 所有音频设备列表:{Style.RESET_ALL}")
        for i, device in enumerate(devices):
            status = "✅" if device['input_channels'] >= 6 else "❌"
            recommended = " [推荐]" if device['input_channels'] >= 6 else ""
            print(f"  {status} [{i:2d}] {device['name'][:60]:<60} "
                  f"输入:{device['input_channels']:2d} 输出:{device['output_channels']:2d}{recommended}")
        
        print(f"\n{Fore.YELLOW}提示: 使用 'python main.py -d INDEX' 来指定设备{Style.RESET_ALL}")
        return
    
    # 创建测试器并运行测试
    tester = XunfeiMicTester()
    
    # 如果没有指定任何参数，默认使用手动选择
    if not args.manual and args.device is None:
        success = tester.run_all_tests(manual_select=True)
    else:
        success = tester.run_all_tests(
            manual_select=args.manual,
            device_index=args.device
        )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
