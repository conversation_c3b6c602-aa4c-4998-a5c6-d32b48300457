#!/usr/bin/env python3
"""
安装验证脚本
检查所有依赖包是否正确安装
"""
import sys
import importlib
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

def test_import(module_name, package_name=None):
    """测试模块导入"""
    try:
        importlib.import_module(module_name)
        print(f"{Fore.GREEN}✅ {package_name or module_name} - 导入成功{Style.RESET_ALL}")
        return True
    except ImportError as e:
        print(f"{Fore.RED}❌ {package_name or module_name} - 导入失败: {e}{Style.RESET_ALL}")
        return False

def test_audio_devices():
    """测试音频设备访问"""
    try:
        import sounddevice as sd
        devices = sd.query_devices()
        print(f"{Fore.GREEN}✅ 音频设备访问 - 检测到 {len(devices)} 个设备{Style.RESET_ALL}")
        
        # 显示前几个设备
        print(f"{Fore.CYAN}   前5个设备:{Style.RESET_ALL}")
        for i, device in enumerate(devices[:5]):
            print(f"     [{i}] {device['name'][:40]}")
        
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ 音频设备访问失败: {e}{Style.RESET_ALL}")
        return False

def main():
    """主函数"""
    print(f"{Fore.CYAN}{'='*50}")
    print(f"{Fore.YELLOW}    讯飞6麦设备检测程序 - 安装验证")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    
    print(f"\n{Fore.YELLOW}🔍 检查Python版本...{Style.RESET_ALL}")
    python_version = sys.version_info
    if python_version >= (3, 7):
        print(f"{Fore.GREEN}✅ Python {python_version.major}.{python_version.minor}.{python_version.micro} - 版本符合要求{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ Python版本过低，需要3.7+{Style.RESET_ALL}")
        return False
    
    print(f"\n{Fore.YELLOW}📦 检查依赖包...{Style.RESET_ALL}")
    
    # 必需的包
    required_packages = [
        ('numpy', 'NumPy'),
        ('scipy', 'SciPy'),
        ('matplotlib', 'Matplotlib'),
        ('sounddevice', 'SoundDevice'),
        ('librosa', 'Librosa'),
        ('soundfile', 'SoundFile'),
        ('colorama', 'Colorama'),
        ('tqdm', 'TQDM'),
    ]
    
    # 可选的包
    optional_packages = [
        ('pyaudio', 'PyAudio'),
        ('pydub', 'PyDub'),
    ]
    
    success_count = 0
    total_required = len(required_packages)
    
    # 测试必需包
    for module, name in required_packages:
        if test_import(module, name):
            success_count += 1
    
    # 测试可选包
    print(f"\n{Fore.YELLOW}📦 检查可选包...{Style.RESET_ALL}")
    for module, name in optional_packages:
        test_import(module, f"{name} (可选)")
    
    print(f"\n{Fore.YELLOW}🎤 测试音频设备访问...{Style.RESET_ALL}")
    audio_ok = test_audio_devices()
    
    print(f"\n{Fore.YELLOW}🔧 测试自定义模块...{Style.RESET_ALL}")
    custom_modules = [
        ('device_detector', '设备检测器'),
        ('audio_tester', '音频测试器'),
        ('beamforming_tester', '波束成形测试器'),
        ('noise_suppression_tester', '降噪测试器'),
        ('config', '配置模块'),
    ]
    
    custom_success = 0
    for module, name in custom_modules:
        if test_import(module, name):
            custom_success += 1
    
    # 总结
    print(f"\n{Fore.CYAN}{'='*50}")
    print(f"{Fore.YELLOW}           安装验证结果")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    
    print(f"必需包: {success_count}/{total_required} 成功")
    print(f"自定义模块: {custom_success}/{len(custom_modules)} 成功")
    print(f"音频设备访问: {'✅' if audio_ok else '❌'}")
    
    if success_count == total_required and custom_success == len(custom_modules) and audio_ok:
        print(f"\n{Fore.GREEN}🎉 安装验证通过！可以运行主程序了。{Style.RESET_ALL}")
        print(f"{Fore.CYAN}运行命令: python main.py{Style.RESET_ALL}")
        return True
    else:
        print(f"\n{Fore.RED}❌ 安装验证失败，请检查缺失的依赖包。{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}安装命令: pip install -r requirements.txt{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
