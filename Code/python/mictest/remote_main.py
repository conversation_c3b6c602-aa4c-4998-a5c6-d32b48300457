#!/usr/bin/env python3
"""
远程麦克风测试主程序
集成远程SSH连接和麦克风检测功能
"""
import argparse
import getpass
import os
import json
import sys
from datetime import datetime
from colorama import init, Fore, Style
import logging

# 初始化colorama
init(autoreset=True)

from remote_detector import RemoteDetector

class RemoteMicTester:
    """远程麦克风测试器主类"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        self.remote_detector = RemoteDetector()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('remote_mic_test.log', encoding='utf-8')
            ]
        )
    
    def print_banner(self):
        """打印程序横幅"""
        banner = f"""
{Fore.CYAN}{'='*70}
{Fore.YELLOW}           远程麦克风设备功能检测程序
{Fore.CYAN}{'='*70}
{Fore.GREEN}版本: 1.0.0
{Fore.GREEN}功能: SSH远程连接 + 6麦设备检测
{Fore.GREEN}时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{Fore.CYAN}{'='*70}{Style.RESET_ALL}
        """
        print(banner)
    
    def get_connection_info(self) -> dict:
        """获取SSH连接信息"""
        print(f"\n{Fore.YELLOW}请输入SSH连接信息:{Style.RESET_ALL}")
        
        hostname = input(f"{Fore.CYAN}主机名/IP地址: {Style.RESET_ALL}").strip()
        if not hostname:
            print(f"{Fore.RED}❌ 主机名不能为空{Style.RESET_ALL}")
            return None
        
        port_input = input(f"{Fore.CYAN}端口 (默认22): {Style.RESET_ALL}").strip()
        port = int(port_input) if port_input else 22
        
        username = input(f"{Fore.CYAN}用户名: {Style.RESET_ALL}").strip()
        if not username:
            print(f"{Fore.RED}❌ 用户名不能为空{Style.RESET_ALL}")
            return None
        
        # 选择认证方式
        print(f"\n{Fore.YELLOW}选择认证方式:{Style.RESET_ALL}")
        print(f"  {Fore.GREEN}1.{Style.RESET_ALL} 密码认证")
        print(f"  {Fore.GREEN}2.{Style.RESET_ALL} 私钥认证")
        print(f"  {Fore.GREEN}3.{Style.RESET_ALL} 使用系统默认SSH密钥")
        
        auth_choice = input(f"{Fore.CYAN}请选择 (1-3): {Style.RESET_ALL}").strip()
        
        password = None
        private_key_path = None
        
        if auth_choice == '1':
            password = getpass.getpass(f"{Fore.CYAN}密码: {Style.RESET_ALL}")
        elif auth_choice == '2':
            private_key_path = input(f"{Fore.CYAN}私钥文件路径: {Style.RESET_ALL}").strip()
            if not os.path.exists(private_key_path):
                print(f"{Fore.RED}❌ 私钥文件不存在{Style.RESET_ALL}")
                return None
        elif auth_choice == '3':
            # 使用默认SSH密钥，不需要额外设置
            pass
        else:
            print(f"{Fore.RED}❌ 无效选择{Style.RESET_ALL}")
            return None
        
        return {
            'hostname': hostname,
            'port': port,
            'username': username,
            'password': password,
            'private_key_path': private_key_path
        }
    
    def connect_to_remote(self, connection_info: dict) -> bool:
        """连接到远程设备"""
        return self.remote_detector.connect(
            hostname=connection_info['hostname'],
            port=connection_info['port'],
            username=connection_info['username'],
            password=connection_info['password'],
            private_key_path=connection_info['private_key_path']
        )
    
    def run_remote_environment_check(self):
        """运行远程环境检查"""
        print(f"\n{Fore.YELLOW}🔍 开始远程环境检查...{Style.RESET_ALL}")
        
        environment_info = self.remote_detector.check_remote_environment()
        
        if not environment_info.get('python_available'):
            print(f"{Fore.RED}❌ 远程设备Python环境不可用{Style.RESET_ALL}")
            return False
        
        # 检查是否需要安装依赖
        missing_packages = []
        required_packages = ['numpy', 'sounddevice', 'scipy']
        
        for package in required_packages:
            if not environment_info['audio_packages'].get(package):
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n{Fore.YELLOW}⚠️  以下包需要安装: {', '.join(missing_packages)}{Style.RESET_ALL}")
            
            install_choice = input(f"{Fore.CYAN}是否自动安装? (y/n): {Style.RESET_ALL}").strip().lower()
            
            if install_choice == 'y':
                if not self.remote_detector.install_dependencies():
                    print(f"{Fore.RED}❌ 依赖安装失败{Style.RESET_ALL}")
                    return False
            else:
                print(f"{Fore.RED}❌ 缺少必要依赖，无法继续测试{Style.RESET_ALL}")
                return False
        
        # 检查是否有合适的音频设备
        audio_devices = environment_info.get('audio_devices', [])
        suitable_devices = [d for d in audio_devices if d['inputs'] >= 6]
        
        if not suitable_devices:
            print(f"{Fore.RED}❌ 未检测到合适的6麦设备{Style.RESET_ALL}")
            return False
        
        print(f"{Fore.GREEN}✅ 远程环境检查通过{Style.RESET_ALL}")
        return True
    
    def run_remote_test(self, device_index: int = None, duration: int = 5):
        """运行远程测试"""
        print(f"\n{Fore.YELLOW}📤 准备远程测试...{Style.RESET_ALL}")
        
        # 上传测试脚本
        if not self.remote_detector.upload_test_scripts():
            print(f"{Fore.RED}❌ 上传测试脚本失败{Style.RESET_ALL}")
            return None
        
        # 运行远程测试
        print(f"\n{Fore.YELLOW}🧪 执行远程麦克风测试...{Style.RESET_ALL}")
        test_results = self.remote_detector.run_remote_test(device_index, duration)
        
        if 'error' in test_results:
            print(f"{Fore.RED}❌ 远程测试失败: {test_results['error']}{Style.RESET_ALL}")
            return test_results
        
        # 显示测试结果
        self.display_test_results(test_results)
        
        # 下载测试结果文件
        print(f"\n{Fore.YELLOW}📥 下载测试结果文件...{Style.RESET_ALL}")
        if self.remote_detector.download_test_results():
            print(f"{Fore.GREEN}✅ 测试结果已下载到 ./remote_results 目录{Style.RESET_ALL}")
        
        return test_results
    
    def display_test_results(self, results: dict):
        """显示测试结果"""
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"{Fore.YELLOW}           远程测试结果")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        # 设备信息
        device_info = results.get('device_info', {})
        print(f"\n{Fore.GREEN}📱 测试设备信息:{Style.RESET_ALL}")
        print(f"  设备索引: {device_info.get('index')}")
        print(f"  设备名称: {device_info.get('name')}")
        print(f"  输入通道: {device_info.get('max_input_channels')}")
        print(f"  输出通道: {device_info.get('max_output_channels')}")
        print(f"  采样率: {device_info.get('default_samplerate')} Hz")
        
        # 通道测试结果
        channel_results = results.get('channel_results', {})
        print(f"\n{Fore.GREEN}🎤 通道测试结果:{Style.RESET_ALL}")
        
        for channel, result in channel_results.items():
            status = "✅" if result['is_active'] else "❌"
            print(f"  {status} 通道 {channel}: "
                  f"RMS={result['rms']:.4f} "
                  f"Peak={result['peak']:.4f} "
                  f"SNR={result['snr_db']:.1f}dB")
        
        # 测试摘要
        summary = results.get('summary', {})
        print(f"\n{Fore.GREEN}📊 测试摘要:{Style.RESET_ALL}")
        print(f"  测试通道数: {summary.get('total_channels_tested', 0)}")
        print(f"  活跃通道数: {summary.get('active_channels', 0)}")
        print(f"  平均SNR: {summary.get('average_snr_db', 0):.1f} dB")
        
        test_passed = summary.get('test_passed', False)
        result_text = "✅ 通过" if test_passed else "❌ 未通过"
        color = Fore.GREEN if test_passed else Fore.RED
        print(f"  测试结果: {color}{result_text}{Style.RESET_ALL}")
        
        # 保存测试报告
        self.save_test_report(results)
    
    def save_test_report(self, results: dict):
        """保存测试报告"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"remote_test_report_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\n{Fore.GREEN}💾 测试报告已保存: {filename}{Style.RESET_ALL}")
            
        except Exception as e:
            self.logger.error(f"保存测试报告失败: {e}")
            print(f"{Fore.RED}❌ 保存测试报告失败: {e}{Style.RESET_ALL}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.remote_detector.cleanup_remote_files()
            self.remote_detector.disconnect()
        except:
            pass
    
    def run_interactive_test(self):
        """运行交互式测试"""
        try:
            self.print_banner()
            
            # 获取连接信息
            connection_info = self.get_connection_info()
            if not connection_info:
                return False
            
            # 连接到远程设备
            if not self.connect_to_remote(connection_info):
                return False
            
            # 检查远程环境
            if not self.run_remote_environment_check():
                return False
            
            # 询问测试参数
            print(f"\n{Fore.YELLOW}设置测试参数:{Style.RESET_ALL}")
            
            device_choice = input(f"{Fore.CYAN}设备索引 (回车自动检测): {Style.RESET_ALL}").strip()
            device_index = int(device_choice) if device_choice else None
            
            duration_choice = input(f"{Fore.CYAN}录音时长/秒 (默认5): {Style.RESET_ALL}").strip()
            duration = int(duration_choice) if duration_choice else 5
            
            # 运行测试
            test_results = self.run_remote_test(device_index, duration)
            
            return test_results is not None and 'error' not in test_results
            
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}用户中断测试{Style.RESET_ALL}")
            return False
        except Exception as e:
            self.logger.error(f"测试过程中出错: {e}")
            print(f"{Fore.RED}❌ 测试过程中出错: {e}{Style.RESET_ALL}")
            return False
        finally:
            self.cleanup()

def main():
    parser = argparse.ArgumentParser(description='远程麦克风设备功能检测程序')
    parser.add_argument('--host', help='远程主机名/IP地址')
    parser.add_argument('--port', type=int, default=22, help='SSH端口')
    parser.add_argument('--user', help='SSH用户名')
    parser.add_argument('--password', help='SSH密码')
    parser.add_argument('--key', help='SSH私钥文件路径')
    parser.add_argument('--device', type=int, help='指定设备索引')
    parser.add_argument('--duration', type=int, default=5, help='录音时长（秒）')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    tester = RemoteMicTester()
    
    if args.interactive or not args.host:
        # 交互式模式
        success = tester.run_interactive_test()
        sys.exit(0 if success else 1)
    
    else:
        # 命令行模式
        try:
            tester.print_banner()
            
            # 使用命令行参数连接
            connection_info = {
                'hostname': args.host,
                'port': args.port,
                'username': args.user,
                'password': args.password,
                'private_key_path': args.key
            }
            
            if not tester.connect_to_remote(connection_info):
                sys.exit(1)
            
            if not tester.run_remote_environment_check():
                sys.exit(1)
            
            test_results = tester.run_remote_test(args.device, args.duration)
            success = test_results is not None and 'error' not in test_results
            
            sys.exit(0 if success else 1)
            
        except Exception as e:
            print(f"{Fore.RED}❌ 程序执行失败: {e}{Style.RESET_ALL}")
            sys.exit(1)
        finally:
            tester.cleanup()

if __name__ == '__main__':
    main() 