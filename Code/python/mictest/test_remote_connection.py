#!/usr/bin/env python3
"""
测试远程连接功能
"""
import sys
import socket
import subprocess
import os

def test_basic_connectivity(hostname):
    """测试基本连通性"""
    print(f"🔍 测试网络连通性到 {hostname}...")
    try:
        result = subprocess.run(['ping', '-c', '1', '-W', '3', hostname], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"  ✅ 网络连通性: 正常")
            return True
        else:
            print(f"  ❌ 网络连通性: 失败")
            return False
    except Exception as e:
        print(f"  ⚠️ 网络连通性: 测试失败 ({e})")
        return False

def test_port(hostname, port):
    """测试端口连通性"""
    print(f"🔍 测试SSH端口 {port} 连通性...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"  ✅ SSH端口({port}): 开放")
            return True
        else:
            print(f"  ❌ SSH端口({port}): 关闭或不可达")
            return False
    except Exception as e:
        print(f"  ⚠️ SSH端口({port}): 测试失败 ({e})")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python test_remote_connection.py <hostname> <username> [password]")
        print("示例:")
        print("  python test_remote_connection.py ************* pi mypassword")
        sys.exit(1)
    
    hostname = sys.argv[1]
    username = sys.argv[2]
    port = 22
    
    print(f"🔗 测试SSH连接到 {username}@{hostname}:{port}")
    print("=" * 50)
    
    tests = []
    
    # 测试网络连通性
    tests.append(test_basic_connectivity(hostname))
    
    # 测试端口连通性
    tests.append(test_port(hostname, port))
    
    print("=" * 50)
    success_count = sum(tests)
    total_tests = len(tests)
    
    if success_count == total_tests:
        print(f"🎉 基本连接测试通过 ({success_count}/{total_tests})")
        sys.exit(0)
    else:
        print(f"⚠️ 部分测试失败 ({success_count}/{total_tests})")
        sys.exit(1)

if __name__ == "__main__":
    main() 