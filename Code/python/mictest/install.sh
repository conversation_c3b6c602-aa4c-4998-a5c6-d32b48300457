#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}========================================"
echo -e "   讯飞6麦设备检测程序 - 自动安装"
echo -e "========================================${NC}"
echo

echo -e "${YELLOW}🔍 检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 未找到Python，请先安装Python 3.7+${NC}"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

$PYTHON_CMD --version
echo -e "${GREEN}✅ Python环境检查通过${NC}"
echo

echo -e "${YELLOW}📦 检查系统依赖...${NC}"
# 检查操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "检测到Linux系统"
    # 检查是否有apt-get
    if command -v apt-get &> /dev/null; then
        echo "安装PortAudio开发包..."
        sudo apt-get update
        sudo apt-get install -y portaudio19-dev python3-pyaudio python3-dev
    elif command -v yum &> /dev/null; then
        echo "安装PortAudio开发包..."
        sudo yum install -y portaudio-devel python3-devel
    elif command -v dnf &> /dev/null; then
        echo "安装PortAudio开发包..."
        sudo dnf install -y portaudio-devel python3-devel
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "检测到macOS系统"
    # 检查是否有brew
    if command -v brew &> /dev/null; then
        echo "安装PortAudio..."
        brew install portaudio
    else
        echo -e "${YELLOW}⚠️  建议安装Homebrew来管理依赖包${NC}"
        echo "安装命令: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    fi
fi

echo -e "${YELLOW}📦 升级pip...${NC}"
$PYTHON_CMD -m pip install --upgrade pip

echo -e "${YELLOW}📦 安装依赖包...${NC}"
echo "正在安装基础包..."
$PYTHON_CMD -m pip install numpy scipy matplotlib colorama tqdm

echo "正在安装音频处理包..."
$PYTHON_CMD -m pip install sounddevice librosa soundfile

echo "正在尝试安装PyAudio..."
if ! $PYTHON_CMD -m pip install pyaudio; then
    echo -e "${YELLOW}⚠️  PyAudio安装失败，但程序仍可运行${NC}"
fi

echo "正在安装其他包..."
$PYTHON_CMD -m pip install pydub

echo
echo -e "${YELLOW}🔧 验证安装...${NC}"
$PYTHON_CMD test_installation.py

if [ $? -ne 0 ]; then
    echo
    echo -e "${RED}❌ 安装验证失败，请检查错误信息${NC}"
    exit 1
fi

echo
echo -e "${GREEN}🎉 安装完成！${NC}"
echo
echo "使用方法:"
echo "  $PYTHON_CMD main.py          - 运行完整测试"
echo "  $PYTHON_CMD test_installation.py - 验证安装"
echo

# 使脚本可执行
chmod +x "$0"
