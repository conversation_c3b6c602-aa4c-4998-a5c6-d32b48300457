"""
降噪功能测试模块
"""
import numpy as np
from scipy import signal
from scipy.fft import fft, ifft
import librosa
import logging
from typing import Dict, Tuple
from config import DEVICE_CONFIG, TEST_CONFIG

class NoiseSuppressionTester:
    """降噪功能测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sample_rate = DEVICE_CONFIG['sample_rate']
        
    def spectral_subtraction(self, noisy_signal: np.ndarray, noise_estimate: np.ndarray, 
                           alpha: float = 2.0, beta: float = 0.01) -> np.ndarray:
        """谱减法降噪"""
        # 短时傅里叶变换
        f, t, stft_noisy = signal.stft(noisy_signal, self.sample_rate, nperseg=512)
        f_noise, t_noise, stft_noise = signal.stft(noise_estimate, self.sample_rate, nperseg=512)
        
        # 计算噪声功率谱
        noise_power = np.mean(np.abs(stft_noise) ** 2, axis=1, keepdims=True)
        
        # 计算增益函数
        noisy_power = np.abs(stft_noisy) ** 2
        gain = 1 - alpha * (noise_power / noisy_power)
        
        # 限制增益的最小值
        gain = np.maximum(gain, beta)
        
        # 应用增益
        enhanced_stft = stft_noisy * gain
        
        # 逆短时傅里叶变换
        _, enhanced_signal = signal.istft(enhanced_stft, self.sample_rate)
        
        return enhanced_signal
    
    def wiener_filter(self, noisy_signal: np.ndarray, noise_estimate: np.ndarray) -> np.ndarray:
        """维纳滤波降噪"""
        # FFT
        noisy_fft = fft(noisy_signal)
        noise_fft = fft(noise_estimate[:len(noisy_signal)])
        
        # 估计信号和噪声功率谱
        noisy_power = np.abs(noisy_fft) ** 2
        noise_power = np.abs(noise_fft) ** 2
        
        # 估计干净信号功率谱
        signal_power = np.maximum(noisy_power - noise_power, 0.1 * noisy_power)
        
        # 维纳滤波器
        wiener_gain = signal_power / (signal_power + noise_power)
        
        # 应用滤波器
        enhanced_fft = noisy_fft * wiener_gain
        enhanced_signal = np.real(ifft(enhanced_fft))
        
        return enhanced_signal
    
    def test_noise_suppression(self, clean_audio: np.ndarray, noisy_audio: np.ndarray) -> Dict:
        """测试降噪性能"""
        self.logger.info("开始测试降噪性能...")
        
        results = {}
        
        # 确保两个音频数组有相同的长度和通道数
        min_length = min(clean_audio.shape[0], noisy_audio.shape[0])
        min_channels = min(clean_audio.shape[1], noisy_audio.shape[1])
        
        # 截取到相同的长度
        clean_audio = clean_audio[:min_length, :min_channels]
        noisy_audio = noisy_audio[:min_length, :min_channels]
        
        self.logger.info(f"处理音频: 长度={min_length}, 通道数={min_channels}")
        
        for channel in range(min_channels):
            try:
                clean_ch = clean_audio[:, channel]
                noisy_ch = noisy_audio[:, channel]
                
                # 检查音频数据是否有效
                if np.max(np.abs(clean_ch)) < 1e-6 or np.max(np.abs(noisy_ch)) < 1e-6:
                    self.logger.warning(f"通道 {channel} 音频数据过小，跳过降噪测试")
                    results[f'channel_{channel}'] = {
                        'original_snr': float('nan'),
                        'spectral_subtraction': {
                            'snr_improvement': 0.0,
                            'pesq_score': 1.0,
                            'spectral_distortion': float('inf')
                        },
                        'wiener_filter': {
                            'snr_improvement': 0.0,
                            'pesq_score': 1.0,
                            'spectral_distortion': float('inf')
                        }
                    }
                    continue
                
                # 估计噪声（使用前0.5秒作为噪声估计）
                noise_samples = int(0.5 * self.sample_rate)
                noise_samples = min(noise_samples, len(noisy_ch) // 4)  # 确保不超过信号长度的1/4
                
                if noise_samples < 1024:  # 至少需要足够的样本
                    noise_samples = min(1024, len(noisy_ch) // 2)
                
                noise_estimate = noisy_ch[:noise_samples]
                
                # 应用不同的降噪算法
                try:
                    enhanced_spectral = self.spectral_subtraction(noisy_ch, noise_estimate)
                    enhanced_wiener = self.wiener_filter(noisy_ch, noise_estimate)
                    
                    # 确保输出长度与输入一致
                    min_output_len = min(len(clean_ch), len(enhanced_spectral), len(enhanced_wiener))
                    clean_ch = clean_ch[:min_output_len]
                    enhanced_spectral = enhanced_spectral[:min_output_len]
                    enhanced_wiener = enhanced_wiener[:min_output_len]
                    
                    # 计算性能指标
                    metrics = {
                        'original_snr': self._calculate_snr(clean_ch, noisy_ch[:min_output_len]),
                        'spectral_subtraction': {
                            'snr_improvement': self._calculate_snr_improvement(clean_ch, noisy_ch[:min_output_len], enhanced_spectral),
                            'pesq_score': self._estimate_pesq(clean_ch, enhanced_spectral),
                            'spectral_distortion': self._calculate_spectral_distortion(clean_ch, enhanced_spectral)
                        },
                        'wiener_filter': {
                            'snr_improvement': self._calculate_snr_improvement(clean_ch, noisy_ch[:min_output_len], enhanced_wiener),
                            'pesq_score': self._estimate_pesq(clean_ch, enhanced_wiener),
                            'spectral_distortion': self._calculate_spectral_distortion(clean_ch, enhanced_wiener)
                        }
                    }
                    
                except Exception as e:
                    self.logger.warning(f"通道 {channel} 降噪算法执行失败: {e}")
                    metrics = {
                        'original_snr': float('nan'),
                        'spectral_subtraction': {
                            'snr_improvement': 0.0,
                            'pesq_score': 1.0,
                            'spectral_distortion': float('inf')
                        },
                        'wiener_filter': {
                            'snr_improvement': 0.0,
                            'pesq_score': 1.0,
                            'spectral_distortion': float('inf')
                        }
                    }
                
                results[f'channel_{channel}'] = metrics
                
            except Exception as e:
                self.logger.error(f"处理通道 {channel} 时发生错误: {e}")
                results[f'channel_{channel}'] = {
                    'original_snr': float('nan'),
                    'spectral_subtraction': {
                        'snr_improvement': 0.0,
                        'pesq_score': 1.0,
                        'spectral_distortion': float('inf')
                    },
                    'wiener_filter': {
                        'snr_improvement': 0.0,
                        'pesq_score': 1.0,
                        'spectral_distortion': float('inf')
                    }
                }
        
        return results
    
    def _calculate_snr(self, clean_signal: np.ndarray, noisy_signal: np.ndarray) -> float:
        """计算信噪比"""
        noise = noisy_signal - clean_signal
        signal_power = np.mean(clean_signal ** 2)
        noise_power = np.mean(noise ** 2)
        
        if noise_power > 0:
            snr = 10 * np.log10(signal_power / noise_power)
        else:
            snr = float('inf')
        
        return float(snr)
    
    def _calculate_snr_improvement(self, clean_signal: np.ndarray, 
                                 noisy_signal: np.ndarray, 
                                 enhanced_signal: np.ndarray) -> float:
        """计算信噪比改善"""
        original_snr = self._calculate_snr(clean_signal, noisy_signal)
        enhanced_snr = self._calculate_snr(clean_signal, enhanced_signal)
        
        return float(enhanced_snr - original_snr)
    
    def _estimate_pesq(self, reference: np.ndarray, degraded: np.ndarray) -> float:
        """估计PESQ分数（简化版本）"""
        # 这是一个简化的PESQ估计，实际PESQ需要专门的库
        # 基于相关性和均方误差的简单估计
        
        # 确保信号长度一致
        min_len = min(len(reference), len(degraded))
        ref = reference[:min_len]
        deg = degraded[:min_len]
        
        # 归一化
        ref = ref / (np.std(ref) + 1e-10)
        deg = deg / (np.std(deg) + 1e-10)
        
        # 计算相关性
        correlation = np.corrcoef(ref, deg)[0, 1]
        
        # 计算均方误差
        mse = np.mean((ref - deg) ** 2)
        
        # 简化的PESQ估计 (1-5分)
        pesq_estimate = 1 + 4 * correlation * np.exp(-mse)
        
        return float(np.clip(pesq_estimate, 1, 5))
    
    def _calculate_spectral_distortion(self, reference: np.ndarray, 
                                     processed: np.ndarray) -> float:
        """计算频谱失真"""
        # 计算功率谱密度
        f_ref, psd_ref = signal.welch(reference, self.sample_rate, nperseg=512)
        f_proc, psd_proc = signal.welch(processed, self.sample_rate, nperseg=512)
        
        # 转换为dB
        psd_ref_db = 10 * np.log10(psd_ref + 1e-10)
        psd_proc_db = 10 * np.log10(psd_proc + 1e-10)
        
        # 计算频谱失真
        spectral_distortion = np.mean((psd_ref_db - psd_proc_db) ** 2)
        
        return float(spectral_distortion)
    
    def test_adaptive_noise_cancellation(self, primary_input: np.ndarray, 
                                       reference_input: np.ndarray) -> Dict:
        """测试自适应噪声消除"""
        self.logger.info("测试自适应噪声消除...")
        
        results = {}
        
        for channel in range(min(primary_input.shape[1], reference_input.shape[1])):
            primary = primary_input[:, channel]
            reference = reference_input[:, channel]
            
            # LMS自适应滤波器
            enhanced_lms = self._lms_adaptive_filter(primary, reference)
            
            # 计算性能
            noise_reduction = self._calculate_noise_reduction(primary, enhanced_lms)
            
            results[f'channel_{channel}'] = {
                'noise_reduction_db': noise_reduction,
                'residual_noise_level': float(np.std(enhanced_lms)),
                'convergence_time': self._estimate_convergence_time(primary, reference)
            }
        
        return results
    
    def _lms_adaptive_filter(self, primary: np.ndarray, reference: np.ndarray, 
                           filter_length: int = 64, mu: float = 0.01) -> np.ndarray:
        """LMS自适应滤波器"""
        N = len(primary)
        w = np.zeros(filter_length)  # 滤波器系数
        enhanced = np.zeros(N)
        
        for n in range(filter_length, N):
            # 参考信号向量
            x = reference[n-filter_length:n][::-1]
            
            # 滤波器输出
            y = np.dot(w, x)
            
            # 误差信号
            e = primary[n] - y
            enhanced[n] = e
            
            # 更新滤波器系数
            w = w + mu * e * x
        
        return enhanced
    
    def _calculate_noise_reduction(self, original: np.ndarray, enhanced: np.ndarray) -> float:
        """计算噪声减少量"""
        original_power = np.mean(original ** 2)
        enhanced_power = np.mean(enhanced ** 2)
        
        if enhanced_power > 0:
            reduction_db = 10 * np.log10(original_power / enhanced_power)
        else:
            reduction_db = float('inf')
        
        return float(reduction_db)
    
    def _estimate_convergence_time(self, primary: np.ndarray, reference: np.ndarray) -> float:
        """估计收敛时间"""
        # 简化的收敛时间估计
        # 基于信号的自相关特性
        autocorr = np.correlate(reference, reference, mode='full')
        autocorr = autocorr[len(autocorr)//2:]
        
        # 找到自相关函数衰减到峰值10%的点
        peak_val = autocorr[0]
        threshold = 0.1 * peak_val
        
        convergence_samples = np.where(autocorr < threshold)[0]
        if len(convergence_samples) > 0:
            convergence_time = convergence_samples[0] / self.sample_rate
        else:
            convergence_time = 1.0  # 默认1秒
        
        return float(convergence_time)
