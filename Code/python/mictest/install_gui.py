#!/usr/bin/env python3
"""
GUI版本依赖安装脚本
"""
import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("🔧 安装GUI版本依赖包...")
    print("=" * 50)
    
    # 基础包列表
    packages = [
        "PyQt6",
        "PyQt6-Qt6", 
        "matplotlib",
        "numpy",
        "scipy",
        "sounddevice",
        "librosa",
        "soundfile",
        "colorama",
        "tqdm"
    ]
    
    # 可选包
    optional_packages = [
        "pyaudio",
        "pydub",
        "pyqtgraph"
    ]
    
    success_count = 0
    failed_packages = []
    
    # 安装基础包
    print("\n📦 安装基础包...")
    for package in packages:
        print(f"正在安装 {package}...", end=" ")
        if install_package(package):
            print("✅ 成功")
            success_count += 1
        else:
            print("❌ 失败")
            failed_packages.append(package)
    
    # 安装可选包
    print("\n📦 安装可选包...")
    for package in optional_packages:
        print(f"正在安装 {package}...", end=" ")
        if install_package(package):
            print("✅ 成功")
        else:
            print("⚠️  跳过")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 安装总结:")
    print(f"成功安装: {success_count}/{len(packages)} 个基础包")
    
    if failed_packages:
        print(f"失败的包: {', '.join(failed_packages)}")
        print("\n❌ 安装未完全成功，请手动安装失败的包")
        print("手动安装命令:")
        for pkg in failed_packages:
            print(f"  pip install {pkg}")
        return False
    else:
        print("✅ 所有基础包安装成功！")
        print("\n🚀 现在可以运行GUI版本:")
        print("  python run_gui.py")
        print("  或双击 run_gui.bat")
        return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
