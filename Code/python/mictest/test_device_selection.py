#!/usr/bin/env python3
"""
设备选择功能测试脚本
"""
import sys
import time
from colorama import init, Fore, Style
from device_detector import DeviceDetector

# 初始化colorama
init(autoreset=True)

def test_device_detection():
    """测试设备检测功能"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.YELLOW}           设备选择功能测试")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    detector = DeviceDetector()
    
    # 测试1: 列出所有设备
    print(f"\n{Fore.GREEN}测试1: 列出所有设备{Style.RESET_ALL}")
    devices = detector.list_all_devices()
    print(f"检测到 {len(devices)} 个音频设备")
    
    for i, device in enumerate(devices[:5]):  # 只显示前5个
        status = "✅" if device['input_channels'] >= 6 else "❌"
        print(f"  {status} [{i:2d}] {device['name'][:40]:<40} "
              f"输入:{device['input_channels']:2d}")
    
    if len(devices) > 5:
        print(f"  ... 还有 {len(devices)-5} 个设备")
    
    # 测试2: 自动检测讯飞设备
    print(f"\n{Fore.GREEN}测试2: 自动检测讯飞设备{Style.RESET_ALL}")
    xunfei_device = detector.detect_xunfei_device()
    
    if xunfei_device:
        print(f"✅ 找到讯飞设备: {xunfei_device['name']}")
        print(f"   索引: {xunfei_device['index']}")
        print(f"   通道: {xunfei_device['channels']}")
    else:
        print(f"❌ 未找到讯飞设备")
    
    # 测试3: 手动选择设备
    print(f"\n{Fore.GREEN}测试3: 手动选择设备{Style.RESET_ALL}")
    
    # 找到一个合适的设备进行测试
    suitable_device_index = None
    for i, device in enumerate(devices):
        if device['input_channels'] >= 2:
            suitable_device_index = i
            break
    
    if suitable_device_index is not None:
        selected_device = detector.select_device_manually(suitable_device_index)
        if selected_device:
            print(f"✅ 成功选择设备 [{suitable_device_index}]: {selected_device['name']}")
        else:
            print(f"❌ 选择设备失败")
    else:
        print(f"⚠️  没有合适的设备进行测试")
    
    # 测试4: 设备连接测试
    if suitable_device_index is not None and detector.device_info:
        print(f"\n{Fore.GREEN}测试4: 设备连接测试{Style.RESET_ALL}")
        
        try:
            connection_ok = detector.test_device_connection()
            if connection_ok:
                print(f"✅ 设备连接测试通过")
            else:
                print(f"❌ 设备连接测试失败")
        except Exception as e:
            print(f"❌ 连接测试异常: {e}")
    
    # 测试5: 获取设备能力
    if detector.device_info:
        print(f"\n{Fore.GREEN}测试5: 获取设备能力{Style.RESET_ALL}")
        
        try:
            capabilities = detector.get_device_capabilities()
            print(f"设备名称: {capabilities.get('device_name', 'N/A')}")
            print(f"最大通道: {capabilities.get('max_channels', 'N/A')}")
            print(f"默认采样率: {capabilities.get('default_sample_rate', 'N/A')}")
            
            supported_rates = capabilities.get('supported_sample_rates', [])
            if supported_rates:
                print(f"支持的采样率: {supported_rates}")
            else:
                print(f"支持的采样率: 检测失败")
                
        except Exception as e:
            print(f"❌ 获取能力信息异常: {e}")
    
    # 测试6: 获取合适的设备列表
    print(f"\n{Fore.GREEN}测试6: 获取合适的设备{Style.RESET_ALL}")
    
    try:
        suitable_devices = detector.get_suitable_devices()
        recommended_count = sum(1 for d in suitable_devices if d['is_suitable'])
        
        print(f"找到 {len(suitable_devices)} 个多通道设备")
        print(f"其中 {recommended_count} 个推荐设备（≥6通道）")
        
        for device in suitable_devices[:3]:  # 显示前3个
            status = "推荐" if device['is_suitable'] else "可用"
            print(f"  [{device['index']:2d}] {device['name'][:30]:<30} "
                  f"输入:{device['input_channels']:2d} ({status})")
    
    except Exception as e:
        print(f"❌ 获取合适设备异常: {e}")
    
    return len(devices), xunfei_device is not None

def test_edge_cases():
    """测试边缘情况"""
    print(f"\n{Fore.CYAN}测试边缘情况{Style.RESET_ALL}")
    
    detector = DeviceDetector()
    
    # 测试无效索引
    print(f"\n{Fore.YELLOW}测试无效设备索引{Style.RESET_ALL}")
    invalid_device = detector.select_device_manually(-1)
    if invalid_device is None:
        print(f"✅ 正确处理了无效索引 -1")
    else:
        print(f"❌ 未正确处理无效索引")
    
    # 测试超大索引
    large_index = 9999
    invalid_device = detector.select_device_manually(large_index)
    if invalid_device is None:
        print(f"✅ 正确处理了超大索引 {large_index}")
    else:
        print(f"❌ 未正确处理超大索引")

def print_summary(total_devices, found_xunfei):
    """打印测试总结"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.YELLOW}              测试总结")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}测试结果:{Style.RESET_ALL}")
    print(f"  📱 检测到设备数量: {total_devices}")
    print(f"  🎯 找到讯飞设备: {'是' if found_xunfei else '否'}")
    
    if total_devices > 0:
        print(f"  ✅ 设备检测功能正常")
    else:
        print(f"  ⚠️  没有检测到音频设备")
    
    print(f"\n{Fore.GREEN}使用建议:{Style.RESET_ALL}")
    
    if found_xunfei:
        print(f"  🎉 系统中有讯飞设备，可以直接运行:")
        print(f"     python3 main.py")
    else:
        print(f"  💡 没有检测到讯飞设备，建议手动选择:")
        print(f"     python3 main.py --list-devices")
        print(f"     python3 main.py -d 设备索引")
        print(f"     python3 device_selector.py")
    
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

def main():
    """主函数"""
    try:
        print(f"{Fore.CYAN}开始设备选择功能测试...{Style.RESET_ALL}")
        
        # 执行主要测试
        total_devices, found_xunfei = test_device_detection()
        
        # 测试边缘情况
        test_edge_cases()
        
        # 打印总结
        print_summary(total_devices, found_xunfei)
        
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}测试被用户中断{Style.RESET_ALL}")
    except Exception as e:
        print(f"\n{Fore.RED}测试发生异常: {e}{Style.RESET_ALL}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 