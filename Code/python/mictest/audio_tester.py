"""
音频测试模块
"""
import numpy as np
import sounddevice as sd
import librosa
import matplotlib.pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq
import logging
import os
from typing import Tuple, List, Dict
from config import DEVICE_CONFIG, TEST_CONFIG, OUTPUT_CONFIG

# 设置matplotlib字体，避免中文显示问题
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置matplotlib字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class AudioTester:
    """音频测试器"""
    
    def __init__(self, device_index: int):
        self.device_index = device_index
        self.logger = logging.getLogger(__name__)
        self.sample_rate = DEVICE_CONFIG['sample_rate']
        self.channels = DEVICE_CONFIG['channels']
        
        # 创建输出目录
        if OUTPUT_CONFIG['save_audio']:
            os.makedirs(OUTPUT_CONFIG['output_dir'], exist_ok=True)
    
    def record_audio(self, duration: float = None) -> np.ndarray:
        """录制音频"""
        if duration is None:
            duration = DEVICE_CONFIG['record_duration']
            
        self.logger.info(f"开始录音 {duration} 秒...")
        
        try:
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=self.channels,
                device=self.device_index,
                dtype='float32'
            )
            sd.wait()  # 等待录音完成
            
            self.logger.info("录音完成")
            return audio_data
            
        except Exception as e:
            self.logger.error(f"录音失败: {e}")
            return np.array([])
    
    def test_channel_functionality(self) -> Dict[int, Dict]:
        """测试各通道功能"""
        self.logger.info("开始测试各通道功能...")
        
        results = {}
        audio_data = self.record_audio()
        
        if audio_data.size == 0:
            return results
        
        for channel in range(self.channels):
            channel_data = audio_data[:, channel]
            
            # 计算基本统计信息
            rms = np.sqrt(np.mean(channel_data ** 2))
            peak = np.max(np.abs(channel_data))
            
            # 频谱分析
            freqs, psd = signal.welch(
                channel_data, 
                self.sample_rate, 
                nperseg=1024
            )
            
            # 计算信噪比（更安全的计算）
            signal_indices = (freqs >= TEST_CONFIG['frequency_range'][0]) & (freqs <= TEST_CONFIG['frequency_range'][1])
            noise_indices = freqs < 100  # 低频噪声
            
            signal_power = np.mean(psd[signal_indices]) if np.any(signal_indices) else 0
            noise_power = np.mean(psd[noise_indices]) if np.any(noise_indices) else 1e-10
            
            if signal_power > 0 and noise_power > 0:
                snr = 10 * np.log10(signal_power / noise_power)
            else:
                snr = float('inf')
            
            results[channel] = {
                'rms': float(rms),
                'peak': float(peak),
                'snr_db': float(snr),
                'is_active': rms > TEST_CONFIG['volume_threshold'],
                'frequency_response': (freqs.tolist(), psd.tolist())
            }
            
            self.logger.info(f"通道 {channel}: RMS={rms:.4f}, Peak={peak:.4f}, SNR={snr:.1f}dB")
        
        return results
    
    def test_audio_quality(self, audio_data: np.ndarray) -> Dict:
        """测试音频质量"""
        self.logger.info("开始音频质量测试...")
        
        quality_metrics = {}
        
        for channel in range(self.channels):
            channel_data = audio_data[:, channel]
            
            # 检查数据是否有效
            if np.max(np.abs(channel_data)) < 1e-6:
                self.logger.warning(f"通道 {channel} 音频数据过小，跳过质量测试")
                quality_metrics[channel] = {
                    'thd_n_percent': float('nan'),
                    'dynamic_range_db': float('nan'),
                    'frequency_flatness_db': float('nan')
                }
                continue
            
            try:
                # THD+N (总谐波失真加噪声)
                thd_n = self._calculate_thd_n(channel_data)
                
                # 动态范围
                dynamic_range = self._calculate_dynamic_range(channel_data)
                
                # 频率响应平坦度
                flatness = self._calculate_frequency_flatness(channel_data)
                
                quality_metrics[channel] = {
                    'thd_n_percent': thd_n,
                    'dynamic_range_db': dynamic_range,
                    'frequency_flatness_db': flatness
                }
            except Exception as e:
                self.logger.warning(f"通道 {channel} 质量测试失败: {e}")
                quality_metrics[channel] = {
                    'thd_n_percent': float('nan'),
                    'dynamic_range_db': float('nan'),
                    'frequency_flatness_db': float('nan')
                }
        
        return quality_metrics
    
    def _calculate_thd_n(self, signal_data: np.ndarray) -> float:
        """计算总谐波失真加噪声"""
        try:
            # 简化的THD+N计算
            fft_data = np.abs(fft(signal_data))
            fft_data = fft_data[1:len(fft_data)//2]  # 去除直流分量，只取一半
            
            if len(fft_data) == 0:
                return float('nan')
            
            fundamental_idx = np.argmax(fft_data)
            fundamental_power = fft_data[fundamental_idx] ** 2
            total_power = np.sum(fft_data ** 2)
            
            if fundamental_power == 0 or total_power == 0:
                return float('nan')
            
            thd_n = np.sqrt(max(0, (total_power - fundamental_power) / fundamental_power)) * 100
            return float(thd_n)
        except:
            return float('nan')
    
    def _calculate_dynamic_range(self, signal_data: np.ndarray) -> float:
        """计算动态范围"""
        try:
            max_abs = np.max(np.abs(signal_data))
            if max_abs <= 0:
                return float('nan')
            
            peak_level = 20 * np.log10(max_abs)
            
            # 更安全的噪声底计算
            quiet_samples = signal_data[np.abs(signal_data) < 0.01]
            if len(quiet_samples) > 0:
                noise_std = np.std(quiet_samples)
                if noise_std > 0:
                    noise_floor = 20 * np.log10(noise_std)
                else:
                    noise_floor = -120  # 默认噪声底
            else:
                noise_floor = -120
            
            return float(peak_level - noise_floor)
        except:
            return float('nan')
    
    def _calculate_frequency_flatness(self, signal_data: np.ndarray) -> float:
        """计算频率响应平坦度"""
        try:
            freqs, psd = signal.welch(signal_data, self.sample_rate, nperseg=1024)
            
            # 在有效频率范围内计算平坦度
            valid_idx = (freqs >= TEST_CONFIG['frequency_range'][0]) & (freqs <= TEST_CONFIG['frequency_range'][1])
            valid_psd = psd[valid_idx]
            
            if len(valid_psd) > 0 and np.any(valid_psd > 0):
                # 防止log(0)
                valid_psd = np.maximum(valid_psd, 1e-12)
                flatness = np.std(10 * np.log10(valid_psd))
                return float(flatness)
            return float('nan')
        except:
            return float('nan')
    
    def save_audio_file(self, audio_data: np.ndarray, filename: str):
        """保存音频文件"""
        if OUTPUT_CONFIG['save_audio']:
            filepath = os.path.join(OUTPUT_CONFIG['output_dir'], filename)
            
            # 转换为16位整数格式
            audio_int16 = (audio_data * 32767).astype(np.int16)
            
            # 使用soundfile保存（librosa.output.write_wav已弃用）
            import soundfile as sf
            for channel in range(self.channels):
                channel_file = f"{filepath}_ch{channel}.wav"
                sf.write(
                    channel_file,
                    audio_int16[:, channel],
                    self.sample_rate
                )
            
            self.logger.info(f"音频文件已保存到: {OUTPUT_CONFIG['output_dir']}")
    
    def plot_frequency_response(self, channel_results: Dict[int, Dict]):
        """绘制频率响应图"""
        try:
            plt.figure(figsize=(12, 8))
            
            for channel, result in channel_results.items():
                freqs, psd = result['frequency_response']
                plt.subplot(2, 3, channel + 1)
                
                # 防止log(0)错误
                psd_safe = np.maximum(psd, 1e-12)
                plt.semilogx(freqs, 10 * np.log10(psd_safe))
                plt.title(f'Channel {channel} Frequency Response')  # 使用英文避免字体问题
                plt.xlabel('Frequency (Hz)')
                plt.ylabel('Power Spectral Density (dB)')
                plt.grid(True)
            
            plt.tight_layout()
            
            if OUTPUT_CONFIG['save_audio']:
                plt.savefig(os.path.join(OUTPUT_CONFIG['output_dir'], 'frequency_response.png'), 
                           dpi=150, bbox_inches='tight')
            
            plt.show()
        except Exception as e:
            self.logger.warning(f"绘制频率响应图失败: {e}")
