#!/usr/bin/env python3
"""
远程测试脚本
"""
import sys
import json
import logging
import sounddevice as sd
import numpy as np
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def list_devices() -> List[Dict]:
    """列出所有音频设备"""
    try:
        devices = sd.query_devices()
        device_list = []
        
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:  # 只包含输入设备
                device_info = {
                    'index': i,
                    'name': device['name'],
                    'inputs': device['max_input_channels'],
                    'outputs': device['max_output_channels'],
                    'sample_rate': device['default_samplerate']
                }
                device_list.append(device_info)
                
        return device_list
    except Exception as e:
        logger.error(f"列出设备失败: {e}")
        return []

def test_device(device_index: int, duration: int = 5) -> Dict:
    """测试指定设备"""
    try:
        # 获取设备信息
        device_info = sd.query_devices(device_index)
        
        # 设置录音参数
        sample_rate = int(device_info['default_samplerate'])
        channels = device_info['max_input_channels']
        
        # 录制音频
        logger.info(f"开始录音: 设备 {device_index}, 时长 {duration}秒")
        audio_data = sd.rec(
            int(duration * sample_rate),
            samplerate=sample_rate,
            channels=channels,
            device=device_index
        )
        sd.wait()
        
        # 分析音频
        rms = np.sqrt(np.mean(audio_data**2))
        peak = np.max(np.abs(audio_data))
        
        # 计算每个通道的RMS值
        channel_rms = []
        for ch in range(channels):
            ch_data = audio_data[:, ch]
            ch_rms = np.sqrt(np.mean(ch_data**2))
            channel_rms.append(float(ch_rms))
        
        return {
            'success': True,
            'device_info': {
                'index': device_index,
                'name': device_info['name'],
                'inputs': channels,
                'sample_rate': sample_rate
            },
            'audio_analysis': {
                'rms': float(rms),
                'peak': float(peak),
                'channel_rms': channel_rms
            }
        }
        
    except Exception as e:
        logger.error(f"测试设备失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """主函数"""
    try:
        # 解析命令行参数
        if len(sys.argv) < 2:
            print(json.dumps({
                'success': False,
                'error': '缺少参数'
            }))
            return
            
        # 解析JSON参数
        params = json.loads(sys.argv[1])
        test_type = params.get('test_type')
        
        if test_type == 'device_detection':
            # 列出所有设备
            devices = list_devices()
            print(json.dumps({
                'success': True,
                'devices': devices
            }))
            
        elif test_type == 'basic_test':
            # 基本测试
            device_index = params.get('device_index')
            duration = params.get('duration', 5)
            
            if device_index is None:
                print(json.dumps({
                    'success': False,
                    'error': '未指定设备索引'
                }))
                return
                
            result = test_device(device_index, duration)
            print(json.dumps(result))
            
        else:
            print(json.dumps({
                'success': False,
                'error': f'未知的测试类型: {test_type}'
            }))
            
    except Exception as e:
        logger.error(f"执行测试失败: {e}")
        print(json.dumps({
            'success': False,
            'error': str(e)
        }))

if __name__ == '__main__':
    main() 