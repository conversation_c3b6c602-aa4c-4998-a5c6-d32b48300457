#!/bin/bash
# 激活虚拟环境并设置正确的PYTHONPATH

# 激活虚拟环境
source .venv/bin/activate

# 设置PYTHONPATH以确保能找到已安装的包
export PYTHONPATH="/mine/code/python/mictest/.venv/lib/python3.13/site-packages:$PYTHONPATH"

echo "✅ 虚拟环境已激活并配置完成"
echo "当前Python版本: $(python --version)"
echo "虚拟环境路径: $VIRTUAL_ENV"

# 验证主要包是否可用
python -c "
try:
    import numpy, scipy, matplotlib, sounddevice, librosa, PyQt6
    print('✅ 所有依赖包都可以正常导入')
except ImportError as e:
    print('❌ 导入错误:', e)
" 