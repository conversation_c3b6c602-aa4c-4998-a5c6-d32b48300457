#!/usr/bin/env python3
"""
远程麦克风测试运行器
在远程设备上执行麦克风功能测试
"""
import argparse
import json
import sys
import os
import logging
import time
from datetime import datetime

# 尝试导入必要的包
try:
    import numpy as np
    import sounddevice as sd
    from scipy import signal
    from scipy.fft import fft, fftfreq
except ImportError as e:
    print(f"错误: 缺少必要的Python包: {e}")
    print("请运行: python3 -m pip install numpy scipy sounddevice")
    sys.exit(1)

class RemoteAudioTester:
    """远程音频测试器"""
    
    def __init__(self, device_index=None):
        self.device_index = device_index
        self.sample_rate = 16000
        self.channels = 6
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def list_devices(self):
        """列出所有音频设备"""
        try:
            devices = sd.query_devices()
            device_list = []
            
            for i, device in enumerate(devices):
                device_info = {
                    'index': i,
                    'name': device['name'],
                    'inputs': device['max_input_channels'],
                    'outputs': device['max_output_channels'],
                    'sample_rate': device['default_samplerate']
                }
                device_list.append(device_info)
            
            return device_list
        except Exception as e:
            self.logger.error(f"获取设备列表失败: {e}")
            return []
    
    def auto_detect_device(self):
        """自动检测6麦设备"""
        devices = self.list_devices()
        
        # 寻找输入通道数>=6的设备
        for device in devices:
            if device['inputs'] >= 6:
                return device['index']
        
        return None
    
    def test_device_connection(self, device_index):
        """测试设备连接"""
        try:
            # 尝试短时间录音测试连接
            test_data = sd.rec(
                int(0.1 * self.sample_rate),  # 0.1秒
                samplerate=self.sample_rate,
                channels=min(self.channels, 2),  # 先测试2通道
                device=device_index,
                dtype='float32'
            )
            sd.wait()
            
            return test_data.size > 0
        except Exception as e:
            self.logger.error(f"设备连接测试失败: {e}")
            return False
    
    def record_audio(self, duration=5, device_index=None):
        """录制音频"""
        if device_index is None:
            device_index = self.device_index
        
        try:
            self.logger.info(f"开始录音 {duration} 秒...")
            
            # 获取设备信息
            device_info = sd.query_devices(device_index)
            actual_channels = min(self.channels, device_info['max_input_channels'])
            
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=actual_channels,
                device=device_index,
                dtype='float32'
            )
            sd.wait()
            
            self.logger.info(f"录音完成，数据形状: {audio_data.shape}")
            return audio_data
            
        except Exception as e:
            self.logger.error(f"录音失败: {e}")
            return np.array([])
    
    def analyze_channels(self, audio_data):
        """分析各通道音频"""
        if audio_data.size == 0:
            return {}
        
        results = {}
        num_channels = audio_data.shape[1] if len(audio_data.shape) > 1 else 1
        
        if num_channels == 1:
            # 单通道数据
            audio_data = audio_data.reshape(-1, 1)
            num_channels = 1
        
        for channel in range(num_channels):
            channel_data = audio_data[:, channel]
            
            # 基本统计
            rms = float(np.sqrt(np.mean(channel_data ** 2)))
            peak = float(np.max(np.abs(channel_data)))
            
            # 频谱分析
            try:
                freqs, psd = signal.welch(
                    channel_data, 
                    self.sample_rate, 
                    nperseg=1024
                )
                
                # 计算信噪比
                signal_band = (freqs >= 300) & (freqs <= 3400)  # 语音频段
                noise_band = freqs < 100  # 低频噪声
                
                signal_power = np.mean(psd[signal_band]) if np.any(signal_band) else 0
                noise_power = np.mean(psd[noise_band]) if np.any(noise_band) else 1e-10
                
                snr = 10 * np.log10(signal_power / noise_power) if signal_power > 0 else -60
                
            except Exception as e:
                self.logger.warning(f"通道 {channel} 频谱分析失败: {e}")
                snr = -60
            
            results[channel] = {
                'rms': rms,
                'peak': peak,
                'snr_db': float(snr),
                'is_active': rms > 0.001  # 活跃阈值
            }
        
        return results
    
    def test_audio_quality(self, audio_data):
        """测试音频质量"""
        if audio_data.size == 0:
            return {}
        
        quality_results = {}
        num_channels = audio_data.shape[1] if len(audio_data.shape) > 1 else 1
        
        if num_channels == 1:
            audio_data = audio_data.reshape(-1, 1)
            num_channels = 1
        
        for channel in range(num_channels):
            channel_data = audio_data[:, channel]
            
            try:
                # 简化的质量指标
                
                # 动态范围
                max_val = np.max(np.abs(channel_data))
                if max_val > 0:
                    dynamic_range = 20 * np.log10(max_val / (np.std(channel_data) + 1e-10))
                else:
                    dynamic_range = 0
                
                # 频率响应平坦度（简化计算）
                freqs, psd = signal.welch(channel_data, self.sample_rate, nperseg=512)
                speech_band = (freqs >= 300) & (freqs <= 3400)
                if np.any(speech_band):
                    speech_psd = psd[speech_band]
                    flatness = np.std(10 * np.log10(speech_psd + 1e-10))
                else:
                    flatness = 0
                
                quality_results[channel] = {
                    'dynamic_range_db': float(dynamic_range),
                    'frequency_flatness_db': float(flatness),
                    'max_amplitude': float(max_val)
                }
                
            except Exception as e:
                self.logger.warning(f"通道 {channel} 质量分析失败: {e}")
                quality_results[channel] = {
                    'dynamic_range_db': 0,
                    'frequency_flatness_db': 0,
                    'max_amplitude': 0
                }
        
        return quality_results
    
    def save_audio_file(self, audio_data, filename):
        """保存音频文件"""
        try:
            import soundfile as sf
            
            wav_filename = f"{filename}.wav"
            sf.write(wav_filename, audio_data, self.sample_rate)
            self.logger.info(f"音频文件已保存: {wav_filename}")
            return wav_filename
        except ImportError:
            self.logger.warning("soundfile包不可用，跳过音频文件保存")
            return None
        except Exception as e:
            self.logger.error(f"保存音频文件失败: {e}")
            return None
    
    def run_complete_test(self, device_index=None, duration=5):
        """运行完整测试"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'test_parameters': {
                'duration': duration,
                'sample_rate': self.sample_rate,
                'target_channels': self.channels
            },
            'device_info': {},
            'channel_results': {},
            'quality_results': {},
            'summary': {}
        }
        
        try:
            # 设备检测
            if device_index is None:
                device_index = self.auto_detect_device()
                if device_index is None:
                    results['error'] = "未找到合适的6麦设备"
                    return results
            
            self.device_index = device_index
            
            # 获取设备信息
            device_info = sd.query_devices(device_index)
            results['device_info'] = {
                'index': device_index,
                'name': device_info['name'],
                'max_input_channels': device_info['max_input_channels'],
                'max_output_channels': device_info['max_output_channels'],
                'default_samplerate': device_info['default_samplerate']
            }
            
            # 测试设备连接
            if not self.test_device_connection(device_index):
                results['error'] = "设备连接测试失败"
                return results
            
            # 录制音频
            audio_data = self.record_audio(duration, device_index)
            if audio_data.size == 0:
                results['error'] = "录音失败"
                return results
            
            # 保存音频文件
            audio_filename = self.save_audio_file(audio_data, f"remote_test_{int(time.time())}")
            if audio_filename:
                results['audio_file'] = audio_filename
            
            # 分析各通道
            results['channel_results'] = self.analyze_channels(audio_data)
            
            # 音频质量测试
            results['quality_results'] = self.test_audio_quality(audio_data)
            
            # 生成测试摘要
            channel_results = results['channel_results']
            active_channels = sum(1 for r in channel_results.values() if r['is_active'])
            avg_snr = np.mean([r['snr_db'] for r in channel_results.values()])
            
            results['summary'] = {
                'total_channels_tested': len(channel_results),
                'active_channels': active_channels,
                'average_snr_db': float(avg_snr),
                'test_passed': active_channels >= 4 and avg_snr > 10  # 简单的通过标准
            }
            
            return results
            
        except Exception as e:
            self.logger.error(f"测试执行失败: {e}")
            results['error'] = str(e)
            return results

def main():
    parser = argparse.ArgumentParser(description='远程麦克风测试运行器')
    parser.add_argument('--device', type=int, help='指定设备索引')
    parser.add_argument('--duration', type=int, default=5, help='录音时长（秒）')
    parser.add_argument('--list-devices', action='store_true', help='列出所有设备')
    parser.add_argument('--output', choices=['json', 'text'], default='text', help='输出格式')
    
    args = parser.parse_args()
    
    tester = RemoteAudioTester()
    
    # 列出设备
    if args.list_devices:
        devices = tester.list_devices()
        if args.output == 'json':
            print(json.dumps(devices, indent=2))
        else:
            print("可用音频设备:")
            for device in devices:
                print(f"  [{device['index']}] {device['name']} - "
                      f"输入:{device['inputs']} 输出:{device['outputs']}")
        return
    
    # 运行测试
    results = tester.run_complete_test(args.device, args.duration)
    
    # 输出结果
    if args.output == 'json':
        print(json.dumps(results, indent=2, ensure_ascii=False))
    else:
        # 文本格式输出
        print("\n=== 远程麦克风测试结果 ===")
        
        if 'error' in results:
            print(f"❌ 测试失败: {results['error']}")
            return
        
        device_info = results.get('device_info', {})
        print(f"设备: [{device_info.get('index')}] {device_info.get('name')}")
        print(f"通道数: {device_info.get('max_input_channels')}")
        
        print("\n通道测试结果:")
        for channel, result in results.get('channel_results', {}).items():
            status = "✅" if result['is_active'] else "❌"
            print(f"  {status} 通道 {channel}: RMS={result['rms']:.4f} "
                  f"Peak={result['peak']:.4f} SNR={result['snr_db']:.1f}dB")
        
        summary = results.get('summary', {})
        print(f"\n测试摘要:")
        print(f"  活跃通道: {summary.get('active_channels', 0)}/{summary.get('total_channels_tested', 0)}")
        print(f"  平均SNR: {summary.get('average_snr_db', 0):.1f}dB")
        print(f"  测试结果: {'✅ 通过' if summary.get('test_passed', False) else '❌ 未通过'}")

if __name__ == '__main__':
    main() 