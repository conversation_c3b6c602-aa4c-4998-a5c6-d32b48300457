"""
远程设备检测模块
通过SSH连接远程设备进行麦克风功能检测
"""
import paramiko
import time
import json
import os
import tempfile
import logging
import socket
import sys
from typing import Dict, List, Optional, Tuple
from scp import SCPClient
from colorama import Fore, Style
import threading
import queue
from paramiko.ssh_exception import SSHException, AuthenticationException
import shutil

class RemoteDetector:
    """远程设备检测器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ssh_client = None
        self.sftp_client = None
        self.remote_script_dir = "/tmp/xunfei_mic_test"  # 设置默认远程目录
        self.connection_info = {}
        
    def connect(self, hostname: str, port: int, username: str, 
                password: str = None, private_key_path: str = None) -> bool:
        """连接到远程设备"""
        try:
            print(f"{Fore.YELLOW}🔗 正在连接到远程设备 {hostname}:{port}...{Style.RESET_ALL}")
            self.logger.info(f"尝试连接: {username}@{hostname}:{port}")
            
            # 创建SSH客户端
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 启用日志记录以调试连接问题
            if self.logger.level <= logging.DEBUG:
                # 使用系统临时目录
                log_file = os.path.join(tempfile.gettempdir(), 'paramiko.log')
                paramiko.util.log_to_file(log_file)
            
            # 设置基本连接参数
            connect_kwargs = {
                'hostname': hostname,
                'port': port,
                'username': username,
                'timeout': 10,  # 减少超时时间
                'banner_timeout': 10,
                'auth_timeout': 10,
                'look_for_keys': False,  # 禁用密钥查找以加快连接
                'allow_agent': False,    # 禁用SSH代理
            }
            
            # 选择认证方式
            auth_method = "未知"
            if private_key_path and os.path.exists(private_key_path):
                self.logger.info(f"使用私钥认证: {private_key_path}")
                auth_method = f"私钥({private_key_path})"
                try:
                    # 尝试加载私钥
                    if private_key_path.endswith('.pem') or 'rsa' in private_key_path.lower():
                        key = paramiko.RSAKey.from_private_key_file(private_key_path)
                    elif 'ed25519' in private_key_path.lower():
                        key = paramiko.Ed25519Key.from_private_key_file(private_key_path)
                    elif 'ecdsa' in private_key_path.lower():
                        key = paramiko.ECDSAKey.from_private_key_file(private_key_path)
                    else:
                        # 尝试通用加载
                        key = paramiko.RSAKey.from_private_key_file(private_key_path)
                    
                    connect_kwargs['pkey'] = key
                    self.logger.info("私钥加载成功")
                except Exception as key_error:
                    self.logger.warning(f"私钥加载失败: {key_error}, 回退到文件路径方式")
                    connect_kwargs['key_filename'] = private_key_path
                    
            elif password:
                self.logger.info("使用密码认证")
                auth_method = "密码"
                # 确保密码不为空且去除首尾空格
                password = password.strip()
                if not password:
                    error_msg = "密码不能为空"
                    self.logger.error(error_msg)
                    print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
                    return False
                connect_kwargs['password'] = password
            else:
                # 尝试使用系统默认的SSH密钥
                self.logger.info("尝试使用默认SSH密钥")
                auth_method = "默认密钥"
                ssh_dir = os.path.expanduser("~/.ssh")
                default_keys = ['id_rsa', 'id_ed25519', 'id_ecdsa', 'id_dsa']
                
                found_key = False
                for key_name in default_keys:
                    key_path = os.path.join(ssh_dir, key_name)
                    if os.path.exists(key_path):
                        self.logger.info(f"找到默认密钥: {key_path}")
                        connect_kwargs['key_filename'] = key_path
                        found_key = True
                        break
                
                if not found_key:
                    self.logger.warning("未找到默认SSH密钥，且未提供密码")
                    print(f"{Fore.RED}❌ 未提供有效的认证方式{Style.RESET_ALL}")
                    return False
            
            print(f"  🔑 认证方式: {auth_method}")
            
            # 尝试连接
            self.logger.info(f"开始SSH连接，参数: {connect_kwargs.keys()}")
            start_time = time.time()
            
            try:
                self.ssh_client.connect(**connect_kwargs)
            except AuthenticationException as e:
                error_msg = f"认证失败: {e}"
                self.logger.error(error_msg)
                print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
                print(f"  💡 请检查：")
                print(f"    1. 用户名是否正确")
                if auth_method == "密码":
                    print(f"    2. 密码是否正确")
                    print(f"    3. 密码中是否包含特殊字符")
                elif auth_method.startswith("私钥"):
                    print(f"    2. 私钥文件是否正确")
                    print(f"    3. 私钥是否受密码保护")
                print(f"    4. 用户是否有权限登录")
                return False
            except (SSHException, socket.error) as e:
                error_msg = f"SSH连接失败: {e}"
                self.logger.error(error_msg)
                print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
                print(f"  💡 请检查：")
                print(f"    1. 主机地址和端口是否正确")
                print(f"    2. 网络连接是否正常")
                print(f"    3. 防火墙设置是否允许SSH连接")
                print(f"    4. SSH服务是否在目标主机上运行")
                return False
            
            connect_time = time.time() - start_time
            print(f"  ⏱️  连接耗时: {connect_time:.2f}秒")
            
            # 测试连接
            self.logger.info("测试SSH连接...")
            test_success, test_out, test_err = self.execute_command("echo 'SSH连接测试成功'")
            if not test_success:
                raise Exception(f"连接测试失败: {test_err}")
            
            print(f"  ✅ SSH连接测试: {test_out.strip()}")
            
            # 清理远程进程
            if not self.cleanup_remote_processes():
                self.logger.warning("远程进程清理失败，但继续执行")
            
            # 创建SFTP客户端
            self.sftp_client = self.ssh_client.open_sftp()
            
            # 创建远程脚本目录
            self._create_remote_script_dir()
            
            # 上传测试脚本
            if not self.upload_test_scripts():
                raise Exception("上传测试脚本失败")
            
            # 保存连接信息
            self.connection_info = {
                'hostname': hostname,
                'port': port,
                'username': username,
                'auth_method': auth_method,
                'connected_at': time.time(),
                'connect_time': connect_time
            }
            
            print(f"{Fore.GREEN}✅ 成功连接到远程设备 {username}@{hostname}{Style.RESET_ALL}")
            self.logger.info("远程连接建立成功")
            return True
            
        except Exception as e:
            error_msg = f"连接失败: {e}"
            self.logger.error(error_msg)
            print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
            return False
    
    def _create_remote_script_dir(self):
        """创建远程脚本目录"""
        try:
            self.logger.info(f"创建远程脚本目录: {self.remote_script_dir}")
            
            # 检查目录是否存在
            try:
                # 首先检查目录是否存在
                _, stdout, _ = self.ssh_client.exec_command(f"test -d {self.remote_script_dir} && echo 'exists' || echo 'not exists'")
                dir_exists = stdout.read().decode().strip() == "exists"
                
                if dir_exists:
                    self.logger.info(f"远程目录已存在，将清空内容: {self.remote_script_dir}")
                    # 如果目录存在，清空它的内容
                    self.ssh_client.exec_command(f"rm -rf {self.remote_script_dir}/*")
                else:
                    self.logger.info(f"远程目录不存在，将创建: {self.remote_script_dir}")
                    # 创建新目录
                    _, stdout, stderr = self.ssh_client.exec_command(f"mkdir -p {self.remote_script_dir}")
                    exit_status = stdout.channel.recv_exit_status()
                    if exit_status != 0:
                        error = stderr.read().decode()
                        self.logger.error(f"创建目录失败: {error}")
                        raise Exception(f"创建目录失败: {error}")
                
                # 设置权限
                _, stdout, stderr = self.ssh_client.exec_command(f"chmod 755 {self.remote_script_dir}")
                exit_status = stdout.channel.recv_exit_status()
                if exit_status != 0:
                    error = stderr.read().decode()
                    self.logger.error(f"设置目录权限失败: {error}")
                    
                # 验证目录是否存在
                _, stdout, _ = self.ssh_client.exec_command(f"test -d {self.remote_script_dir} && echo 'verified' || echo 'failed'")
                verified = stdout.read().decode().strip() == "verified"
                if not verified:
                    self.logger.error(f"创建目录后验证失败: {self.remote_script_dir}")
                    raise Exception(f"无法验证远程目录: {self.remote_script_dir}")
                
                # 确认目录确实存在且有正确的权限
                _, stdout, _ = self.ssh_client.exec_command(f"ls -lad {self.remote_script_dir}")
                dir_info = stdout.read().decode().strip()
                self.logger.info(f"远程目录信息: {dir_info}")
                
                # 尝试在目录中创建一个测试文件，验证写入权限
                test_file = f"{self.remote_script_dir}/test_write"
                _, stdout, stderr = self.ssh_client.exec_command(f"echo 'test' > {test_file} && cat {test_file}")
                exit_status = stdout.channel.recv_exit_status()
                test_content = stdout.read().decode().strip()
                if exit_status != 0 or test_content != "test":
                    error = stderr.read().decode()
                    self.logger.error(f"目录写入测试失败: {error}")
                    raise Exception(f"无法在远程目录中写入文件: {self.remote_script_dir}")
                
                # 清理测试文件
                self.ssh_client.exec_command(f"rm -f {test_file}")
                    
                self.logger.info(f"远程目录已准备好: {self.remote_script_dir}")
                return True
                
            except Exception as e:
                self.logger.error(f"处理远程目录时出错: {e}")
                raise
            
        except Exception as e:
            self.logger.error(f"创建远程脚本目录失败: {e}")
            raise
            
    def upload_test_scripts(self) -> bool:
        """上传测试脚本到远程设备"""
        try:
            self.logger.info("开始上传测试脚本...")
            
            # 确保远程目录存在
            self._create_remote_script_dir()
            
            # 测试脚本内容
            test_script_content = """#!/usr/bin/env python3
import sys
import json
import logging
import sounddevice as sd
import numpy as np
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_devices() -> List[Dict]:
    try:
        devices = sd.query_devices()
        device_list = []
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:
                device_info = {
                    'index': i,
                    'name': device['name'],
                    'inputs': device['max_input_channels'],
                    'outputs': device['max_output_channels'],
                    'sample_rate': device['default_samplerate']
                }
                device_list.append(device_info)
        return device_list
    except Exception as e:
        logger.error(f"列出设备失败: {e}")
        return []

def test_device(device_index: int, duration: int = 5) -> Dict:
    try:
        device_info = sd.query_devices(device_index)
        sample_rate = int(device_info['default_samplerate'])
        channels = device_info['max_input_channels']
        logger.info(f"开始录音: 设备 {device_index}, 时长 {duration}秒")
        audio_data = sd.rec(
            int(duration * sample_rate),
            samplerate=sample_rate,
            channels=channels,
            device=device_index
        )
        sd.wait()
        rms = np.sqrt(np.mean(audio_data**2))
        peak = np.max(np.abs(audio_data))
        return {
            'success': True,
            'device_info': {
                'index': device_index,
                'name': device_info['name'],
                'inputs': channels,
                'sample_rate': sample_rate
            },
            'audio_analysis': {
                'rms': float(rms),
                'peak': float(peak)
            }
        }
    except Exception as e:
        logger.error(f"测试设备失败: {e}")
        return {'success': False, 'error': str(e)}

def main():
    try:
        # 解析参数
        params = {}
        if len(sys.argv) > 1:
            arg = sys.argv[1]
            if arg.startswith('@'):
                # 从文件读取参数
                param_file = arg[1:]
                try:
                    with open(param_file, 'r') as f:
                        params = json.loads(f.read())
                except Exception as e:
                    print(json.dumps({'success': False, 'error': f"读取参数文件失败: {e}"}))
                    return
            else:
                # 直接解析命令行参数
                try:
                    params = json.loads(arg)
                except Exception as e:
                    print(json.dumps({'success': False, 'error': f"解析参数失败: {e}"}))
                    return
        else:
            print(json.dumps({'success': False, 'error': '缺少参数'}))
            return
            
        # 执行测试
        test_type = params.get('test_type')
        if test_type == 'device_detection':
            devices = list_devices()
            print(json.dumps({'success': True, 'devices': devices}))
        elif test_type == 'basic_test':
            device_index = params.get('device_index')
            duration = params.get('duration', 5)
            if device_index is None:
                print(json.dumps({'success': False, 'error': '未指定设备索引'}))
                return
            result = test_device(device_index, duration)
            print(json.dumps(result))
        else:
            print(json.dumps({'success': False, 'error': f'未知的测试类型: {test_type}'}))
    except Exception as e:
        logger.error(f"执行测试失败: {e}")
        print(json.dumps({'success': False, 'error': str(e)}))

if __name__ == '__main__':
    main()
"""
            
            # 使用SSH命令直接创建文件
            # 注意：使用cat和EOF来创建多行文件
            self.logger.info("在远程服务器上创建test_script.py...")
            
            # 创建临时脚本
            temp_script = f"""cat > {self.remote_script_dir}/test_script.py << 'EOF'
{test_script_content}
EOF
"""
            
            # 执行创建文件的命令
            _, stdout, stderr = self.ssh_client.exec_command(temp_script)
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status != 0:
                error = stderr.read().decode()
                self.logger.error(f"创建test_script.py失败: {error}")
                return False
            
            # 设置执行权限
            _, stdout, stderr = self.ssh_client.exec_command(f"chmod +x {self.remote_script_dir}/test_script.py")
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status != 0:
                error = stderr.read().decode()
                self.logger.error(f"设置执行权限失败: {error}")
                return False
            
            # 验证文件是否存在
            _, stdout, _ = self.ssh_client.exec_command(f"test -f {self.remote_script_dir}/test_script.py && echo 'exists' || echo 'not exists'")
            file_exists = stdout.read().decode().strip()
            
            if file_exists != "exists":
                self.logger.error(f"创建文件后验证失败: {self.remote_script_dir}/test_script.py")
                return False
            
            # 验证文件是否可执行
            _, stdout, _ = self.ssh_client.exec_command(f"test -x {self.remote_script_dir}/test_script.py && echo 'executable' || echo 'not executable'")
            executable = stdout.read().decode().strip()
            
            if executable != "executable":
                self.logger.error(f"文件不可执行: {self.remote_script_dir}/test_script.py")
                return False
            
            # 查看文件内容（仅供调试）
            _, stdout, _ = self.ssh_client.exec_command(f"ls -la {self.remote_script_dir}")
            dir_content = stdout.read().decode()
            self.logger.info(f"远程目录内容:\n{dir_content}")
            
            self.logger.info("测试脚本已成功创建")
            return True
            
        except Exception as e:
            self.logger.error(f"上传测试脚本失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.sftp_client:
                self.sftp_client.close()
            if self.ssh_client:
                self.ssh_client.close()
            self.sftp_client = None
            self.ssh_client = None
            
            print(f"{Fore.YELLOW}🔌 已断开远程连接{Style.RESET_ALL}")
            
        except Exception as e:
            self.logger.error(f"断开连接时出错: {e}")
    
    def execute_command(self, command: str, timeout: int = 30) -> Tuple[bool, str, str]:
        """执行远程命令"""
        if not self.ssh_client:
            return False, "", "未连接到远程设备"
        
        try:
            self.logger.debug(f"执行远程命令: {command}")
            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=timeout)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            stdout_text = stdout.read().decode('utf-8', errors='ignore')
            stderr_text = stderr.read().decode('utf-8', errors='ignore')
            
            success = exit_status == 0
            
            if not success:
                self.logger.warning(f"命令执行失败 (退出码: {exit_status}): {stderr_text}")
            
            return success, stdout_text, stderr_text
            
        except Exception as e:
            self.logger.error(f"执行命令失败: {e}")
            return False, "", str(e)
    
    def check_remote_environment(self) -> Dict:
        """检查远程环境"""
        print(f"{Fore.YELLOW}🔍 检查远程环境...{Style.RESET_ALL}")
        
        environment_info = {
            'python_available': False,
            'python_version': '',
            'audio_packages': {},
            'audio_devices': [],
            'system_info': {},
            'permissions': {}
        }
        
        try:
            # 检查Python版本
            success, stdout, stderr = self.execute_command("python3 --version")
            if success:
                environment_info['python_available'] = True
                environment_info['python_version'] = stdout.strip()
                print(f"  ✅ Python: {stdout.strip()}")
            else:
                print(f"  ❌ Python不可用")
                return environment_info
            
            # 检查系统信息
            success, stdout, stderr = self.execute_command("uname -a")
            if success:
                environment_info['system_info']['uname'] = stdout.strip()
                print(f"  ✅ 系统: {stdout.strip()}")
            
            # 检查音频相关包
            packages_to_check = ['numpy', 'sounddevice', 'librosa', 'scipy']
            for package in packages_to_check:
                success, stdout, stderr = self.execute_command(
                    f"python3 -c 'import {package}; print({package}.__version__)'"
                )
                if success:
                    environment_info['audio_packages'][package] = stdout.strip()
                    print(f"  ✅ {package}: {stdout.strip()}")
                else:
                    environment_info['audio_packages'][package] = None
                    print(f"  ❌ {package}: 未安装")
            
            # 检查音频设备（如果sounddevice可用）
            if environment_info['audio_packages'].get('sounddevice'):
                success, stdout, stderr = self.execute_command(
                    "python3 -c 'import sounddevice as sd; import json; print(json.dumps([{\"index\": i, \"name\": d[\"name\"], \"inputs\": d[\"max_input_channels\"], \"outputs\": d[\"max_output_channels\"]} for i, d in enumerate(sd.query_devices())]))'"
                )
                if success:
                    try:
                        devices = json.loads(stdout)
                        environment_info['audio_devices'] = devices
                        print(f"  ✅ 检测到 {len(devices)} 个音频设备")
                        
                        # 显示可能的6麦设备
                        for device in devices:
                            if device['inputs'] >= 6:
                                print(f"    🎤 [{device['index']}] {device['name']} (输入:{device['inputs']})")
                    except json.JSONDecodeError:
                        print(f"  ⚠️  音频设备信息解析失败")
            
            # 检查音频权限
            success, stdout, stderr = self.execute_command("groups")
            if success:
                groups = stdout.strip().split()
                audio_groups = [g for g in groups if 'audio' in g.lower()]
                environment_info['permissions']['groups'] = groups
                environment_info['permissions']['audio_groups'] = audio_groups
                
                if audio_groups:
                    print(f"  ✅ 音频权限: {', '.join(audio_groups)}")
                else:
                    print(f"  ⚠️  未检测到音频组权限")
            
        except Exception as e:
            self.logger.error(f"环境检查失败: {e}")
            print(f"  ❌ 环境检查失败: {e}")
        
        return environment_info
    
    def run_remote_test(self, test_type: str, device_index: int = None, 
                       duration: int = 5) -> Dict:
        """运行远程测试"""
        if not self.ssh_client:
            raise Exception("未连接到远程设备")
            
        try:
            # 查看远程目录内容，检查文件是否存在
            _, stdout, _ = self.ssh_client.exec_command(f"ls -la {self.remote_script_dir}")
            dir_content = stdout.read().decode()
            self.logger.info(f"远程目录内容:\n{dir_content}")
            
            # 检查测试脚本是否存在
            _, stdout, _ = self.ssh_client.exec_command(f"test -f {self.remote_script_dir}/test_script.py && echo 'exists' || echo 'not exists'")
            file_exists = stdout.read().decode().strip()
            if file_exists != "exists":
                self.logger.error(f"测试脚本不存在: {self.remote_script_dir}/test_script.py")
                # 尝试重新上传脚本
                self.logger.info("尝试重新上传测试脚本...")
                if not self.upload_test_scripts():
                    raise Exception("重新上传测试脚本失败")
            
                # 再次验证文件是否存在
                _, stdout, _ = self.ssh_client.exec_command(f"test -f {self.remote_script_dir}/test_script.py && echo 'exists' || echo 'not exists'")
                file_exists = stdout.read().decode().strip()
                if file_exists != "exists":
                    raise Exception(f"无法创建测试脚本: {self.remote_script_dir}/test_script.py")
            
            # 构建测试参数
            test_params = {
                'test_type': test_type,
                'duration': duration
            }
            
            if device_index is not None:
                test_params['device_index'] = device_index
            
            # 将参数转换为JSON字符串，并确保转义正确
            params_json = json.dumps(test_params)
            
            # 创建一个临时参数文件而不是直接在命令行传递JSON
            param_file = f"{self.remote_script_dir}/params.json"
            _, stdout, stderr = self.ssh_client.exec_command(f"echo '{params_json}' > {param_file}")
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status != 0:
                error = stderr.read().decode()
                self.logger.error(f"创建参数文件失败: {error}")
                raise Exception(f"无法创建参数文件: {error}")
            
            # 构建命令，使用参数文件而不是命令行参数
            cmd = f"cd {self.remote_script_dir} && python3 ./test_script.py @{param_file}"
            
            self.logger.info(f"执行远程测试命令: {cmd}")
            
            # 执行测试前，先检查文件执行权限
            _, stdout, _ = self.ssh_client.exec_command(f"ls -l {self.remote_script_dir}/test_script.py")
            file_perms = stdout.read().decode().strip()
            self.logger.info(f"测试脚本权限: {file_perms}")
            
            # 确保文件可执行
            self.ssh_client.exec_command(f"chmod +x {self.remote_script_dir}/test_script.py")
            
            # 检查文件内容是否正确
            _, stdout, _ = self.ssh_client.exec_command(f"head -n 5 {self.remote_script_dir}/test_script.py")
            file_head = stdout.read().decode().strip()
            self.logger.info(f"测试脚本内容头部:\n{file_head}")
            
            # 使用bash执行命令，确保命令正确解析
            full_cmd = f"bash -c '{cmd}'"
            self.logger.info(f"最终执行命令: {full_cmd}")
            
            # 执行命令
            stdin, stdout, stderr = self.ssh_client.exec_command(full_cmd)
            
            # 获取输出
            output = stdout.read().decode()
            error = stderr.read().decode()
            
            # 检查退出状态
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status != 0:
                error_msg = error.strip() or output.strip()
                self.logger.error(f"命令执行失败 (退出码: {exit_status}): {error_msg}")
                
                # 尝试直接执行python3，看看是否能找到该命令
                _, stdout, _ = self.ssh_client.exec_command("which python3")
                python_path = stdout.read().decode().strip()
                self.logger.info(f"Python3路径: {python_path}")
                
                # 检查Python模块
                _, stdout, _ = self.ssh_client.exec_command("python3 -c 'import sys; print(sys.path)'")
                python_path = stdout.read().decode().strip()
                self.logger.info(f"Python路径: {python_path}")
                
                raise Exception(f"测试执行失败: {error_msg}")
                
            # 解析结果
            try:
                self.logger.info(f"远程测试输出: {output}")
                result = json.loads(output)
                return result
            except json.JSONDecodeError as e:
                self.logger.error(f"无法解析测试结果 ({e}): {output}")
                # 尝试清理输出并重新解析
                cleaned_output = output.strip()
                try:
                    # 尝试找到有效的JSON部分
                    json_start = cleaned_output.find("{")
                    json_end = cleaned_output.rfind("}")
                    if json_start >= 0 and json_end > json_start:
                        valid_json = cleaned_output[json_start:json_end+1]
                        self.logger.info(f"尝试解析清理后的JSON: {valid_json}")
                        result = json.loads(valid_json)
                        return result
                except:
                    pass
                raise Exception(f"测试结果格式错误: {e}")
                
        except Exception as e:
            self.logger.error(f"远程测试失败: {e}")
            raise
    
    def cleanup_remote_processes(self) -> bool:
        """清理远程设备上的相关进程"""
        try:
            print(f"{Fore.YELLOW}🧹 清理远程进程...{Style.RESET_ALL}")
            
            # 获取当前进程ID
            success, pid_output, _ = self.execute_command("echo $$")
            if not success:
                self.logger.error("无法获取当前进程ID")
                return False
            current_pid = pid_output.strip()
            
            # 清理 ifly 相关进程
            self.logger.info("清理 ifly 相关进程...")
            success, ifly_output, _ = self.execute_command("ps -ef | grep ifly")
            if success:
                for line in ifly_output.splitlines():
                    if current_pid not in line and 'grep' not in line:
                        try:
                            pid = line.split()[1]
                            self.execute_command(f"kill -9 {pid}")
                            self.logger.info(f"已终止 ifly 进程: {pid}")
                        except:
                            continue
            
            # 清理 obu 相关进程
            self.logger.info("清理 obu 相关进程...")
            success, obu_output, _ = self.execute_command("ps -ef | grep obu")
            if success:
                for line in obu_output.splitlines():
                    if current_pid not in line and 'grep' not in line:
                        try:
                            pid = line.split()[1]
                            self.execute_command(f"kill -9 {pid}")
                            self.logger.info(f"已终止 obu 进程: {pid}")
                        except:
                            continue
            
            print(f"{Fore.GREEN}✅ 远程进程清理完成{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            error_msg = f"清理远程进程失败: {e}"
            self.logger.error(error_msg)
            print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
            return False 