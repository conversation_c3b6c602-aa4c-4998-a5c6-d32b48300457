"""
GUI图表显示模块
"""
import numpy as np
import matplotlib
matplotlib.use('Qt5Agg')  # 使用Qt5Agg后端，它与PyQt6兼容
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.style as mplstyle
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel
from PyQt6.QtCore import pyqtSignal
from typing import Dict, List, Tuple
from matplotlib.font_manager import FontProperties

# 设置matplotlib样式
mplstyle.use('seaborn-v0_8')

# 配置中文字体
def set_chinese_font():
    # 设置中文字体，按优先级尝试
    font_candidates = ['Microsoft YaHei', '微软雅黑', 'SimHei', 'WenQuanYi Micro Hei', 'sans-serif']
    for font in font_candidates:
        try:
            chinese_font = FontProperties(family=font)
            matplotlib.rcParams['font.family'] = font
            return chinese_font
        except:
            continue
    return FontProperties()  # 返回默认字体

# 获取中文字体
chinese_font = set_chinese_font()

class MicArrayVisualization(QWidget):
    """麦克风阵列可视化组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.test_results = {}
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_layout = QHBoxLayout()
        
        control_layout.addWidget(QLabel("显示类型:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "频率响应", "波束图案", "通道对比", "信噪比分析", "3D波束图案"
        ])
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        control_layout.addWidget(self.chart_type_combo)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 图表画布
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
    def set_test_results(self, results: Dict):
        """设置测试结果数据"""
        self.test_results = results
        self.update_chart()
        
    def update_chart(self):
        """更新图表显示"""
        chart_type = self.chart_type_combo.currentText()
        
        self.figure.clear()
        
        if chart_type == "频率响应":
            self.plot_frequency_response()
        elif chart_type == "波束图案":
            self.plot_beam_pattern()
        elif chart_type == "通道对比":
            self.plot_channel_comparison()
        elif chart_type == "信噪比分析":
            self.plot_snr_analysis()
        elif chart_type == "3D波束图案":
            self.plot_3d_beam_pattern()
            
        self.canvas.draw()
        
    def plot_frequency_response(self):
        """绘制频率响应图"""
        if 'basic_test' not in self.test_results:
            self._plot_no_data("频率响应")
            return
            
        channel_results = self.test_results['basic_test'].get('channel_results', {})
        
        if not channel_results:
            self._plot_no_data("频率响应")
            return
            
        # 创建子图
        rows = 2
        cols = 3
        
        for i, (channel, result) in enumerate(channel_results.items()):
            if i >= 6:  # 最多显示6个通道
                break
                
            ax = self.figure.add_subplot(rows, cols, i + 1)
            
            if 'frequency_response' in result:
                freqs, psd = result['frequency_response']
                freqs = np.array(freqs)
                psd = np.array(psd)
                
                # 转换为dB
                psd_db = 10 * np.log10(psd + 1e-10)
                
                ax.semilogx(freqs, psd_db)
                ax.set_title(f'通道 {channel}')
                ax.set_xlabel('频率 (Hz)')
                ax.set_ylabel('功率谱密度 (dB)')
                ax.grid(True, alpha=0.3)
                ax.set_xlim(100, 8000)
                
        self.figure.suptitle('各通道频率响应', fontsize=14, fontweight='bold')
        self.figure.tight_layout()
        
    def plot_beam_pattern(self):
        """绘制波束图案"""
        if 'beamforming_test' not in self.test_results:
            self._plot_no_data("波束图案")
            return
            
        beam_results = self.test_results['beamforming_test']
        performance = beam_results.get('performance_results', {})
        
        if 'beam_pattern' not in performance:
            self._plot_no_data("波束图案")
            return
            
        angles, responses = performance['beam_pattern']
        angles = np.array(angles)
        responses = np.array(responses)
        
        # 极坐标图
        ax1 = self.figure.add_subplot(121, projection='polar')
        angles_rad = np.radians(angles)
        ax1.plot(angles_rad, responses, 'b-', linewidth=2)
        ax1.fill(angles_rad, responses, alpha=0.3)
        ax1.set_title('波束图案 (极坐标)', pad=20)
        ax1.grid(True)
        
        # 直角坐标图
        ax2 = self.figure.add_subplot(122)
        responses_db = 20 * np.log10(responses + 1e-10)
        ax2.plot(angles, responses_db, 'r-', linewidth=2)
        ax2.set_xlabel('角度 (度)')
        ax2.set_ylabel('响应 (dB)')
        ax2.set_title('波束图案 (dB)')
        ax2.grid(True, alpha=0.3)
        ax2.set_xlim(0, 360)
        
        self.figure.suptitle('波束成形图案分析', fontsize=14, fontweight='bold')
        self.figure.tight_layout()
        
    def plot_channel_comparison(self):
        """绘制通道对比图"""
        if 'basic_test' not in self.test_results:
            self._plot_no_data("通道对比")
            return
            
        channel_results = self.test_results['basic_test'].get('channel_results', {})
        
        if not channel_results:
            self._plot_no_data("通道对比")
            return
            
        channels = list(channel_results.keys())
        rms_values = [result['rms'] for result in channel_results.values()]
        peak_values = [result['peak'] for result in channel_results.values()]
        snr_values = [result['snr_db'] for result in channel_results.values()]
        
        # 创建三个子图
        ax1 = self.figure.add_subplot(131)
        bars1 = ax1.bar(channels, rms_values, color='skyblue', alpha=0.7)
        ax1.set_title('RMS 值对比')
        ax1.set_xlabel('通道')
        ax1.set_ylabel('RMS')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars1, rms_values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        
        ax2 = self.figure.add_subplot(132)
        bars2 = ax2.bar(channels, peak_values, color='lightcoral', alpha=0.7)
        ax2.set_title('峰值对比')
        ax2.set_xlabel('通道')
        ax2.set_ylabel('峰值')
        ax2.grid(True, alpha=0.3)
        
        for bar, value in zip(bars2, peak_values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        
        ax3 = self.figure.add_subplot(133)
        bars3 = ax3.bar(channels, snr_values, color='lightgreen', alpha=0.7)
        ax3.set_title('信噪比对比')
        ax3.set_xlabel('通道')
        ax3.set_ylabel('SNR (dB)')
        ax3.grid(True, alpha=0.3)
        
        for bar, value in zip(bars3, snr_values):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value:.1f}', ha='center', va='bottom', fontsize=8)
        
        self.figure.suptitle('通道性能对比分析', fontsize=14, fontweight='bold')
        self.figure.tight_layout()
        
    def plot_snr_analysis(self):
        """绘制信噪比分析图"""
        if 'basic_test' not in self.test_results:
            self._plot_no_data("信噪比分析")
            return
            
        channel_results = self.test_results['basic_test'].get('channel_results', {})
        
        if not channel_results:
            self._plot_no_data("信噪比分析")
            return
            
        channels = list(channel_results.keys())
        snr_values = [result['snr_db'] for result in channel_results.values()]
        
        # SNR阈值线
        threshold = 20  # dB
        
        ax = self.figure.add_subplot(111)
        
        # 绘制SNR柱状图
        colors = ['green' if snr >= threshold else 'red' for snr in snr_values]
        bars = ax.bar(channels, snr_values, color=colors, alpha=0.7)
        
        # 添加阈值线
        ax.axhline(y=threshold, color='orange', linestyle='--', linewidth=2, 
                  label=f'推荐阈值 ({threshold} dB)')
        
        # 添加数值标签
        for bar, value in zip(bars, snr_values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                   f'{value:.1f} dB', ha='center', va='bottom', fontweight='bold')
        
        ax.set_title('各通道信噪比分析', fontsize=14, fontweight='bold')
        ax.set_xlabel('通道')
        ax.set_ylabel('信噪比 (dB)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加性能评估文本
        good_channels = sum(1 for snr in snr_values if snr >= threshold)
        total_channels = len(snr_values)
        
        ax.text(0.02, 0.98, f'性能评估:\n优良通道: {good_channels}/{total_channels}\n'
                           f'平均SNR: {np.mean(snr_values):.1f} dB',
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        self.figure.tight_layout()
        
    def plot_3d_beam_pattern(self):
        """绘制3D波束图案"""
        if 'beamforming_test' not in self.test_results:
            self._plot_no_data("3D波束图案")
            return
            
        # 这里可以实现3D波束图案的绘制
        # 由于复杂性，暂时显示占位符
        ax = self.figure.add_subplot(111, projection='3d')
        
        # 生成示例3D数据
        phi = np.linspace(0, 2*np.pi, 50)
        theta = np.linspace(0, np.pi, 50)
        PHI, THETA = np.meshgrid(phi, theta)
        
        # 简化的3D波束图案
        R = 1 + 0.3 * np.cos(3*PHI) * np.sin(THETA)
        
        X = R * np.sin(THETA) * np.cos(PHI)
        Y = R * np.sin(THETA) * np.sin(PHI)
        Z = R * np.cos(THETA)
        
        ax.plot_surface(X, Y, Z, cmap='viridis', alpha=0.8)
        ax.set_title('3D波束图案 (示例)', fontsize=14, fontweight='bold')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        
        self.figure.tight_layout()
        
    def _plot_no_data(self, chart_type: str):
        """显示无数据图表"""
        ax = self.figure.add_subplot(111)
        ax.text(0.5, 0.5, f'暂无{chart_type}数据\n请先运行相应的测试',
                ha='center', va='center', transform=ax.transAxes,
                fontsize=16, bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
    def export_chart(self, filename: str):
        """导出图表"""
        self.figure.savefig(filename, dpi=300, bbox_inches='tight')
        
    def clear_charts(self):
        """清空图表"""
        self.figure.clear()
        self.canvas.draw()
