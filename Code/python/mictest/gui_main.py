#!/usr/bin/env python3
"""
讯飞6麦设备功能检测程序 - PyQt6 GUI版本
"""
import sys
import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import traceback

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QTextEdit, QLabel, QPushButton, QProgressBar,
    QGroupBox, QGridLayout, QComboBox, QSpinBox, QDoubleSpinBox,
    QCheckBox, QFileDialog, QMessageBox, QSplitter, QFrame,
    QTableWidget, QTableWidgetItem, QHeaderView, QScrollArea,
    QLineEdit, QRadioButton, QButtonGroup, QInputDialog, QProgressDialog
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSettings, pyqtSlot
)
from PyQt6.QtGui import (
    QFont, QPixmap, QIcon, QPalette, QColor, QTextCursor
)

# 导入自定义模块
from device_detector import DeviceDetector
from audio_tester import AudioTester
from beamforming_tester import BeamformingTester
from noise_suppression_tester import NoiseSuppressionTester
from config import DEVICE_CONFIG, TEST_CONFIG, OUTPUT_CONFIG
from gui_charts import MicArrayVisualization

# 尝试导入远程检测功能
try:
    from remote_detector import RemoteDetector
    from gui_remote_worker import RemoteTestWorker
    REMOTE_AVAILABLE = True
except ImportError:
    REMOTE_AVAILABLE = False
    RemoteTestWorker = None

class LogHandler(logging.Handler):
    """自定义日志处理器，将日志输出到GUI"""

    def __init__(self, text_widget: QTextEdit):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)
        # 在主线程中更新GUI
        self.text_widget.append(msg)
        # 自动滚动到底部
        cursor = self.text_widget.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.text_widget.setTextCursor(cursor)

class TestWorker(QThread):
    """测试工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态文本
    test_completed = pyqtSignal(str, dict)   # 测试名称, 结果
    test_failed = pyqtSignal(str, str)       # 测试名称, 错误信息
    device_detected = pyqtSignal(dict)       # 设备信息
    log_message = pyqtSignal(str, str)       # 日志级别, 消息

    def __init__(self):
        super().__init__()
        self.test_type = None
        self.device_index = None
        self.test_params = {}
        self.should_stop = False

        # 初始化测试器
        self.device_detector = DeviceDetector()
        self.audio_tester = None
        self.beamforming_tester = BeamformingTester()
        self.noise_tester = NoiseSuppressionTester()

    def set_test_params(self, test_type: str, device_index: int = None, **params):
        """设置测试参数"""
        self.test_type = test_type
        self.device_index = device_index
        self.test_params = params

    def stop_test(self):
        """停止测试"""
        self.should_stop = True

    def run(self):
        """运行测试"""
        try:
            if self.test_type == "device_detection":
                self._run_device_detection()
            elif self.test_type == "basic_test":
                self._run_basic_test()
            elif self.test_type == "beamforming_test":
                self._run_beamforming_test()
            elif self.test_type == "noise_test":
                self._run_noise_test()
            elif self.test_type == "full_test":
                self._run_full_test()
        except Exception as e:
            self.test_failed.emit(self.test_type, str(e))

    def _run_device_detection(self):
        """运行设备检测"""
        self.progress_updated.emit(10, "正在扫描音频设备...")

        # 检测所有设备
        all_devices = self.device_detector.list_all_devices()
        self.progress_updated.emit(50, "正在检测讯飞6麦设备...")

        # 检测讯飞设备
        device_info = self.device_detector.detect_xunfei_device()

        if device_info:
            self.progress_updated.emit(80, "正在测试设备连接...")
            connection_ok = self.device_detector.test_device_connection()

            if connection_ok:
                capabilities = self.device_detector.get_device_capabilities()
                result = {
                    'device_info': device_info,
                    'capabilities': capabilities,
                    'all_devices': all_devices,
                    'connection_ok': True
                }
                self.progress_updated.emit(100, "设备检测完成")
                self.device_detected.emit(device_info)
                self.test_completed.emit("device_detection", result)
            else:
                self.test_failed.emit("device_detection", "设备连接测试失败")
        else:
            result = {
                'device_info': None,
                'all_devices': all_devices,
                'connection_ok': False
            }
            self.test_completed.emit("device_detection", result)

    def _run_basic_test(self):
        """运行基本功能测试"""
        if not self.device_index:
            self.test_failed.emit("basic_test", "未指定设备索引")
            return

        self.audio_tester = AudioTester(self.device_index)

        self.progress_updated.emit(20, "开始录音...")
        duration = self.test_params.get('duration', DEVICE_CONFIG['record_duration'])
        audio_data = self.audio_tester.record_audio(duration)

        if audio_data.size == 0:
            self.test_failed.emit("basic_test", "录音失败")
            return

        self.progress_updated.emit(50, "分析通道功能...")
        channel_results = self.audio_tester.test_channel_functionality()

        self.progress_updated.emit(80, "分析音频质量...")
        quality_results = self.audio_tester.test_audio_quality(audio_data)

        # 保存音频文件
        if self.test_params.get('save_audio', True):
            self.audio_tester.save_audio_file(audio_data, "basic_test")

        result = {
            'channel_results': channel_results,
            'quality_results': quality_results,
            'audio_data_shape': audio_data.shape
        }

        self.progress_updated.emit(100, "基本测试完成")
        self.test_completed.emit("basic_test", result)

    def _run_beamforming_test(self):
        """运行波束成形测试"""
        if not self.device_index:
            self.test_failed.emit("beamforming_test", "未指定设备索引")
            return

        if not self.audio_tester:
            self.audio_tester = AudioTester(self.device_index)

        self.progress_updated.emit(20, "录制波束成形测试音频...")
        duration = self.test_params.get('duration', 5)
        audio_data = self.audio_tester.record_audio(duration)

        if audio_data.size == 0:
            self.test_failed.emit("beamforming_test", "录音失败")
            return

        self.progress_updated.emit(50, "分析方向性响应...")
        directional_results = self.beamforming_tester.test_directional_response(audio_data)

        self.progress_updated.emit(70, "计算波束成形性能...")
        performance_results = self.beamforming_tester.test_beamforming_performance(audio_data)

        self.progress_updated.emit(90, "测试空间分辨率...")
        resolution_results = self.beamforming_tester.test_spatial_resolution(audio_data)

        result = {
            'directional_results': directional_results,
            'performance_results': performance_results,
            'resolution_results': resolution_results
        }

        self.progress_updated.emit(100, "波束成形测试完成")
        self.test_completed.emit("beamforming_test", result)

    def _run_noise_test(self):
        """运行降噪测试"""
        # 这里需要两次录音，实现会更复杂
        self.progress_updated.emit(100, "降噪测试完成")
        self.test_completed.emit("noise_test", {})

    def _run_full_test(self):
        """运行完整测试"""
        # 依次运行所有测试
        tests = ["device_detection", "basic_test", "beamforming_test"]
        for i, test in enumerate(tests):
            if self.should_stop:
                break
            self.test_type = test
            progress = int((i / len(tests)) * 100)
            self.progress_updated.emit(progress, f"运行{test}...")

            if test == "device_detection":
                self._run_device_detection()
            elif test == "basic_test" and self.device_index:
                self._run_basic_test()
            elif test == "beamforming_test" and self.device_index:
                self._run_beamforming_test()

        self.progress_updated.emit(100, "所有测试完成")
        self.test_completed.emit("full_test", {})


class XunfeiMicGUI(QMainWindow):
    """讯飞6麦设备检测GUI主窗口"""

    def __init__(self):
        super().__init__()
        self.settings = QSettings('XunfeiMic', 'TestTool')
        self.test_worker = None
        self.remote_worker = None  # 远程测试工作线程
        self.current_device = None
        self.remote_device = None  # 当前远程设备
        self.test_results = {}
        self.is_remote_mode = False  # 远程模式标志
        self.remote_connected = False  # 远程连接状态

        self.init_ui()
        self.setup_logging()
        self.load_settings()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("讯飞6麦设备功能检测工具 v1.0")
        self.setGeometry(100, 100, 1200, 800)

        # 设置应用图标（如果有的话）
        # self.setWindowIcon(QIcon('icon.png'))

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧控制面板
        self.create_control_panel(splitter)

        # 右侧结果显示区域
        self.create_result_panel(splitter)

        # 设置分割器比例
        splitter.setSizes([400, 800])

        # 创建状态栏
        self.statusBar().showMessage("就绪")

    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)

        # 检测模式选择组
        mode_group = QGroupBox("检测模式")
        mode_layout = QVBoxLayout(mode_group)
        
        # 模式选择单选按钮
        self.mode_button_group = QButtonGroup()
        
        self.local_mode_radio = QRadioButton("本地检测")
        self.local_mode_radio.setChecked(True)
        self.local_mode_radio.toggled.connect(self.on_mode_changed)
        self.mode_button_group.addButton(self.local_mode_radio)
        mode_layout.addWidget(self.local_mode_radio)
        
        if REMOTE_AVAILABLE:
            self.remote_mode_radio = QRadioButton("远程检测")
            self.remote_mode_radio.toggled.connect(self.on_mode_changed)
            self.mode_button_group.addButton(self.remote_mode_radio)
            mode_layout.addWidget(self.remote_mode_radio)
        else:
            # 如果远程功能不可用，显示提示
            disabled_label = QLabel("远程检测 (需要安装paramiko)")
            disabled_label.setStyleSheet("color: gray;")
            mode_layout.addWidget(disabled_label)
        
        control_layout.addWidget(mode_group)

        # 远程连接配置组
        if REMOTE_AVAILABLE:
            self.remote_config_group = QGroupBox("远程连接配置")
            remote_config_layout = QGridLayout(self.remote_config_group)
            
            # 主机地址
            remote_config_layout.addWidget(QLabel("主机地址:"), 0, 0)
            self.hostname_input = QLineEdit()
            self.hostname_input.setPlaceholderText("例如: *************")
            self.hostname_input.textChanged.connect(self._check_remote_connection_params)
            remote_config_layout.addWidget(self.hostname_input, 0, 1)
            
            # 端口
            remote_config_layout.addWidget(QLabel("SSH端口:"), 1, 0)
            self.port_input = QSpinBox()
            self.port_input.setRange(1, 65535)
            self.port_input.setValue(22)
            remote_config_layout.addWidget(self.port_input, 1, 1)
            
            # 用户名
            remote_config_layout.addWidget(QLabel("用户名:"), 2, 0)
            self.username_input = QLineEdit()
            self.username_input.setPlaceholderText("例如: pi, ubuntu")
            self.username_input.textChanged.connect(self._check_remote_connection_params)
            remote_config_layout.addWidget(self.username_input, 2, 1)
            
            # 密码
            remote_config_layout.addWidget(QLabel("密码:"), 3, 0)
            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.password_input.setPlaceholderText("SSH密码")
            self.password_input.textChanged.connect(self._check_remote_connection_params)
            remote_config_layout.addWidget(self.password_input, 3, 1)
            
            # 私钥文件路径
            remote_config_layout.addWidget(QLabel("私钥文件:"), 4, 0)
            self.key_file_layout = QHBoxLayout()
            self.key_file_input = QLineEdit()
            self.key_file_input.setPlaceholderText("可选: SSH私钥文件路径")
            self.key_file_input.textChanged.connect(self._check_remote_connection_params)
            self.key_file_layout.addWidget(self.key_file_input)
            
            self.browse_key_btn = QPushButton("浏览...")
            self.browse_key_btn.clicked.connect(self.browse_key_file)
            self.key_file_layout.addWidget(self.browse_key_btn)
            remote_config_layout.addLayout(self.key_file_layout, 4, 1)
            
            # 添加连接测试按钮
            button_layout = QHBoxLayout()
            self.test_connection_btn = QPushButton("🧪 测试连接")
            self.test_connection_btn.clicked.connect(self.test_ssh_connection)
            self.test_connection_btn.setEnabled(False)
            button_layout.addWidget(self.test_connection_btn)
            
            # 连接按钮
            self.connect_btn = QPushButton("🔗 连接远程设备")
            self.connect_btn.clicked.connect(self.connect_remote)
            self.connect_btn.setEnabled(False)
            button_layout.addWidget(self.connect_btn)
            
            remote_config_layout.addLayout(button_layout, 5, 0, 1, 2)
            
            # 连接状态显示
            self.connection_status_label = QLabel("未连接")
            self.connection_status_label.setStyleSheet("QLabel { background-color: #ffcccc; padding: 5px; border-radius: 3px; }")
            remote_config_layout.addWidget(self.connection_status_label, 6, 0, 1, 2)
            
            # 初始状态隐藏远程配置
            self.remote_config_group.setVisible(False)
            control_layout.addWidget(self.remote_config_group)

        # 设备选择组
        device_group = QGroupBox("设备选择")
        device_layout = QVBoxLayout(device_group)

        # 设备检测按钮
        self.detect_btn = QPushButton("🔍 检测设备")
        self.detect_btn.clicked.connect(self.detect_devices)
        device_layout.addWidget(self.detect_btn)

        # 设备选择下拉框
        self.device_combo = QComboBox()
        self.device_combo.setEnabled(False)
        device_layout.addWidget(QLabel("选择设备:"))
        device_layout.addWidget(self.device_combo)

        # 设备信息显示
        self.device_info_label = QLabel("未检测到设备")
        self.device_info_label.setWordWrap(True)
        self.device_info_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; border-radius: 3px; }")
        device_layout.addWidget(self.device_info_label)

        control_layout.addWidget(device_group)

        # 测试配置组
        config_group = QGroupBox("测试配置")
        config_layout = QGridLayout(config_group)

        # 录音时长
        config_layout.addWidget(QLabel("录音时长(秒):"), 0, 0)
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 30)
        self.duration_spin.setValue(DEVICE_CONFIG['record_duration'])
        config_layout.addWidget(self.duration_spin, 0, 1)

        # 采样率
        config_layout.addWidget(QLabel("采样率(Hz):"), 1, 0)
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(['8000', '16000', '22050', '44100', '48000'])
        self.sample_rate_combo.setCurrentText(str(DEVICE_CONFIG['sample_rate']))
        config_layout.addWidget(self.sample_rate_combo, 1, 1)

        # 保存音频文件
        self.save_audio_check = QCheckBox("保存音频文件")
        self.save_audio_check.setChecked(OUTPUT_CONFIG['save_audio'])
        config_layout.addWidget(self.save_audio_check, 2, 0, 1, 2)

        control_layout.addWidget(config_group)

        # 测试控制组
        test_group = QGroupBox("测试控制")
        test_layout = QVBoxLayout(test_group)

        # 单项测试按钮
        self.basic_test_btn = QPushButton("🎤 基本功能测试")
        self.basic_test_btn.clicked.connect(lambda: self.start_test("basic_test"))
        self.basic_test_btn.setEnabled(False)
        test_layout.addWidget(self.basic_test_btn)

        self.beam_test_btn = QPushButton("📡 波束成形测试")
        self.beam_test_btn.clicked.connect(lambda: self.start_test("beamforming_test"))
        self.beam_test_btn.setEnabled(False)
        test_layout.addWidget(self.beam_test_btn)

        self.noise_test_btn = QPushButton("🔇 降噪功能测试")
        self.noise_test_btn.clicked.connect(lambda: self.start_test("noise_test"))
        self.noise_test_btn.setEnabled(False)
        test_layout.addWidget(self.noise_test_btn)

        # 完整测试按钮
        self.full_test_btn = QPushButton("🚀 运行完整测试")
        self.full_test_btn.clicked.connect(lambda: self.start_test("full_test"))
        self.full_test_btn.setEnabled(False)
        self.full_test_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        test_layout.addWidget(self.full_test_btn)

        # 停止测试按钮
        self.stop_test_btn = QPushButton("⏹️ 停止测试")
        self.stop_test_btn.clicked.connect(self.stop_test)
        self.stop_test_btn.setEnabled(False)
        self.stop_test_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        test_layout.addWidget(self.stop_test_btn)

        control_layout.addWidget(test_group)

        # 进度显示
        progress_group = QGroupBox("测试进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("就绪")
        progress_layout.addWidget(self.progress_label)

        control_layout.addWidget(progress_group)

        # 添加弹性空间
        control_layout.addStretch()

        parent.addWidget(control_widget)

    def create_result_panel(self, parent):
        """创建右侧结果显示面板"""
        result_widget = QWidget()
        result_layout = QVBoxLayout(result_widget)

        # 创建标签页
        self.tab_widget = QTabWidget()
        result_layout.addWidget(self.tab_widget)

        # 日志标签页
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.tab_widget.addTab(self.log_text, "📋 日志")

        # 设备信息标签页
        self.device_table = QTableWidget()
        self.tab_widget.addTab(self.device_table, "🔧 设备信息")

        # 测试结果标签页
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.tab_widget.addTab(self.result_text, "📊 测试结果")

        # 图表显示标签页
        self.chart_widget = MicArrayVisualization()
        self.tab_widget.addTab(self.chart_widget, "📈 图表分析")

        parent.addWidget(result_widget)

    def setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器
        self.log_handler = LogHandler(self.log_text)
        self.log_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        ))

        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(self.log_handler)
        root_logger.setLevel(logging.INFO)

        # 记录启动信息
        logging.info("讯飞6麦设备检测工具已启动")

    def load_settings(self):
        """加载设置"""
        # 恢复窗口几何形状
        geometry = self.settings.value('geometry')
        if geometry:
            self.restoreGeometry(geometry)

        # 恢复其他设置
        duration = self.settings.value('duration', DEVICE_CONFIG['record_duration'], type=int)
        self.duration_spin.setValue(duration)

        sample_rate = self.settings.value('sample_rate', DEVICE_CONFIG['sample_rate'], type=int)
        self.sample_rate_combo.setCurrentText(str(sample_rate))

        save_audio = self.settings.value('save_audio', OUTPUT_CONFIG['save_audio'], type=bool)
        self.save_audio_check.setChecked(save_audio)

        # 恢复远程连接设置
        if REMOTE_AVAILABLE:
            hostname = self.settings.value('remote_hostname', '', type=str)
            port = self.settings.value('remote_port', 22, type=int)
            username = self.settings.value('remote_username', '', type=str)
            key_file = self.settings.value('remote_key_file', '', type=str)
            
            if hostname:
                self.hostname_input.setText(hostname)
            self.port_input.setValue(port)
            if username:
                self.username_input.setText(username)
            if key_file:
                self.key_file_input.setText(key_file)
            
            # 检查是否启用远程模式
            remote_mode = self.settings.value('remote_mode_enabled', False, type=bool)
            if remote_mode:
                self.remote_mode_radio.setChecked(True)
                self.on_mode_changed()

    def save_settings(self):
        """保存设置"""
        self.settings.setValue('geometry', self.saveGeometry())
        self.settings.setValue('duration', self.duration_spin.value())
        self.settings.setValue('sample_rate', int(self.sample_rate_combo.currentText()))
        self.settings.setValue('save_audio', self.save_audio_check.isChecked())

        # 保存远程连接设置
        if REMOTE_AVAILABLE:
            self.settings.setValue('remote_hostname', self.hostname_input.text())
            self.settings.setValue('remote_port', self.port_input.value())
            self.settings.setValue('remote_username', self.username_input.text())
            self.settings.setValue('remote_key_file', self.key_file_input.text())
            self.settings.setValue('remote_mode_enabled', self.is_remote_mode)
            
            logging.info("远程连接设置已保存")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_settings()

        # 停止正在运行的测试
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop_test()
            self.test_worker.wait(3000)  # 等待3秒

        # 清理远程连接
        if self.remote_worker and self.remote_worker.isRunning():
            self.remote_worker.stop_test()
            self.remote_worker.wait(3000)  # 等待3秒
            
        if self.remote_worker:
            self.remote_worker.cleanup()

        event.accept()

    @pyqtSlot()
    def detect_devices(self):
        """检测设备"""
        if self.test_worker and self.test_worker.isRunning():
            QMessageBox.warning(self, "警告", "测试正在进行中，请先停止当前测试")
            return
            
        if self.remote_worker and self.remote_worker.isRunning():
            QMessageBox.warning(self, "警告", "远程操作正在进行中，请等待完成")
            return

        # 检查模式
        if self.is_remote_mode:
            if not self.remote_connected:
                QMessageBox.warning(self, "警告", "请先连接远程设备")
                return
            self._detect_remote_devices()
        else:
            self._detect_local_devices()

    def _detect_local_devices(self):
        """检测本地设备"""
        self.detect_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("正在检测本地设备...")

        # 创建并启动工作线程
        self.test_worker = TestWorker()
        self.test_worker.progress_updated.connect(self.update_progress)
        self.test_worker.test_completed.connect(self.on_device_detection_completed)
        self.test_worker.test_failed.connect(self.on_test_failed)
        self.test_worker.device_detected.connect(self.on_device_detected)

        self.test_worker.set_test_params("device_detection")
        self.test_worker.start()

    def _detect_remote_devices(self):
        """检测远程设备"""
        if not REMOTE_AVAILABLE or not self.remote_connected:
            return
            
        self.detect_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("正在检测远程设备...")

        # 复用现有的远程工作线程
        if not self.remote_worker:
            QMessageBox.warning(self, "警告", "远程连接已断开，请重新连接")
            return

        # 连接信号（避免重复连接）
        try:
            self.remote_worker.progress_updated.disconnect()
            self.remote_worker.test_completed.disconnect()
            self.remote_worker.test_failed.disconnect()
            self.remote_worker.device_detected.disconnect()
        except:
            pass

        self.remote_worker.progress_updated.connect(self.update_progress)
        self.remote_worker.test_completed.connect(self.on_remote_device_detection_completed)
        self.remote_worker.test_failed.connect(self.on_remote_test_failed)
        self.remote_worker.device_detected.connect(self.on_remote_device_detected)

        # 开始检测
        self.remote_worker.set_test_params("remote_device_detection")
        self.remote_worker.start()

    @pyqtSlot(int, str)
    def update_progress(self, value: int, text: str):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(text)
        self.statusBar().showMessage(text)

    @pyqtSlot(dict)
    def on_device_detected(self, device_info: Dict):
        """设备检测成功"""
        self.current_device = device_info

        # 启用测试按钮
        self.basic_test_btn.setEnabled(True)
        self.beam_test_btn.setEnabled(True)
        self.noise_test_btn.setEnabled(True)
        self.full_test_btn.setEnabled(True)

        # 更新设备信息显示
        info_text = f"""
设备名称: {device_info['name']}
设备索引: {device_info['index']}
输入通道: {device_info['channels']}
采样率: {device_info['sample_rate']} Hz
        """.strip()
        self.device_info_label.setText(info_text)

        logging.info(f"检测到设备: {device_info['name']}")

    @pyqtSlot(str, dict)
    def on_device_detection_completed(self, test_name: str, results: Dict):
        """设备检测完成"""
        self.detect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.progress_label.setText("设备检测完成")

        # 更新设备列表
        if 'all_devices' in results:
            self.update_device_table(results['all_devices'])

        if results.get('device_info'):
            self.device_combo.clear()
            self.device_combo.addItem(
                f"[{results['device_info']['index']}] {results['device_info']['name'][:40]}",
                results['device_info']['index']
            )
            self.device_combo.setEnabled(True)
        else:
            QMessageBox.information(self, "信息", "未检测到合适的6麦设备，但您可以从设备列表中手动选择")

    def update_device_table(self, devices: list):
        """更新设备信息表格"""
        self.device_table.setRowCount(len(devices))
        self.device_table.setColumnCount(6)
        self.device_table.setHorizontalHeaderLabels([
            "索引", "设备名称", "输入通道", "输出通道", "采样率", "主机API"
        ])

        for i, device in enumerate(devices):
            self.device_table.setItem(i, 0, QTableWidgetItem(str(device.get('index', 'N/A'))))
            self.device_table.setItem(i, 1, QTableWidgetItem(device.get('name', 'Unknown')))
            
            # 兼容不同的键名
            input_channels = device.get('input_channels', device.get('inputs', 0))
            output_channels = device.get('output_channels', device.get('outputs', 0))
            
            self.device_table.setItem(i, 2, QTableWidgetItem(str(input_channels)))
            self.device_table.setItem(i, 3, QTableWidgetItem(str(output_channels)))
            self.device_table.setItem(i, 4, QTableWidgetItem(f"{device.get('sample_rate', 0):.0f}"))
            self.device_table.setItem(i, 5, QTableWidgetItem(device.get('hostapi', 'Unknown')))

        # 调整列宽
        header = self.device_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)

    @pyqtSlot(str)
    def start_test(self, test_type: str):
        """开始测试"""
        # 检查设备状态
        if self.is_remote_mode:
            if not self.remote_connected:
                QMessageBox.warning(self, "警告", "请先连接远程设备")
                return
            if not self.remote_device:
                QMessageBox.warning(self, "警告", "请先检测远程设备")
                return
        else:
            if not self.current_device:
                QMessageBox.warning(self, "警告", "请先检测本地设备")
                return

        if self.test_worker and self.test_worker.isRunning():
            QMessageBox.warning(self, "警告", "本地测试正在进行中")
            return
            
        if self.remote_worker and self.remote_worker.isRunning():
            QMessageBox.warning(self, "警告", "远程测试正在进行中")
            return

        # 禁用测试按钮
        self.set_test_buttons_enabled(False)
        self.stop_test_btn.setEnabled(True)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 获取测试参数
        test_params = {
            'duration': self.duration_spin.value(),
            'sample_rate': int(self.sample_rate_combo.currentText()),
            'save_audio': self.save_audio_check.isChecked()
        }

        if self.is_remote_mode:
            self._start_remote_test(test_type, test_params)
        else:
            self._start_local_test(test_type, test_params)

        logging.info(f"开始{'远程' if self.is_remote_mode else '本地'}{test_type}测试")

    def _start_local_test(self, test_type: str, test_params: dict):
        """开始本地测试"""
        # 创建并启动工作线程
        self.test_worker = TestWorker()
        self.test_worker.progress_updated.connect(self.update_progress)
        self.test_worker.test_completed.connect(self.on_test_completed)
        self.test_worker.test_failed.connect(self.on_test_failed)

        self.test_worker.set_test_params(
            test_type,
            self.current_device['index'],
            **test_params
        )
        self.test_worker.start()

    def _start_remote_test(self, test_type: str, test_params: dict):
        """开始远程测试"""
        if not REMOTE_AVAILABLE or not self.remote_connected:
            QMessageBox.warning(self, "警告", "远程连接不可用")
            return

        # 复用现有的远程工作线程
        if not self.remote_worker:
            QMessageBox.warning(self, "警告", "远程连接已断开，请重新连接")
            return

        # 连接信号
        try:
            self.remote_worker.progress_updated.disconnect()
            self.remote_worker.test_completed.disconnect()
            self.remote_worker.test_failed.disconnect()
        except:
            pass

        self.remote_worker.progress_updated.connect(self.update_progress)
        self.remote_worker.test_completed.connect(self.on_remote_test_completed)
        self.remote_worker.test_failed.connect(self.on_remote_test_failed)

        # 转换测试类型为远程版本
        remote_test_type = f"remote_{test_type}"
        if test_type == "full_test":
            remote_test_type = "remote_full_test"

        # 添加设备索引
        test_params['device_index'] = self.remote_device.get('index')

        self.remote_worker.set_test_params(remote_test_type, **test_params)
        self.remote_worker.start()

    def set_test_buttons_enabled(self, enabled: bool):
        """设置测试按钮状态"""
        self.basic_test_btn.setEnabled(enabled)
        self.beam_test_btn.setEnabled(enabled)
        self.noise_test_btn.setEnabled(enabled)
        self.full_test_btn.setEnabled(enabled)

    @pyqtSlot()
    def stop_test(self):
        """停止测试"""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.stop_test()
            self.progress_label.setText("正在停止测试...")
            logging.info("用户请求停止测试")
        
        if self.remote_worker and self.remote_worker.isRunning():
            self.remote_worker.stop_test()
            self.progress_label.setText("正在停止远程测试...")
            logging.info("用户请求停止远程测试")

    @pyqtSlot(str, dict)
    def on_test_completed(self, test_name: str, results: Dict):
        """测试完成"""
        self.set_test_buttons_enabled(True)
        self.stop_test_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_label.setText(f"{test_name}测试完成")

        # 保存测试结果
        self.test_results[test_name] = results

        # 显示结果
        self.display_test_results(test_name, results)

        # 更新图表
        self.chart_widget.set_test_results(self.test_results)

        logging.info(f"{test_name}测试完成")

    @pyqtSlot(str, str)
    def on_test_failed(self, test_name: str, error_msg: str):
        """测试失败"""
        self.set_test_buttons_enabled(True)
        self.stop_test_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_label.setText(f"{test_name}测试失败")

        QMessageBox.critical(self, "测试失败", f"{test_name}测试失败:\n{error_msg}")
        logging.error(f"{test_name}测试失败: {error_msg}")

    def display_test_results(self, test_name: str, results: Dict):
        """显示测试结果"""
        result_text = f"\n{'='*50}\n"
        result_text += f"{test_name.upper()} 测试结果\n"
        result_text += f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        result_text += f"{'='*50}\n\n"

        if test_name == "basic_test":
            result_text += self.format_basic_test_results(results)
        elif test_name == "beamforming_test":
            result_text += self.format_beamforming_results(results)
        elif test_name == "noise_test":
            result_text += self.format_noise_test_results(results)

        result_text += "\n" + "="*50 + "\n"

        self.result_text.append(result_text)

        # 切换到结果标签页
        self.tab_widget.setCurrentWidget(self.result_text)

    def format_basic_test_results(self, results: Dict) -> str:
        """格式化基本测试结果"""
        text = "基本功能测试结果:\n\n"
        
        # 如果是远程测试结果，添加来源标记
        if results.get('remote_source'):
            text += "🌐 远程测试结果\n"
            if 'timestamp' in results:
                text += f"测试时间: {results['timestamp']}\n"
            text += "\n"

        if 'channel_results' in results:
            text += "通道测试结果:\n"
            for channel, result in results['channel_results'].items():
                status = "✅ 正常" if result.get('is_active', False) else "❌ 异常"
                text += f"  通道 {channel}: {status}\n"
                text += f"    RMS: {result.get('rms', 0):.4f}\n"
                text += f"    峰值: {result.get('peak', 0):.4f}\n"
                text += f"    信噪比: {result.get('snr_db', 0):.1f} dB\n\n"

        if 'quality_results' in results:
            text += "音频质量分析:\n"
            for channel, quality in results['quality_results'].items():
                text += f"  通道 {channel}:\n"
                text += f"    THD+N: {quality.get('thd_n_percent', 0):.2f}%\n"
                text += f"    动态范围: {quality.get('dynamic_range_db', 0):.1f} dB\n"
                text += f"    频率平坦度: {quality.get('frequency_flatness_db', 0):.1f} dB\n\n"

        # 显示测试参数信息
        if 'test_parameters' in results:
            text += "测试参数:\n"
            params = results['test_parameters']
            text += f"  录音时长: {params.get('duration', 'N/A')} 秒\n"
            text += f"  采样率: {params.get('sample_rate', 'N/A')} Hz\n"
            if results.get('remote_source'):
                text += f"  设备索引: {params.get('device_index', 'N/A')}\n"
            text += "\n"

        # 显示设备信息
        if 'device_info' in results and results['device_info']:
            text += "设备信息:\n"
            device_info = results['device_info']
            text += f"  设备名称: {device_info.get('name', 'Unknown')}\n"
            text += f"  输入通道数: {device_info.get('inputs', device_info.get('channels', 'N/A'))}\n"
            text += f"  采样率: {device_info.get('sample_rate', 'N/A')} Hz\n\n"

        return text

    def format_beamforming_results(self, results: Dict) -> str:
        """格式化波束成形测试结果"""
        text = "波束成形测试结果:\n\n"

        if 'performance_results' in results:
            perf = results['performance_results']
            text += "波束成形性能:\n"
            text += f"  主瓣角度: {perf['main_lobe_angle']:.1f}°\n"
            text += f"  -3dB波束宽度: {perf['beam_width_3db']:.1f}°\n"
            text += f"  旁瓣抑制: {perf['sidelobe_suppression_db']:.1f} dB\n\n"

        if 'directional_results' in results:
            text += "方向性响应:\n"
            for angle, result in results['directional_results'].items():
                text += f"  {angle:3.0f}°: RMS={result['rms_power']:.4f}, Peak={result['peak_power']:.4f}\n"
            text += "\n"

        if 'resolution_results' in results:
            text += "空间分辨率:\n"
            for angle_pair, result in results['resolution_results'].items():
                separable = "✅ 可分离" if result['separable'] else "❌ 不可分离"
                text += f"  {angle_pair}: {separable} (相关性: {result['correlation']:.3f})\n"
            text += "\n"

        return text

    def format_noise_test_results(self, results: Dict) -> str:
        """格式化降噪测试结果"""
        text = "降噪功能测试结果:\n\n"
        text += "测试功能尚未完全实现\n"
        return text

    # === 远程连接相关方法 ===

    @pyqtSlot()
    def on_mode_changed(self):
        """模式切换处理"""
        if not REMOTE_AVAILABLE:
            return
            
        self.is_remote_mode = self.remote_mode_radio.isChecked()
        
        # 显示/隐藏远程配置
        self.remote_config_group.setVisible(self.is_remote_mode)
        
        # 重置连接状态
        if self.is_remote_mode:
            self.detect_btn.setText("🔍 检测远程设备")
            # 检查是否能启用连接按钮
            self._check_remote_connection_params()
        else:
            self.detect_btn.setText("🔍 检测设备")
            # 断开远程连接
            if self.remote_connected:
                self.disconnect_remote()
        
        # 重置设备信息
        self.current_device = None
        self.remote_device = None
        self.device_info_label.setText("未检测到设备")
        self.device_combo.clear()
        self.device_combo.setEnabled(False)
        self.set_test_buttons_enabled(False)
        
        logging.info(f"切换到{'远程' if self.is_remote_mode else '本地'}检测模式")

    def _check_remote_connection_params(self):
        """检查远程连接参数是否完整"""
        if not REMOTE_AVAILABLE:
            return
            
        hostname = self.hostname_input.text().strip()
        username = self.username_input.text().strip()
        
        # 至少需要主机地址和用户名
        has_basic_params = bool(hostname and username)
        
        # 需要密码或私钥文件
        has_auth = bool(self.password_input.text().strip() or 
                       self.key_file_input.text().strip())
        
        # 如果有基本参数，启用测试连接（即使没有认证信息也可以测试网络连通性）
        self.test_connection_btn.setEnabled(has_basic_params)
        
        # 只有有完整信息时才启用连接按钮
        self.connect_btn.setEnabled(has_basic_params and has_auth)

    @pyqtSlot()
    def browse_key_file(self):
        """浏览私钥文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择SSH私钥文件", "", "所有文件 (*)"
        )
        if file_path:
            self.key_file_input.setText(file_path)
            self._check_remote_connection_params()

    @pyqtSlot()
    def connect_remote(self):
        """连接远程设备"""
        if not REMOTE_AVAILABLE:
            QMessageBox.warning(self, "警告", "远程功能不可用，请安装paramiko库")
            return
            
        if self.remote_worker and self.remote_worker.isRunning():
            QMessageBox.warning(self, "警告", "远程操作正在进行中")
            return

        # 获取连接参数
        hostname = self.hostname_input.text().strip()
        port = self.port_input.value()
        username = self.username_input.text().strip()
        password = self.password_input.text().strip() or None
        private_key_path = self.key_file_input.text().strip() or None

        if not hostname or not username:
            QMessageBox.warning(self, "警告", "请填写主机地址和用户名")
            return

        if not password and not private_key_path:
            QMessageBox.warning(self, "警告", "请填写密码或选择私钥文件")
            return

        # 禁用连接按钮
        self.connect_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建远程工作线程
        self.remote_worker = RemoteTestWorker()
        self.remote_worker.progress_updated.connect(self.update_progress)
        self.remote_worker.connection_status.connect(self.on_connection_status)
        self.remote_worker.test_completed.connect(self.on_remote_connection_completed)
        self.remote_worker.test_failed.connect(self.on_remote_connection_failed)

        # 设置连接参数
        self.remote_worker.set_connection_params(
            hostname=hostname,
            port=port,
            username=username,
            password=password,
            private_key_path=private_key_path
        )

        # 开始连接
        self.remote_worker.set_test_params("connect")
        self.remote_worker.start()

        logging.info(f"正在连接远程设备: {username}@{hostname}:{port}")

    @pyqtSlot(bool, str)
    def on_connection_status(self, connected: bool, message: str):
        """连接状态更新"""
        self.remote_connected = connected
        
        if connected:
            self.connection_status_label.setText(f"✅ {message}")
            self.connection_status_label.setStyleSheet(
                "QLabel { background-color: #ccffcc; padding: 5px; border-radius: 3px; }"
            )
            self.connect_btn.setText("🔌 断开连接")
            self.connect_btn.clicked.disconnect()
            self.connect_btn.clicked.connect(self.disconnect_remote)
        else:
            self.connection_status_label.setText(f"❌ {message}")
            self.connection_status_label.setStyleSheet(
                "QLabel { background-color: #ffcccc; padding: 5px; border-radius: 3px; }"
            )
            self.connect_btn.setText("🔗 连接远程设备")
            self.connect_btn.clicked.disconnect()
            self.connect_btn.clicked.connect(self.connect_remote)

        self.connect_btn.setEnabled(True)

    @pyqtSlot(str, dict)
    def on_remote_connection_completed(self, test_name: str, results: Dict):
        """远程连接完成"""
        self.progress_bar.setVisible(False)
        if test_name == "connect" and results.get("connected"):
            logging.info("远程连接建立成功")
            # 可以启用设备检测
            self.detect_btn.setEnabled(True)

    @pyqtSlot(str, str)
    def on_remote_connection_failed(self, test_name: str, error_msg: str):
        """远程连接失败"""
        self.connect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "连接失败", f"远程连接失败:\n{error_msg}")
        logging.error(f"远程连接失败: {error_msg}")

    @pyqtSlot()
    def disconnect_remote(self):
        """断开远程连接"""
        if self.remote_worker:
            self.remote_worker.cleanup()
            self.remote_worker = None
        
        self.remote_connected = False
        self.remote_device = None
        self.on_connection_status(False, "已断开")
        
        # 重置界面状态
        self.device_info_label.setText("未连接远程设备")
        self.device_combo.clear()
        self.device_combo.setEnabled(False)
        self.set_test_buttons_enabled(False)
        
        logging.info("已断开远程连接")

    # === 远程设备检测相关的信号处理 ===

    @pyqtSlot(dict)
    def on_remote_device_detected(self, device_info: Dict):
        """远程设备检测成功"""
        self.remote_device = device_info

        # 启用测试按钮
        self.basic_test_btn.setEnabled(True)
        self.beam_test_btn.setEnabled(True)
        self.noise_test_btn.setEnabled(True)
        self.full_test_btn.setEnabled(True)

        # 更新设备信息显示
        device_index = device_info.get('index', 'N/A')
        device_name = device_info.get('name', 'Unknown')
        inputs = device_info.get('inputs', 'N/A')
        sample_rate = device_info.get('sample_rate', 'N/A')
        
        info_text = f"""
远程设备信息:
设备索引: {device_index}
设备名称: {device_name}
输入通道: {inputs}
采样率: {sample_rate} Hz
        """.strip()
        self.device_info_label.setText(info_text)

        logging.info(f"选择远程设备: {device_name} (索引: {device_index}, 通道: {inputs})")
        
        # 更新状态栏
        self.statusBar().showMessage(f"已选择远程设备: {device_name}")
        
        # 切换到设备信息标签页
        self.tab_widget.setCurrentIndex(1)  # 设备信息标签页索引

    @pyqtSlot(str, dict)
    def on_remote_device_detection_completed(self, test_name: str, results: Dict):
        """远程设备检测完成"""
        self.detect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.progress_label.setText("远程设备检测完成")
        
        # 更新设备列表
        if 'all_devices' in results:
            self.update_device_table(results['all_devices'])
            
            # 更新状态栏
            device_count = len(results['all_devices'])
            self.statusBar().showMessage(f"检测到 {device_count} 个远程设备")
            
            # 获取合适的设备列表
            suitable_devices = results.get('suitable_devices', [])
            
            if suitable_devices:
                self.device_combo.clear()
                for device in suitable_devices:
                    # 确保device_index是整数
                    device_index = device.get('index', 0)
                    device_name = device.get('name', 'Unknown Device')
                    inputs = device.get('inputs', 0)
                    
                    self.device_combo.addItem(
                        f"[{device_index}] {device_name[:40]} (输入:{inputs})",
                        device_index
                    )
                self.device_combo.setEnabled(True)
                
                # 自动选择第一个合适的设备
                if not self.remote_device:
                    self.remote_device = suitable_devices[0]
                    self.on_remote_device_detected(suitable_devices[0])
                    
                # 显示成功消息
                logging.info(f"远程设备检测成功: 找到 {len(suitable_devices)} 个合适的设备")
            else:
                QMessageBox.information(
                    self, "信息", 
                    f"远程设备上检测到 {results.get('device_count', 0)} 个音频设备，"
                    "但没有找到合适的6麦设备。\n请从设备列表中手动选择。"
                )
        else:
            # 没有找到设备
            QMessageBox.information(self, "信息", "未在远程设备上检测到音频设备。")
            logging.warning("远程设备检测完成，但没有找到音频设备")

    @pyqtSlot(str, str)
    def on_remote_test_failed(self, test_name: str, error_msg: str):
        """远程测试失败"""
        self.detect_btn.setEnabled(True)
        if hasattr(self, 'connect_btn'):
            self.connect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.progress_label.setText(f"远程{test_name}失败")

        QMessageBox.critical(self, "远程操作失败", f"远程{test_name}失败:\n{error_msg}")
        logging.error(f"远程{test_name}失败: {error_msg}")

    @pyqtSlot(str, dict)
    def on_remote_test_completed(self, test_name: str, results: Dict):
        """远程测试完成"""
        self.set_test_buttons_enabled(True)
        self.stop_test_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # 转换测试名称显示
        display_name = test_name.replace("remote_", "")
        self.progress_label.setText(f"远程{display_name}测试完成")

        # 保存测试结果
        self.test_results[display_name] = results

        # 显示结果
        self.display_test_results(display_name, results)

        # 更新图表
        self.chart_widget.set_test_results(self.test_results)

        logging.info(f"远程{display_name}测试完成")

    @pyqtSlot()
    def test_ssh_connection(self):
        """测试SSH连接"""
        if not REMOTE_AVAILABLE:
            QMessageBox.warning(self, "警告", "远程功能不可用，请安装paramiko库")
            return

        hostname = self.hostname_input.text().strip()
        port = self.port_input.value()
        username = self.username_input.text().strip()

        if not hostname or not username:
            QMessageBox.warning(self, "警告", "请填写主机地址和用户名")
            return

        # 创建测试对话框
        progress = QProgressDialog("正在测试连接...", "取消", 0, 0, self)
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.setAutoClose(True)
        progress.setAutoReset(True)
        progress.show()

        import socket
        import subprocess
        
        # 测试结果列表
        test_results = []
        
        try:
            # 1. 测试网络连通性 (ping)
            progress.setLabelText("测试网络连通性...")
            try:
                # 根据操作系统选择正确的 ping 命令参数
                if sys.platform == 'win32':
                    result = subprocess.run(['ping', '-n', '1', '-w', '3000', hostname], 
                                          capture_output=True, text=True, timeout=5)
                else:
                    result = subprocess.run(['ping', '-c', '1', '-W', '3', hostname], 
                                          capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    test_results.append("✅ 网络连通性: 正常")
                else:
                    test_results.append("❌ 网络连通性: 失败")
            except Exception as e:
                test_results.append(f"⚠️ 网络连通性: 测试失败 ({e})")

            # 2. 测试端口连通性
            progress.setLabelText("测试SSH端口连通性...")
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((hostname, port))
                sock.close()
                
                if result == 0:
                    test_results.append(f"✅ SSH端口({port}): 开放")
                else:
                    test_results.append(f"❌ SSH端口({port}): 关闭或不可达")
            except Exception as e:
                test_results.append(f"⚠️ SSH端口({port}): 测试失败 ({e})")

            # 3. 测试SSH服务响应
            progress.setLabelText("测试SSH服务...")
            try:
                import paramiko
                ssh_test = paramiko.SSHClient()
                ssh_test.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                # 尝试连接但不进行认证，只测试SSH服务是否响应
                try:
                    ssh_test.connect(hostname, port=port, username=username, 
                                   password='dummy', timeout=5, 
                                   look_for_keys=False, allow_agent=False)
                except paramiko.AuthenticationException:
                    # 认证失败是预期的，说明SSH服务正常
                    test_results.append("✅ SSH服务: 响应正常")
                except (paramiko.SSHException, socket.error) as e:
                    test_results.append(f"❌ SSH服务: 异常 ({e})")
                except Exception as e:
                    test_results.append(f"⚠️ SSH服务: 测试失败 ({e})")
                finally:
                    ssh_test.close()
                    
            except Exception as e:
                test_results.append(f"⚠️ SSH服务: 无法测试 ({e})")

            # 4. 检查认证配置
            password = self.password_input.text().strip()
            key_file = self.key_file_input.text().strip()
            
            if password:
                test_results.append("✅ 认证配置: 密码已设置")
            elif key_file:
                if os.path.exists(key_file):
                    test_results.append("✅ 认证配置: 私钥文件存在")
                else:
                    test_results.append("❌ 认证配置: 私钥文件不存在")
            else:
                # 检查默认密钥
                ssh_dir = os.path.expanduser("~/.ssh")
                default_keys = ['id_rsa', 'id_ed25519', 'id_ecdsa']
                found_default = False
                for key_name in default_keys:
                    key_path = os.path.join(ssh_dir, key_name)
                    if os.path.exists(key_path):
                        test_results.append(f"✅ 认证配置: 找到默认密钥 {key_name}")
                        found_default = True
                        break
                
                if not found_default:
                    test_results.append("❌ 认证配置: 未设置密码或私钥")

        except Exception as e:
            test_results.append(f"❌ 测试过程出错: {e}")
        finally:
            progress.close()

        # 显示测试结果
        result_text = "SSH连接测试结果:\n\n" + "\n".join(test_results)
        
        # 判断整体结果
        success_count = len([r for r in test_results if r.startswith("✅")])
        total_tests = len(test_results)
        
        if success_count >= total_tests - 1:  # 允许一个警告
            result_text += f"\n\n🎉 测试基本通过 ({success_count}/{total_tests})"
            QMessageBox.information(self, "连接测试结果", result_text)
        else:
            result_text += f"\n\n⚠️ 存在问题需要解决 ({success_count}/{total_tests})"
            QMessageBox.warning(self, "连接测试结果", result_text)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("讯飞6麦设备检测工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("XunfeiMic")
    
    # 创建主窗口
    window = XunfeiMicGUI()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()