"""
讯飞6麦设备检测模块
"""
import sounddevice as sd
import numpy as np
import logging
from typing import Optional, Dict, List
from config import DEVICE_CONFIG

class DeviceDetector:
    """设备检测器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.device_info = None
        
    def detect_xunfei_device(self) -> Optional[Dict]:
        """检测讯飞6麦设备"""
        devices = sd.query_devices()
        
        # 查找讯飞设备
        xunfei_keywords = ['xunfei', '讯飞', 'iflytek', '6mic', '6麦']
        
        for i, device in enumerate(devices):
            device_name = device['name'].lower()
            
            # 检查设备名称是否包含讯飞相关关键词
            for keyword in xunfei_keywords:
                if keyword in device_name:
                    if device['max_input_channels'] >= DEVICE_CONFIG['channels']:
                        self.device_info = {
                            'index': i,
                            'name': device['name'],
                            'channels': device['max_input_channels'],
                            'sample_rate': device['default_samplerate'],
                            'hostapi': device['hostapi']
                        }
                        self.logger.info(f"找到讯飞6麦设备: {device['name']}")
                        return self.device_info
        
        # 如果没找到特定设备，查找6通道以上的输入设备
        for i, device in enumerate(devices):
            if (device['max_input_channels'] >= DEVICE_CONFIG['channels'] and 
                device['max_input_channels'] > 0):
                self.device_info = {
                    'index': i,
                    'name': device['name'],
                    'channels': device['max_input_channels'],
                    'sample_rate': device['default_samplerate'],
                    'hostapi': device['hostapi']
                }
                self.logger.warning(f"未找到讯飞设备，使用多通道设备: {device['name']}")
                return self.device_info
        
        self.logger.error("未找到合适的6麦设备")
        return None
    
    def select_device_manually(self, device_index: int) -> Optional[Dict]:
        """手动选择指定索引的设备"""
        try:
            devices = sd.query_devices()
            
            if device_index < 0 or device_index >= len(devices):
                self.logger.error(f"设备索引 {device_index} 超出范围 (0-{len(devices)-1})")
                return None
            
            device = devices[device_index]
            
            # 检查设备是否有足够的输入通道
            if device['max_input_channels'] < DEVICE_CONFIG['channels']:
                self.logger.warning(f"设备 '{device['name']}' 输入通道数 ({device['max_input_channels']}) "
                                  f"少于要求的 {DEVICE_CONFIG['channels']} 通道")
                # 仍然允许选择，但使用实际可用的通道数
                channels = device['max_input_channels']
            else:
                channels = DEVICE_CONFIG['channels']
            
            self.device_info = {
                'index': device_index,
                'name': device['name'],
                'channels': channels,
                'sample_rate': device['default_samplerate'],
                'hostapi': device['hostapi']
            }
            
            self.logger.info(f"手动选择设备: {device['name']} (索引: {device_index})")
            return self.device_info
            
        except Exception as e:
            self.logger.error(f"选择设备时发生错误: {e}")
            return None
    
    def get_suitable_devices(self) -> List[Dict]:
        """获取所有合适的多通道输入设备"""
        devices = sd.query_devices()
        suitable_devices = []
        
        for i, device in enumerate(devices):
            if device['max_input_channels'] >= 2:  # 至少2通道的输入设备
                device_info = {
                    'index': i,
                    'name': device['name'],
                    'input_channels': device['max_input_channels'],
                    'output_channels': device['max_output_channels'],
                    'sample_rate': device['default_samplerate'],
                    'hostapi': sd.query_hostapis(device['hostapi'])['name'],
                    'is_suitable': device['max_input_channels'] >= DEVICE_CONFIG['channels']
                }
                suitable_devices.append(device_info)
        
        return suitable_devices
    
    def list_all_devices(self) -> List[Dict]:
        """列出所有音频设备"""
        devices = sd.query_devices()
        device_list = []
        
        for i, device in enumerate(devices):
            device_info = {
                'index': i,
                'name': device['name'],
                'input_channels': device['max_input_channels'],
                'output_channels': device['max_output_channels'],
                'sample_rate': device['default_samplerate'],
                'hostapi': sd.query_hostapis(device['hostapi'])['name']
            }
            device_list.append(device_info)
            
        return device_list
    
    def test_device_connection(self) -> bool:
        """测试设备连接"""
        if not self.device_info:
            return False
            
        try:
            # 使用实际的通道数进行测试
            test_channels = min(self.device_info['channels'], DEVICE_CONFIG['channels'])
            
            # 尝试打开设备进行短暂录音
            with sd.InputStream(
                device=self.device_info['index'],
                channels=test_channels,
                samplerate=DEVICE_CONFIG['sample_rate'],
                blocksize=DEVICE_CONFIG['chunk_size']
            ) as stream:
                # 读取一小段数据测试
                data, overflowed = stream.read(DEVICE_CONFIG['chunk_size'])
                if overflowed:
                    self.logger.warning("设备缓冲区溢出")
                
                self.logger.info("设备连接测试成功")
                return True
                
        except Exception as e:
            self.logger.error(f"设备连接测试失败: {e}")
            return False
    
    def get_device_capabilities(self) -> Dict:
        """获取设备能力信息"""
        if not self.device_info:
            return {}
            
        capabilities = {
            'device_name': self.device_info['name'],
            'max_channels': self.device_info['channels'],
            'default_sample_rate': self.device_info['sample_rate'],
            'supported_sample_rates': [],
            'supported_formats': []
        }
        
        # 测试支持的采样率
        test_rates = [8000, 16000, 22050, 44100, 48000]
        test_channels = min(self.device_info['channels'], DEVICE_CONFIG['channels'])
        
        for rate in test_rates:
            try:
                sd.check_input_settings(
                    device=self.device_info['index'],
                    channels=test_channels,
                    samplerate=rate
                )
                capabilities['supported_sample_rates'].append(rate)
            except:
                pass
        
        return capabilities
