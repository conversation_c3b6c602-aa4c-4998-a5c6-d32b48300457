# 讯飞6麦设备配置文件

# 设备配置
DEVICE_CONFIG = {
    'name': '讯飞6麦阵列',
    'channels': 6,  # 6个麦克风通道
    'sample_rate': 16000,  # 采样率
    'chunk_size': 1024,  # 音频块大小
    'format': 'int16',  # 音频格式
    'record_duration': 3,  # 录音时长(秒)
}

# 测试配置
TEST_CONFIG = {
    'volume_threshold': 0.01,  # 音量阈值
    'noise_floor': -60,  # 噪声底限(dB)
    'snr_threshold': 20,  # 信噪比阈值(dB)
    'frequency_range': (100, 8000),  # 频率范围(Hz)
    'test_tone_freq': 1000,  # 测试音频频率(Hz)
}

# 波束成形配置
BEAMFORMING_CONFIG = {
    'angles': [0, 60, 120, 180, 240, 300],  # 测试角度
    'mic_positions': [  # 麦克风位置(相对坐标)
        (0.03, 0),      # 麦克风1
        (0.015, 0.026), # 麦克风2
        (-0.015, 0.026),# 麦克风3
        (-0.03, 0),     # 麦克风4
        (-0.015, -0.026),# 麦克风5
        (0.015, -0.026) # 麦克风6
    ],
    'sound_speed': 343,  # 声速(m/s)
}

# 输出配置
OUTPUT_CONFIG = {
    'save_audio': True,
    'output_dir': 'test_results',
    'log_level': 'INFO'
}
