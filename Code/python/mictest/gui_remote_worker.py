"""
GUI远程测试工作线程
实现远程麦克风检测的GUI集成
"""
import logging
from typing import Dict, Optional
from PyQt6.QtCore import QThread, pyqtSignal
from remote_detector import RemoteDetector


class RemoteTestWorker(QThread):
    """远程测试工作线程"""

    # 信号定义 - 与本地测试保持一致的接口
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态文本
    test_completed = pyqtSignal(str, dict)   # 测试名称, 结果
    test_failed = pyqtSignal(str, str)       # 测试名称, 错误信息
    device_detected = pyqtSignal(dict)       # 设备信息
    connection_status = pyqtSignal(bool, str)  # 连接状态, 消息
    environment_checked = pyqtSignal(dict)   # 环境检查结果

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # 测试参数
        self.test_type = None
        self.connection_params = {}
        self.test_params = {}
        self.should_stop = False
        
        # 远程检测器
        self.remote_detector = None
        
    def set_connection_params(self, hostname: str, port: int, username: str,
                            password: str = None, private_key_path: str = None):
        """设置连接参数"""
        self.connection_params = {
            'hostname': hostname,
            'port': port,
            'username': username,
            'password': password,
            'private_key_path': private_key_path
        }
        
    def set_test_params(self, test_type: str, **params):
        """设置测试参数"""
        self.test_type = test_type
        self.test_params = params

    def stop_test(self):
        """停止测试"""
        self.should_stop = True
        
    def run(self):
        """运行远程测试"""
        try:
            if self.test_type == "connect":
                self._handle_connection()
            elif self.test_type == "environment_check":
                self._check_environment()
            elif self.test_type == "remote_device_detection":
                self._handle_device_detection()
            elif self.test_type.startswith("remote_"):
                self._handle_remote_test()
                
        except Exception as e:
            self.logger.error(f"远程测试出错: {e}")
            self.test_failed.emit(self.test_type, str(e))
            
    def _handle_connection(self):
        """处理连接请求"""
        try:
            # 连接远程设备
            self.remote_detector = RemoteDetector()
            connected = self.remote_detector.connect(**self.connection_params)
            
            if connected:
                # 清理远程进程
                self.remote_detector.cleanup_remote_processes()
                
                # 发送连接成功信号
                self.connection_status.emit(True, "已连接")
                self.test_completed.emit("connect", {"connected": True})
            else:
                self.connection_status.emit(False, "连接失败")
                self.test_failed.emit("connect", "无法连接到远程设备")
                
        except Exception as e:
            self.connection_status.emit(False, str(e))
            self.test_failed.emit("connect", str(e))
            
    def _check_environment(self):
        """检查远程环境"""
        if not self.remote_detector:
            self.test_failed.emit("environment_check", "未建立远程连接")
            return
            
        self.progress_updated.emit(20, "检查远程Python环境...")
        
        try:
            environment_info = self.remote_detector.check_remote_environment()
            
            self.progress_updated.emit(60, "检查音频依赖...")
            
            # 检查是否需要安装依赖
            missing_packages = []
            required_packages = ['numpy', 'sounddevice', 'scipy']
            
            for package in required_packages:
                if not environment_info['audio_packages'].get(package):
                    missing_packages.append(package)
            
            environment_info['missing_packages'] = missing_packages
            environment_info['needs_install'] = len(missing_packages) > 0
            
            self.progress_updated.emit(100, "环境检查完成")
            self.environment_checked.emit(environment_info)
            self.test_completed.emit("environment_check", environment_info)
            
        except Exception as e:
            self.test_failed.emit("environment_check", str(e))
            
    def _handle_device_detection(self):
        """处理设备检测请求"""
        try:
            # 运行设备检测
            result = self.remote_detector.run_remote_test("device_detection")
            
            if result.get('success'):
                # 从返回结果中提取设备列表
                devices_list = result.get('devices', [])
                
                # 确保设备列表非空
                if not devices_list:
                    self.logger.warning("远程设备列表为空")
                    self.test_failed.emit("remote_device_detection", "未检测到远程音频设备")
                    return
                    
                self.logger.info(f"检测到 {len(devices_list)} 个远程音频设备")
                
                # 找出合适的设备（优先选择通道数大于等于6的设备）
                suitable_devices = [d for d in devices_list if d.get('inputs', 0) >= 6]
                
                # 如果没有找到合适的设备，使用所有设备
                if not suitable_devices:
                    self.logger.warning("未找到输入通道数>=6的设备，使用所有设备")
                    suitable_devices = devices_list
                
                # 构建结果字典
                device_result = {
                    'all_devices': devices_list,
                    'suitable_devices': suitable_devices,
                    'device_count': len(devices_list)
                }
                
                # 如果找到了设备，发送设备检测信号
                if suitable_devices:
                    # 选择第一个合适的设备
                    self.device_detected.emit(suitable_devices[0])
                    
                # 发送测试完成信号
                self.test_completed.emit("remote_device_detection", device_result)
                
            else:
                error_msg = result.get('error', '设备检测失败')
                self.logger.error(f"远程设备检测失败: {error_msg}")
                self.test_failed.emit("remote_device_detection", error_msg)
                
        except Exception as e:
            self.logger.error(f"处理远程设备检测时出错: {e}")
            self.test_failed.emit("remote_device_detection", str(e))
            
    def _handle_remote_test(self):
        """处理远程测试请求"""
        try:
            # 获取测试类型（移除"remote_"前缀）
            test_type = self.test_type.replace("remote_", "")
            
            # 运行测试
            result = self.remote_detector.run_remote_test(
                test_type,
                device_index=self.test_params.get('device_index'),
                duration=self.test_params.get('duration', 5)
            )
            
            if result.get('success'):
                self.test_completed.emit(self.test_type, result)
            else:
                self.test_failed.emit(self.test_type, 
                                    result.get('error', '测试失败'))
                
        except Exception as e:
            self.test_failed.emit(self.test_type, str(e))
            
    def _run_remote_basic_test(self):
        """运行远程基本测试"""
        if not self.remote_detector:
            self.test_failed.emit("remote_basic_test", "未建立远程连接")
            return
            
        self.progress_updated.emit(20, "准备远程测试...")
        
        try:
            # 获取测试参数
            device_index = self.test_params.get('device_index')
            duration = self.test_params.get('duration', 5)
            
            self.progress_updated.emit(50, "执行远程音频测试...")
            
            # 运行远程测试
            results = self.remote_detector.run_remote_test(device_index, duration)
            
            if 'error' in results:
                self.test_failed.emit("remote_basic_test", results['error'])
                return
                
            self.progress_updated.emit(80, "下载测试结果...")
            
            # 下载测试结果
            self.remote_detector.download_test_results()
            
            self.progress_updated.emit(100, "远程基本测试完成")
            
            # 转换结果格式以匹配本地测试接口
            formatted_results = self._format_remote_results(results)
            self.test_completed.emit("remote_basic_test", formatted_results)
            
        except Exception as e:
            self.test_failed.emit("remote_basic_test", str(e))
            
    def _run_remote_beamforming_test(self):
        """运行远程波束成形测试"""
        # 暂时使用基本测试逻辑，后续可扩展
        self.test_params['duration'] = self.test_params.get('duration', 8)  # 波束成形测试时间longer
        self._run_remote_basic_test()
        
    def _run_remote_full_test(self):
        """运行远程完整测试"""
        try:
            # 依次运行远程测试
            tests = [
                ("remote_device_detection", "设备检测"),
                ("remote_basic_test", "基本功能测试"),
                ("remote_beamforming_test", "波束成形测试")
            ]
            
            for i, (test_type, test_name) in enumerate(tests):
                if self.should_stop:
                    break
                    
                progress = int((i / len(tests)) * 90)  # 留10%给最终处理
                self.progress_updated.emit(progress, f"运行{test_name}...")
                
                # 临时修改测试类型
                original_test_type = self.test_type
                self.test_type = test_type
                
                if test_type == "remote_device_detection":
                    self._handle_device_detection()
                elif test_type == "remote_basic_test":
                    self._run_remote_basic_test()
                elif test_type == "remote_beamforming_test":
                    self._run_remote_beamforming_test()
                    
                # 恢复测试类型
                self.test_type = original_test_type
                
            self.progress_updated.emit(100, "远程完整测试完成")
            self.test_completed.emit("remote_full_test", {"status": "completed"})
            
        except Exception as e:
            self.test_failed.emit("remote_full_test", str(e))
            
    def _format_remote_results(self, remote_results: dict) -> dict:
        """将远程测试结果格式化为与本地测试兼容的格式"""
        try:
            # 提取通道结果
            channel_results = remote_results.get('channel_results', {})
            
            # 转换通道键为整数（如果需要）
            formatted_channel_results = {}
            for key, value in channel_results.items():
                try:
                    int_key = int(key)
                    formatted_channel_results[int_key] = value
                except (ValueError, TypeError):
                    formatted_channel_results[key] = value
            
            # 提取质量结果
            quality_results = remote_results.get('quality_results', {})
            
            # 转换质量结果键
            formatted_quality_results = {}
            for key, value in quality_results.items():
                try:
                    int_key = int(key)
                    formatted_quality_results[int_key] = value
                except (ValueError, TypeError):
                    formatted_quality_results[key] = value
            
            # 创建兼容格式的结果
            formatted_results = {
                'channel_results': formatted_channel_results,
                'quality_results': formatted_quality_results,
                'device_info': remote_results.get('device_info', {}),
                'summary': remote_results.get('summary', {}),
                'remote_source': True,  # 标记为远程测试结果
                'timestamp': remote_results.get('timestamp'),
                'test_parameters': remote_results.get('test_parameters', {})
            }
            
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"格式化远程结果失败: {e}")
            return remote_results
            
    def cleanup(self):
        """清理资源"""
        if self.remote_detector:
            try:
                self.remote_detector.cleanup_remote_files()
                self.remote_detector.disconnect()
            except:
                pass 