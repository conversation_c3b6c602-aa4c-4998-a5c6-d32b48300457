#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}========================================"
echo -e "   讯飞6麦设备检测程序 - 打包工具"
echo -e "========================================${NC}"
echo

# 检查Python环境
echo -e "${YELLOW}🔍 检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 未找到Python，请先安装Python 3.7+${NC}"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

$PYTHON_CMD --version
echo -e "${GREEN}✅ Python环境检查通过${NC}"
echo

# 检查PyInstaller
echo -e "${YELLOW}🔍 检查PyInstaller...${NC}"
if ! $PYTHON_CMD -c "import PyInstaller" &> /dev/null; then
    echo -e "${YELLOW}⚠️ 未安装PyInstaller，正在安装...${NC}"
    $PYTHON_CMD -m pip install pyinstaller
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ PyInstaller安装失败${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}✅ PyInstaller检查通过${NC}"
echo

# 检查依赖项
echo -e "${YELLOW}🔍 检查项目依赖...${NC}"
$PYTHON_CMD -m pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 依赖项安装失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 依赖项安装完成${NC}"
echo

# 清理之前的构建
echo -e "${YELLOW}🧹 清理之前的构建...${NC}"
rm -rf build/ dist/
echo -e "${GREEN}✅ 清理完成${NC}"
echo

# 开始打包
echo -e "${YELLOW}📦 开始打包应用...${NC}"
pyinstaller mictest.spec --clean
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 打包失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 打包完成${NC}"
echo

# 创建发布包
echo -e "${YELLOW}📦 创建发布包...${NC}"
mkdir -p release
cp -r dist/mictest release/
cp README.md release/

echo -e "${GREEN}✅ 发布包创建完成${NC}"
echo -e "${GREEN}📂 可执行文件位于: release/mictest/mictest${NC}"
echo

echo -e "${CYAN}========================================"
echo -e "   打包完成！"
echo -e "========================================${NC}" 