@echo off
chcp 65001 >nul
echo ========================================
echo    讯飞6麦设备检测程序 - 自动安装
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo ✅ Python环境检查通过
echo.

echo 📦 升级pip...
python -m pip install --upgrade pip
echo.

echo 📦 安装依赖包...
echo 正在安装基础包...
pip install numpy scipy matplotlib colorama tqdm

echo 正在安装音频处理包...
pip install sounddevice librosa soundfile

echo 正在尝试安装PyAudio...
pip install pyaudio
if errorlevel 1 (
    echo ⚠️  PyAudio安装失败，尝试使用pipwin...
    pip install pipwin
    pipwin install pyaudio
    if errorlevel 1 (
        echo ⚠️  PyAudio安装失败，但程序仍可运行
    )
)

echo 正在安装其他包...
pip install pydub

echo.
echo 🔧 验证安装...
python test_installation.py

if errorlevel 1 (
    echo.
    echo ❌ 安装验证失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 🎉 安装完成！
echo.
echo 使用方法:
echo   python run_gui.py       - 运行GUI版本（推荐）
echo   python main.py          - 运行命令行版本
echo   python test_installation.py - 验证安装
echo.
echo 如需安装GUI版本的额外依赖，请运行:
echo   python install_gui.py
echo.
pause
