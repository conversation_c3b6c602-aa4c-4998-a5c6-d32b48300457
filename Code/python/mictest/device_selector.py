#!/usr/bin/env python3
"""
设备选择器 - 用于列出和选择音频设备
"""
import sys
from colorama import init, Fore, Style
from device_detector import DeviceDetector

# 初始化colorama
init(autoreset=True)

def list_devices():
    """列出所有音频设备"""
    detector = DeviceDetector()
    devices = detector.list_all_devices()
    
    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"{Fore.YELLOW}              讯飞6麦设备选择器")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}📋 检测到的音频设备:{Style.RESET_ALL}")
    print(f"{Fore.WHITE}{'索引':<4} {'状态':<4} {'设备名称':<50} {'输入':<4} {'输出':<4} {'备注'}{Style.RESET_ALL}")
    print(f"{Fore.WHITE}{'-'*80}{Style.RESET_ALL}")
    
    for i, device in enumerate(devices):
        if device['input_channels'] >= 6:
            status = f"{Fore.GREEN}✅"
            note = f"{Fore.GREEN}[推荐]"
        elif device['input_channels'] >= 2:
            status = f"{Fore.YELLOW}⚠️"
            note = f"{Fore.YELLOW}[可用]"
        else:
            status = f"{Fore.RED}❌"
            note = f"{Fore.RED}[不适用]"
        
        device_name = device['name'][:50]
        print(f"{Fore.WHITE}[{i:2d}]{Style.RESET_ALL} {status} {device_name:<50} "
              f"{device['input_channels']:2d}    {device['output_channels']:2d}    {note}{Style.RESET_ALL}")
    
    return devices

def select_device_interactive(devices):
    """交互式选择设备"""
    print(f"\n{Fore.YELLOW}🎯 设备选择选项:{Style.RESET_ALL}")
    print(f"{Fore.CYAN}  1. 输入设备索引 (0-{len(devices)-1}){Style.RESET_ALL}")
    print(f"{Fore.CYAN}  2. 输入 'test INDEX' 测试指定设备{Style.RESET_ALL}")
    print(f"{Fore.CYAN}  3. 输入 'auto' 进行自动检测{Style.RESET_ALL}")
    print(f"{Fore.CYAN}  4. 输入 'quit' 退出程序{Style.RESET_ALL}")
    
    detector = DeviceDetector()
    
    while True:
        try:
            user_input = input(f"\n{Fore.YELLOW}请选择: {Style.RESET_ALL}").strip()
            
            if user_input.lower() == 'quit':
                print(f"{Fore.YELLOW}退出程序{Style.RESET_ALL}")
                return None
            
            elif user_input.lower() == 'auto':
                print(f"\n{Fore.CYAN}🔍 自动检测设备...{Style.RESET_ALL}")
                device_info = detector.detect_xunfei_device()
                if device_info:
                    print(f"{Fore.GREEN}✅ 自动检测到设备: {device_info['name']}{Style.RESET_ALL}")
                    return device_info['index']
                else:
                    print(f"{Fore.RED}❌ 自动检测失败{Style.RESET_ALL}")
                    continue
            
            elif user_input.lower().startswith('test '):
                try:
                    test_index = int(user_input.split()[1])
                    if 0 <= test_index < len(devices):
                        print(f"\n{Fore.CYAN}🧪 测试设备 [{test_index}]: {devices[test_index]['name']}{Style.RESET_ALL}")
                        device_info = detector.select_device_manually(test_index)
                        if device_info and detector.test_device_connection():
                            print(f"{Fore.GREEN}✅ 设备测试成功！{Style.RESET_ALL}")
                        else:
                            print(f"{Fore.RED}❌ 设备测试失败{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.RED}❌ 设备索引超出范围{Style.RESET_ALL}")
                except (ValueError, IndexError):
                    print(f"{Fore.RED}❌ 请使用格式: test INDEX{Style.RESET_ALL}")
                continue
            
            else:
                device_index = int(user_input)
                if 0 <= device_index < len(devices):
                    selected_device = devices[device_index]
                    print(f"\n{Fore.GREEN}✅ 已选择设备 [{device_index}]: {selected_device['name']}{Style.RESET_ALL}")
                    
                    if selected_device['input_channels'] < 6:
                        print(f"{Fore.YELLOW}⚠️  注意: 该设备输入通道数为 {selected_device['input_channels']}，"
                              f"少于推荐的6通道{Style.RESET_ALL}")
                        confirm = input(f"{Fore.YELLOW}是否继续使用? (y/n): {Style.RESET_ALL}").strip().lower()
                        if confirm not in ['y', 'yes', '是']:
                            continue
                    
                    return device_index
                else:
                    print(f"{Fore.RED}❌ 设备索引超出范围，请输入 0-{len(devices)-1}{Style.RESET_ALL}")
                    
        except ValueError:
            print(f"{Fore.RED}❌ 请输入有效的数字、'auto'、'test INDEX' 或 'quit'{Style.RESET_ALL}")
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}程序退出{Style.RESET_ALL}")
            return None

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='讯飞6麦设备选择器')
    parser.add_argument('--run-test', '-r', type=int, metavar='INDEX',
                        help='直接使用指定设备运行完整测试')
    
    args = parser.parse_args()
    
    # 列出所有设备
    devices = list_devices()
    
    if args.run_test is not None:
        # 直接运行测试
        if 0 <= args.run_test < len(devices):
            print(f"\n{Fore.CYAN}🚀 使用设备 [{args.run_test}] 运行完整测试...{Style.RESET_ALL}")
            from main import XunfeiMicTester
            tester = XunfeiMicTester()
            success = tester.run_all_tests(device_index=args.run_test)
            sys.exit(0 if success else 1)
        else:
            print(f"{Fore.RED}❌ 设备索引 {args.run_test} 超出范围{Style.RESET_ALL}")
            sys.exit(1)
    
    # 交互式选择
    selected_index = select_device_interactive(devices)
    
    if selected_index is not None:
        print(f"\n{Fore.CYAN}💡 使用选定设备运行测试的命令:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}   python main.py -d {selected_index}{Style.RESET_ALL}")
        print(f"\n{Fore.CYAN}💡 或者直接运行完整测试:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}   python device_selector.py -r {selected_index}{Style.RESET_ALL}")
        
        run_now = input(f"\n{Fore.YELLOW}是否现在运行完整测试? (y/n): {Style.RESET_ALL}").strip().lower()
        if run_now in ['y', 'yes', '是']:
            from main import XunfeiMicTester
            tester = XunfeiMicTester()
            tester.run_all_tests(device_index=selected_index)

if __name__ == "__main__":
    main() 