# 设备选择使用指南

本指南介绍如何在讯飞6麦设备测试程序中选择特定的麦克风设备。

## 快速开始

### 1. 查看所有可用设备

```bash
# 列出所有音频设备
python3 main.py --list-devices
```

输出示例：
```
📋 所有音频设备列表:
  ❌ [ 0] rockchip-es8388: dailink-multicodecs ES8323.4-0010-0 (hw:0,0 输入: 2 输出: 2
  ✅ [ 1] rockchip-es7210: dailink-multicodecs es7210.6-0040-0 (hw:1,0 输入: 8 输出: 0 [推荐]
  ✅ [ 2] sysdefault                                                   输入:128 输出:128 [推荐]
  ...
```

### 2. 设备状态说明

- ✅ **推荐设备**: 输入通道数 ≥ 6，适合多通道测试
- ⚠️ **可用设备**: 输入通道数 ≥ 2，可以使用但通道数有限  
- ❌ **不适用**: 输入通道数 < 2，不适合录音测试

## 设备选择方式

### 方式1: 自动检测（默认）

```bash
python3 main.py
```

程序会自动搜索包含讯飞相关关键词的设备：
- `xunfei`, `讯飞`, `iflytek`, `6mic`, `6麦`

如果没找到匹配设备，会选择第一个多通道设备。

### 方式2: 手动交互式选择

```bash
python3 main.py -m
```

程序会显示设备列表，然后提示您选择：

```
📋 检测到的音频设备:
  ✅ [ 0] 设备名称A                     输入: 8 输出: 0 [推荐]
  ❌ [ 1] 设备名称B                     输入: 2 输出: 2

请选择要使用的麦克风设备:
  输入设备索引 (0-1):
  输入 'auto' 进行自动检测
  输入 'quit' 退出程序
> 
```

支持的输入：
- **数字**: 直接选择对应索引的设备
- **auto**: 切换到自动检测模式  
- **quit**: 退出程序

### 方式3: 直接指定设备索引

```bash
# 使用设备索引1
python3 main.py -d 1
```

适合当您已经知道要使用的设备索引时。

### 方式4: 使用设备选择器工具

```bash
# 交互式设备选择器
python3 device_selector.py
```

提供更友好的界面：

```
================================================================================
                      讯飞6麦设备选择器
================================================================================

📋 检测到的音频设备:
索引 状态 设备名称                                              输入 输出 备注
--------------------------------------------------------------------------------
[ 0] ❌   rockchip-es8388                                      2    2    [不适用]
[ 1] ✅   rockchip-es7210                                      8    0    [推荐]
[ 2] ✅   sysdefault                                         128  128    [推荐]

🎯 设备选择选项:
  1. 输入设备索引 (0-2)
  2. 输入 'test INDEX' 测试指定设备
  3. 输入 'auto' 进行自动检测
  4. 输入 'quit' 退出程序

请选择: 
```

支持的操作：
- **数字**: 选择设备并可选择立即运行测试
- **test 索引**: 测试设备连接状态  
- **auto**: 自动检测设备
- **quit**: 退出程序

### 方式5: 设备选择器直接运行测试

```bash
# 直接使用设备1运行完整测试
python3 device_selector.py -r 1
```

## 设备选择建议

### 优先级顺序

1. **讯飞专用设备**: 包含讯飞关键词且通道数≥6
2. **多通道设备**: 输入通道数≥6的其他设备
3. **双通道设备**: 输入通道数≥2的设备
4. **其他设备**: 不推荐用于测试

### 设备兼容性检查

选择设备后，程序会进行兼容性检查：

```bash
✅ 成功检测到设备:
   设备名称: rockchip-es7210
   设备索引: 1
   输入通道: 8
   采样率: 16000.0
✅ 设备连接测试通过
```

### 常见设备类型

- **rockchip-es7210**: 典型的讯飞8通道设备
- **sysdefault**: 系统默认音频设备
- **dmix/upmix**: 音频混合设备
- **USB Audio**: USB音频设备

## 故障排除

### 设备检测不到

```bash
❌ 未检测到合适的6麦设备
```

**解决方案**:
1. 检查设备连接
2. 使用 `--list-devices` 查看所有设备
3. 手动选择可用的多通道设备

### 设备连接测试失败

```bash
❌ 设备连接测试失败
```

**解决方案**:
1. 确保设备没有被其他程序占用
2. 检查设备权限
3. 尝试其他设备
4. 重新插拔设备

### 通道数不足警告

```bash
⚠️ 注意: 该设备输入通道数为 2，少于推荐的6通道
是否继续使用? (y/n):
```

**说明**: 程序可以使用少于6通道的设备，但某些测试功能可能受限。

## 使用示例

### 完整工作流程

```bash
# 1. 查看所有设备
python3 main.py --list-devices

# 2. 选择合适的设备（假设选择设备1）
python3 main.py -d 1

# 或者使用交互式选择
python3 main.py -m

# 或者使用设备选择器
python3 device_selector.py
```

### 自动化脚本

```bash
#!/bin/bash
# 自动选择最佳设备并运行测试

# 尝试自动检测
python3 main.py

# 如果失败，使用设备1
if [ $? -ne 0 ]; then
    echo "自动检测失败，尝试使用设备1"
    python3 main.py -d 1
fi
```

## 配置选项

可以通过修改 `config.py` 调整设备检测行为：

```python
DEVICE_CONFIG = {
    'channels': 6,          # 最少需要的通道数
    'sample_rate': 16000,   # 采样率
    'chunk_size': 1024,     # 缓冲区大小
}
```

## 高级功能

### 设备能力查询

程序会自动查询设备能力：
- 支持的采样率
- 最大通道数
- 音频格式

### 设备性能测试

使用设备选择器的 `test` 命令：

```bash
请选择: test 1
🧪 测试设备 [1]: rockchip-es7210
✅ 设备测试成功！
```

这会验证设备是否可以正常录音。 