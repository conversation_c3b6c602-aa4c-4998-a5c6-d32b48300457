"""
波束成形测试模块
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import logging
from typing import List, Dict, Tuple

# 设置matplotlib字体，避免中文显示问题
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
from config import BEAMFORMING_CONFIG, DEVICE_CONFIG, OUTPUT_CONFIG
import os

class BeamformingTester:
    """波束成形测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.mic_positions = np.array(BEAMFORMING_CONFIG['mic_positions'])
        self.sound_speed = BEAMFORMING_CONFIG['sound_speed']
        self.sample_rate = DEVICE_CONFIG['sample_rate']
        
    def delay_and_sum_beamforming(self, audio_data: np.ndarray, target_angle: float) -> np.ndarray:
        """延迟求和波束成形"""
        target_angle_rad = np.radians(target_angle)
        target_direction = np.array([np.cos(target_angle_rad), np.sin(target_angle_rad)])
        
        # 计算各麦克风的延迟
        delays = []
        for mic_pos in self.mic_positions:
            # 计算到达时间差
            distance_diff = np.dot(mic_pos, target_direction)
            delay_samples = int(distance_diff / self.sound_speed * self.sample_rate)
            delays.append(delay_samples)
        
        # 对齐信号
        max_delay = max(delays)
        aligned_signals = []
        
        for channel in range(len(self.mic_positions)):
            delay = max_delay - delays[channel]
            if delay > 0:
                # 添加延迟
                padded_signal = np.pad(audio_data[:, channel], (delay, 0), mode='constant')
                aligned_signals.append(padded_signal[:len(audio_data)])
            else:
                # 裁剪信号
                aligned_signals.append(audio_data[-delay:, channel])
        
        # 求和
        beamformed_signal = np.mean(aligned_signals, axis=0)
        return beamformed_signal
    
    def test_directional_response(self, audio_data: np.ndarray) -> Dict[float, Dict]:
        """测试方向性响应"""
        self.logger.info("开始测试方向性响应...")
        
        results = {}
        test_angles = BEAMFORMING_CONFIG['angles']
        
        for angle in test_angles:
            self.logger.info(f"测试角度: {angle}°")
            
            # 执行波束成形
            beamformed = self.delay_and_sum_beamforming(audio_data, angle)
            
            # 计算信号强度
            rms_power = np.sqrt(np.mean(beamformed ** 2))
            peak_power = np.max(np.abs(beamformed))
            
            # 频谱分析
            freqs, psd = signal.welch(beamformed, self.sample_rate, nperseg=1024)
            
            results[angle] = {
                'rms_power': float(rms_power),
                'peak_power': float(peak_power),
                'frequency_spectrum': (freqs.tolist(), psd.tolist()),
                'beamformed_signal': beamformed.tolist()
            }
        
        return results
    
    def calculate_beam_pattern(self, audio_data: np.ndarray, frequency: float = 1000) -> Tuple[np.ndarray, np.ndarray]:
        """计算波束图案"""
        angles = np.arange(0, 360, 5)  # 每5度一个点
        responses = []
        
        for angle in angles:
            beamformed = self.delay_and_sum_beamforming(audio_data, angle)
            
            # 在特定频率处计算响应
            fft_data = np.fft.fft(beamformed)
            freqs = np.fft.fftfreq(len(beamformed), 1/self.sample_rate)
            
            # 找到最接近目标频率的索引
            freq_idx = np.argmin(np.abs(freqs - frequency))
            response = np.abs(fft_data[freq_idx])
            responses.append(response)
        
        responses = np.array(responses)
        # 归一化
        responses = responses / np.max(responses)
        
        return angles, responses
    
    def test_beamforming_performance(self, audio_data: np.ndarray) -> Dict:
        """测试波束成形性能"""
        self.logger.info("开始测试波束成形性能...")
        
        performance_metrics = {}
        
        # 测试主瓣宽度和旁瓣抑制
        angles, responses = self.calculate_beam_pattern(audio_data)
        
        # 找到主瓣
        main_lobe_idx = np.argmax(responses)
        main_lobe_angle = angles[main_lobe_idx]
        
        # 计算-3dB波束宽度
        half_power_level = responses[main_lobe_idx] / np.sqrt(2)
        half_power_indices = np.where(responses >= half_power_level)[0]
        
        if len(half_power_indices) > 1:
            beam_width = angles[half_power_indices[-1]] - angles[half_power_indices[0]]
        else:
            beam_width = 0
        
        # 计算旁瓣抑制比
        # 排除主瓣附近的点
        exclude_range = 30  # 排除主瓣附近30度范围
        exclude_indices = np.where(
            (angles >= main_lobe_angle - exclude_range) & 
            (angles <= main_lobe_angle + exclude_range)
        )[0]
        
        sidelobe_responses = np.delete(responses, exclude_indices)
        if len(sidelobe_responses) > 0:
            max_sidelobe = np.max(sidelobe_responses)
            sidelobe_suppression = 20 * np.log10(responses[main_lobe_idx] / max_sidelobe)
        else:
            sidelobe_suppression = float('inf')
        
        performance_metrics = {
            'main_lobe_angle': float(main_lobe_angle),
            'beam_width_3db': float(beam_width),
            'sidelobe_suppression_db': float(sidelobe_suppression),
            'beam_pattern': (angles.tolist(), responses.tolist())
        }
        
        return performance_metrics
    
    def plot_beam_pattern(self, angles: np.ndarray, responses: np.ndarray):
        """绘制波束图案"""
        try:
            plt.figure(figsize=(10, 8))
            
            # 极坐标图
            plt.subplot(121, projection='polar')
            angles_rad = np.radians(angles)
            plt.plot(angles_rad, responses)
            plt.fill(angles_rad, responses, alpha=0.3)
            plt.title('Beam Pattern (Polar)')  # 使用英文避免字体问题
            plt.grid(True)
            
            # 直角坐标图
            plt.subplot(122)
            # 防止log(0)错误
            responses_db = 20 * np.log10(np.maximum(responses, 1e-12))
            plt.plot(angles, responses_db)
            plt.xlabel('Angle (degrees)')
            plt.ylabel('Response (dB)')
            plt.title('Beam Pattern (dB)')
            plt.grid(True)
            
            plt.tight_layout()
            
            if OUTPUT_CONFIG['save_audio']:
                plt.savefig(os.path.join(OUTPUT_CONFIG['output_dir'], 'beam_pattern.png'),
                           dpi=150, bbox_inches='tight')
            
            plt.show()
        except Exception as e:
            self.logger.warning(f"绘制波束图案失败: {e}")
    
    def test_spatial_resolution(self, audio_data: np.ndarray) -> Dict:
        """测试空间分辨率"""
        self.logger.info("测试空间分辨率...")
        
        # 测试相邻角度的分辨能力
        test_angle_pairs = [(0, 15), (90, 105), (180, 195), (270, 285)]
        resolution_results = {}
        
        for angle1, angle2 in test_angle_pairs:
            beam1 = self.delay_and_sum_beamforming(audio_data, angle1)
            beam2 = self.delay_and_sum_beamforming(audio_data, angle2)
            
            # 计算相关性
            correlation = np.corrcoef(beam1, beam2)[0, 1]
            
            # 计算功率差异
            power1 = np.mean(beam1 ** 2)
            power2 = np.mean(beam2 ** 2)
            power_ratio_db = 10 * np.log10(power1 / power2) if power2 > 0 else float('inf')
            
            resolution_results[f"{angle1}-{angle2}"] = {
                'correlation': float(correlation),
                'power_ratio_db': float(power_ratio_db),
                'separable': correlation < 0.7  # 相关性阈值
            }
        
        return resolution_results
