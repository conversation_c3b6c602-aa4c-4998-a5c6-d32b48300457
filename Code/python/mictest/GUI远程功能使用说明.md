# GUI 远程功能使用说明

## 概述

讯飞6麦设备检测工具的GUI版本现在支持远程检测功能，您可以通过SSH连接对远程Linux设备上的麦克风阵列进行完整的功能检测。

## 功能特性

### 双模式支持
- **本地检测模式**: 检测本机连接的6麦设备
- **远程检测模式**: 通过SSH连接远程设备进行检测

### 远程功能
- SSH连接管理（支持密码和私钥认证）
- **🆕 连接测试功能**: 诊断SSH连接问题
- **🆕 登录信息保存**: 自动保存上次连接信息
- 远程环境自动检查和依赖安装
- 远程设备检测和测试
- 测试结果自动下载和显示
- 与本地测试相同的界面和功能

## 使用方法

### 启动GUI应用
```bash
# 激活虚拟环境
source .venv/bin/activate

# 启动GUI
python gui_main.py
```

### 远程检测步骤

#### 1. 选择检测模式
- 启动应用后，在左侧控制面板的"检测模式"部分
- 选择"远程检测"单选按钮
- 远程连接配置面板将自动显示

#### 2. 配置SSH连接
填写以下必需信息：
- **主机地址**: 远程设备的IP地址（如：*************）
- **SSH端口**: SSH服务端口（默认：22）
- **用户名**: SSH登录用户名（如：pi, ubuntu）
- **认证方式**（选择其一）：
  - **密码**: 输入SSH登录密码
  - **私钥文件**: 选择SSH私钥文件路径

> 💡 **新功能**: 应用会自动保存您的连接信息，下次启动时自动恢复

#### 3. 测试连接（推荐）
- 点击"🧪 测试连接"按钮进行连接诊断
- 系统会测试：
  - 🌐 网络连通性（ping测试）
  - 🔌 SSH端口连通性
  - 🔧 SSH服务响应
  - 🔑 认证配置检查
- 根据测试结果修复问题后再进行连接

#### 4. 建立连接
- 点击"🔗 连接远程设备"按钮
- 等待连接建立（状态会显示为"✅ 连接成功"）
- 连接成功后可以进行设备检测

#### 5. 检测远程设备
- 点击"🔍 检测远程设备"按钮
- 系统会自动：
  - 检查远程Python环境
  - 安装必要的音频依赖包
  - 扫描音频设备
  - 识别6麦设备

#### 6. 运行远程测试
选择要执行的测试：
- **🎤 基本功能测试**: 测试各通道录音和音质
- **📡 波束成形测试**: 测试方向性和空间分辨率  
- **🔇 降噪功能测试**: 测试噪声抑制能力
- **🚀 运行完整测试**: 依次执行所有测试

### 结果查看

测试完成后，可以在右侧面板查看：
- **📋 日志**: 详细的操作和错误日志
- **🔧 设备信息**: 远程设备的音频设备列表
- **📊 测试结果**: 格式化的测试报告（标记为🌐远程测试结果）
- **📈 图表分析**: 测试数据的可视化图表

## 🆕 新增功能详解

### 连接测试功能
在建立正式连接前，可以使用"测试连接"功能诊断潜在问题：

1. **网络连通性测试**
   - 使用ping命令测试网络连通性
   - 检测网络延迟和丢包情况

2. **SSH端口测试**
   - 测试SSH端口（默认22）是否开放
   - 检测防火墙和网络配置

3. **SSH服务测试**
   - 验证SSH服务是否正常响应
   - 检测SSH协议兼容性

4. **认证配置检查**
   - 验证密码或私钥文件设置
   - 检查默认SSH密钥

### 登录信息保存
- 自动保存连接配置（主机、端口、用户名、私钥路径）
- 保存检测模式选择（本地/远程）
- 下次启动自动恢复上次设置
- 不保存敏感信息（密码不会保存）

## 前后端分离设计

### 功能复用
- 远程测试使用与本地测试相同的测试逻辑
- 用户界面保持一致的操作体验
- 测试结果格式完全兼容

### 模块结构
```
gui_main.py               # 主GUI窗口（支持本地和远程模式）
gui_remote_worker.py      # 远程测试工作线程
remote_detector.py        # 远程检测核心逻辑（改进连接处理）
device_detector.py        # 本地设备检测逻辑
audio_tester.py           # 音频测试逻辑
test_remote_connection.py # 连接测试脚本
...                       # 其他测试模块
```

### 工作线程
- **TestWorker**: 处理本地测试任务
- **RemoteTestWorker**: 处理远程测试任务
- 两者提供相同的信号接口，确保GUI逻辑统一

## 🔧 连接问题诊断

### 使用内置测试功能
1. 在GUI中点击"🧪 测试连接"
2. 查看详细测试结果
3. 根据失败项目进行针对性修复

### 使用命令行测试工具
```bash
# 使用密码认证测试
python test_remote_connection.py ************* pi mypassword

# 使用私钥认证测试
python test_remote_connection.py ************* ubuntu '' ~/.ssh/id_rsa
```

### 常见问题及解决方案

#### 🌐 网络连通性失败
```bash
# 手动测试网络连通性
ping -c 3 目标IP

# 检查路由
traceroute 目标IP
```

#### 🔌 SSH端口问题
```bash
# 测试端口连通性
telnet 目标IP 22
# 或使用nc
nc -zv 目标IP 22

# 检查防火墙
# 在远程设备上：
sudo ufw status
sudo iptables -L
```

#### 🔧 SSH服务问题
```bash
# 在远程设备上检查SSH服务
sudo systemctl status ssh
sudo systemctl start ssh   # 启动服务
sudo systemctl enable ssh  # 开机自启

# 检查SSH配置
sudo nano /etc/ssh/sshd_config
```

#### 🔑 认证问题
```bash
# 检查用户权限
groups 用户名

# 生成SSH密钥对
ssh-keygen -t rsa -b 4096

# 复制公钥到远程设备
ssh-copy-id 用户名@目标IP

# 手动测试SSH连接
ssh -v 用户名@目标IP
```

## 注意事项

### 网络要求
- 确保本机能SSH连接到目标设备
- 网络延迟会影响测试时间
- 建议在稳定的网络环境下使用

### 远程设备要求
- Linux系统，支持Python 3.6+
- 已安装SSH服务
- 有sudo权限（用于安装依赖包）
- 连接了支持的6麦设备

### 安全建议
- 使用SSH私钥认证（比密码更安全）
- 定期更换SSH密码
- 限制SSH访问的IP范围
- 关闭不必要的端口

## 故障排除

### 连接失败
1. **使用测试连接功能**: 点击"🧪 测试连接"获取详细诊断
2. **检查网络**: `ping 目标IP`
3. **验证SSH服务**: `ssh user@host`
4. **确认端口**: 默认22，可能被修改
5. **检查防火墙**: 确保SSH端口开放

### 设备检测失败
- 确认设备已连接到远程主机
- 检查设备权限（可能需要将用户加入audio组）
- 验证驱动程序安装

### 测试中断
- 检查网络连接稳定性
- 确认远程设备资源充足
- 查看详细错误日志

## 技术支持

如遇问题，请：
1. 使用"🧪 测试连接"功能进行诊断
2. 运行命令行测试工具：`python test_remote_connection.py`
3. 查看GUI日志标签页的详细信息
4. 检查网络和SSH连接
5. 确认远程设备状态
6. 联系技术支持团队

---

通过此增强版GUI工具，您可以更轻松、更可靠地管理本地和远程的6麦设备检测任务！ 