# 项目根目录

## 📁 目录结构

### 🚀 projects/ - 项目代码
- `ad-hoc/` - 无线自组网项目
- `ble/` - BLE蓝牙项目  
- `robot/` - 机器人项目

### 📚 docs/ - 技术文档
- `code/` - 代码相关文档
- `ros/` - ROS相关文档
- `router/` - 路由器相关文档

### ⚙️ config/ - 配置文件
- `mine/` - 个人配置
- `work/` - 工作配置
- `script/` - 脚本文件
- `template/` - 模板文件

### 🎨 resources/ - 资源文件
- `assets/` - 静态资源
- `attachment/` - 图片附件
- `AI/` - AI相关资源

### 👤 personal/ - 个人内容
- `journals/` - 日记
- `SimpRead/` - 简阅配置
- `其他杂/` - 杂项

### 💼 workspace/ - 工作空间
- `TODO.md` - 待办事项
- `备忘录.md` - 备忘录
- `wsl备份恢复指南.md` - WSL指南
- `tools/` - 整理工具

## 🎯 使用说明

这个目录结构经过优化，将原来的20+个根目录文件夹缩减为6个主要分类，便于管理和查找。

每个主目录都有明确的功能定位，新增内容请按照分类放入对应目录。
