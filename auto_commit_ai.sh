#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本所在目录的绝对路径
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PROJECT_DIR="$SCRIPT_DIR"  # 使用脚本所在目录作为项目目录

# DeepSeek API密钥 - 需要替换为您自己的密钥
# 您可以通过环境变量设置，或者直接在这里设置
DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-"***********************************"}

# AI模型配置
MODEL="deepseek-chat"  # DeepSeek模型
MAX_TOKENS=500
TEMPERATURE=0.7

# 日志文件
LOG_FILE="$PROJECT_DIR/auto_commit.log"

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查是否有未提交的变更
check_changes() {
    cd "$PROJECT_DIR" || {
        log "${RED}错误：无法进入项目目录 $PROJECT_DIR${NC}"
        exit 1
    }
    
    # 检查是否是Git仓库
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        log "${RED}错误：当前目录不是Git仓库${NC}"
        log "${YELLOW}请在Git仓库目录中运行此脚本${NC}"
        
        # 尝试初始化Git仓库
        log "${BLUE}尝试初始化Git仓库...${NC}"
        git init
        
        # 再次检查是否成功初始化
        if ! git rev-parse --is-inside-work-tree &> /dev/null; then
            log "${RED}Git仓库初始化失败，退出脚本${NC}"
            exit 1
        else
            log "${GREEN}Git仓库初始化成功${NC}"
            # 添加所有文件到暂存区
            git add .
            log "${GREEN}所有文件已添加到暂存区${NC}"
            return 0
        fi
    fi
    
    # 检查是否有未暂存的变更
    if [[ -z $(git status --porcelain) ]]; then
        log "${YELLOW}没有检测到变更，无需提交${NC}"
        exit 0
    fi
    
    log "${GREEN}检测到未提交的变更${NC}"
    return 0
}

# 获取变更的详细信息
get_changes_details() {
    cd "$PROJECT_DIR" || exit 1
    
    # 获取变更的文件列表
    CHANGED_FILES=$(git status --porcelain | awk '{print $2}')
    
    # 获取每个文件的diff
    DIFF_DETAILS=""
    for file in $CHANGED_FILES; do
        if [[ -f "$file" ]]; then
            if git ls-files --error-unmatch "$file" &> /dev/null; then
                # 已跟踪文件的diff
                DIFF_DETAILS+="文件: $file\n"
                DIFF_DETAILS+="$(git diff --unified=1 "$file" | grep -v "^---" | grep -v "^+++")\n\n"
            else
                # 新文件，获取内容摘要
                DIFF_DETAILS+="新文件: $file\n"
                DIFF_DETAILS+="$(head -n 10 "$file" | sed 's/^/> /')\n"
                if [[ $(wc -l < "$file") -gt 10 ]]; then
                    DIFF_DETAILS+="... (文件较长，仅显示前10行)\n\n"
                else
                    DIFF_DETAILS+="\n"
                fi
            fi
        fi
    done
    
    echo "$DIFF_DETAILS"
}

# 使用AI生成commit信息
generate_commit_message() {
    local changes="$1"
    local prompt="请根据以下Git变更生成一个简洁、明确的commit信息。
    遵循以下格式：
    - 第一行：简短的标题，不超过50个字符
    - 空行
    - 详细描述：解释这次提交做了什么以及为什么做这些变更
    
    变更内容：
    $changes
    
    请确保commit信息符合良好的Git实践，使用现在时态，并专注于'做了什么'而不是'怎么做的'。"
    
    log "${BLUE}正在使用DeepSeek AI生成commit信息...${NC}"
    
    # 创建临时文件保存完整响应
    TEMP_RESPONSE_FILE=$(mktemp)
    
    # 调用DeepSeek API
    local response
    response=$(curl -s -X POST "https://api.deepseek.com/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
        -d "{
            \"model\": \"$MODEL\",
            \"messages\": [{\"role\": \"user\", \"content\": \"$prompt\"}],
            \"max_tokens\": $MAX_TOKENS,
            \"temperature\": $TEMPERATURE
        }")
    
    # 保存完整响应到临时文件
    echo "$response" > "$TEMP_RESPONSE_FILE"
    log "${BLUE}API响应已保存到临时文件: $TEMP_RESPONSE_FILE${NC}"
    
    # 检查API调用是否成功
    if [[ $response == *"error"* ]]; then
        log "${RED}DeepSeek API调用失败${NC}"
        log "${RED}错误详情: $(echo "$response" | grep -o '"error":[^}]*}' || echo "$response")${NC}"
        echo "自动生成的commit信息 ($(date '+%Y-%m-%d %H:%M'))"
        return 1
    fi
    
    # 尝试多种方式提取生成的commit信息
    local commit_message=""
    
    # 方法1: 标准OpenAI格式
    commit_message=$(echo "$response" | grep -o '"content":"[^"]*"' | head -1 | sed 's/"content":"//;s/"$//')
    
    # 方法2: 如果方法1失败，尝试DeepSeek特定格式
    if [[ -z "$commit_message" ]]; then
        commit_message=$(echo "$response" | grep -o '"message":[^}]*"content":"[^"]*"' | head -1 | sed 's/.*"content":"//;s/"$//')
    fi
    
    # 方法3: 尝试解析choices数组
    if [[ -z "$commit_message" ]]; then
        commit_message=$(echo "$response" | grep -o '"choices":\[[^]]*\]' | grep -o '"content":"[^"]*"' | head -1 | sed 's/"content":"//;s/"$//')
    fi
    
    # 方法4: 使用jq工具(如果可用)
    if [[ -z "$commit_message" ]] && command -v jq &> /dev/null; then
        commit_message=$(echo "$response" | jq -r '.choices[0].message.content' 2>/dev/null)
    fi
    
    # 如果提取失败，使用默认信息
    if [[ -z "$commit_message" ]]; then
        log "${YELLOW}无法从API响应中提取commit信息，使用默认信息${NC}"
        log "${YELLOW}API响应: $(echo "$response" | head -c 300)...${NC}"
        echo "自动生成的commit信息 ($(date '+%Y-%m-%d %H:%M'))"
        return 1
    fi
    
    echo "$commit_message"
    return 0
}

# 执行git提交
do_commit() {
    local commit_message="$1"
    cd "$PROJECT_DIR" || exit 1
    
    # 添加所有变更
    git add .
    
    # 提交变更
    git commit -m "$commit_message"
    
    # 检查提交是否成功
    if [[ $? -eq 0 ]]; then
        log "${GREEN}成功提交变更${NC}"
        return 0
    else
        log "${RED}提交失败${NC}"
        return 1
    fi
}

# 主函数
main() {
    log "${BLUE}开始自动提交流程${NC}"
    
    # 检查API密钥
    if [[ "$DEEPSEEK_API_KEY" == "your_api_key_here" ]]; then
        log "${RED}错误：未设置DeepSeek API密钥${NC}"
        log "${YELLOW}请设置DEEPSEEK_API_KEY环境变量或在脚本中直接设置${NC}"
        exit 1
    fi
    
    # 检查变更
    check_changes
    
    # 获取变更详情
    changes=$(get_changes_details)
    
    # 生成commit信息
    commit_message=$(generate_commit_message "$changes")
    
    # 执行提交
    do_commit "$commit_message"
    
    log "${BLUE}自动提交流程结束${NC}"
}

# 执行主函数
main 